import pymysql
from dotenv import load_dotenv
import os
import random
import string
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv('d:\\web\\lianghua_int\\backend\\.env')

def get_db_connection():
    """
    获取数据库连接

    返回:
        pymysql.connections.Connection: 数据库连接对象
    """
    try:
        return pymysql.connect(
            host=os.getenv('DB_HOST'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASS'),
            database=os.getenv('DB_NAME'),
            port=int(os.getenv('DB_PORT')),
            charset=os.getenv('DB_CHARSET')
        )
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise

def generate_random_string(length=10):
    """
    生成指定长度的随机字符串

    参数:
        length (int): 字符串长度

    返回:
        str: 随机字符串
    """
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

def generate_random_email():
    """
    生成随机邮箱地址

    返回:
        str: 随机邮箱地址
    """
    domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'qq.com', '163.com']
    username = generate_random_string(random.randint(5, 10)).lower()
    domain = random.choice(domains)
    return f"{username}@{domain}"

def generate_random_phone():
    """
    生成随机手机号

    返回:
        str: 随机手机号
    """
    prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                '150', '151', '152', '153', '155', '156', '157', '158', '159',
                '180', '181', '182', '183', '184', '185', '186', '187', '188', '189']
    prefix = random.choice(prefixes)
    suffix = ''.join(random.choices(string.digits, k=8))
    return f"{prefix}{suffix}"

def generate_random_ip():
    """
    生成随机IP地址

    返回:
        str: 随机IP地址
    """
    return f"{random.randint(1, 255)}.{random.randint(0, 255)}.{random.randint(0, 255)}.{random.randint(0, 255)}"

def execute_query(cursor, query, params=None, commit_conn=None, batch_size=100, current_count=0):
    """
    执行SQL查询并可选择性地提交事务

    参数:
        cursor: 数据库游标
        query: SQL查询语句
        params: 查询参数
        commit_conn: 数据库连接对象，如果提供则可能提交事务
        batch_size: 批处理大小，每处理这么多条记录提交一次
        current_count: 当前已处理的记录数

    返回:
        int: 更新后的记录数
    """
    try:
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)

        # 如果提供了连接对象且达到批处理大小，则提交事务
        if commit_conn and (current_count + 1) % batch_size == 0:
            commit_conn.commit()

        return current_count + 1
    except Exception as e:
        logger.error(f"执行查询失败: {e}")
        logger.error(f"查询: {query}")
        logger.error(f"参数: {params}")
        raise

def create_users(conn):
    """
    生成用户信息

    按照说明文档要求创建用户层级关系，包括主要用户和下级用户

    参数:
        conn (pymysql.connections.Connection): 数据库连接对象
    """
    logger.info("1. 开始生成用户信息...")
    cursor = conn.cursor()

    # 固定密码
    password = "$2y$10$k6MnV0pr4AVMWuW8hkVCge.VOcAsntd7uoCr9Y2ChuOmtEDO3.bWS"
    # 创建时间
    created_at = "2023-01-01 00:00:00"
    # 用户ID映射表
    user_ids = {}

    # 定义主要用户创建函数
    def create_main_user(username, pid, level):
        """创建主要用户"""
        query = """
            INSERT INTO users (username, password, email, phone, status, pid, level, created_at, updated_at, reg_ip)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (username, password, generate_random_email(), generate_random_phone(),
                 1, pid, level, created_at, created_at, generate_random_ip())

        execute_query(cursor, query, params)
        user_id = cursor.lastrowid
        user_ids[username] = user_id
        return user_id

    # 定义批量创建下级用户函数
    def create_subordinate_users(parent_id, parent_name, count):
        """批量创建下级用户"""
        for i in range(1, count + 1):
            username = f"{parent_name}_{i}"
            query = """
                INSERT INTO users (username, password, email, phone, status, pid, level, created_at, updated_at, reg_ip)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = (username, password, generate_random_email(), generate_random_phone(),
                     1, parent_id, 0, created_at, created_at, generate_random_ip())

            execute_query(cursor, query, params, conn, 50, i-1)
            user_ids[username] = cursor.lastrowid

    try:
        # 创建主要用户链
        user7_id = create_main_user('user7', 0, 7)
        user6_id = create_main_user('user6', user7_id, 6)
        user5_id = create_main_user('user5', user6_id, 5)
        user4_id = create_main_user('user4', user5_id, 4)
        user3_id = create_main_user('user3', user4_id, 3)
        user2_id = create_main_user('user2', user3_id, 2)
        user1_id = create_main_user('user1', user2_id, 1)

        # 创建各级下属用户
        create_subordinate_users(user7_id, 'user7', 99)  # 创建99个用户, pid=user7的id
        create_subordinate_users(user6_id, 'user6', 49)  # 创建49个用户, pid=user6的id
        create_subordinate_users(user5_id, 'user5', 29)  # 创建29个用户, pid=user5的id
        create_subordinate_users(user4_id, 'user4', 19)  # 创建19个用户, pid=user4的id
        create_subordinate_users(user3_id, 'user3', 9)   # 创建9个用户, pid=user3的id
        create_subordinate_users(user2_id, 'user2', 4)   # 创建4个用户, pid=user2的id
        create_subordinate_users(user1_id, 'user1', 1)   # 创建1个用户, pid=user1的id

        conn.commit()
        logger.info(f"用户信息生成完成，共生成 {len(user_ids)} 个用户")
    except Exception as e:
        conn.rollback()
        logger.error(f"创建用户失败: {e}")
        raise

def create_accounts(conn):
    """
    为每个用户生成账号和子账号

    按照说明文档要求，为每个用户创建一个主账号，每个主账号创建20个子账号

    参数:
        conn (pymysql.connections.Connection): 数据库连接对象
    """
    logger.info("2. 开始生成账号和子账号...")
    cursor = conn.cursor()

    # 创建时间
    created_at = "2023-01-01 00:00:00"
    # 账号ID映射表
    account_ids = {}
    # 子账号ID映射表
    sub_account_ids = {}

    # 账号总数计数器
    account_count = 0
    # 子账号总数计数器
    sub_account_count = 0

    try:
        # 获取所有用户信息
        query = "SELECT id, username FROM users"
        execute_query(cursor, query)
        users = cursor.fetchall()

        # 用户ID映射表
        user_ids = {}
        for user in users:
            user_id = user[0]
            username = user[1]
            user_ids[username] = user_id

        # 为每个用户生成一个账号
        for username, user_id in user_ids.items():
            # 生成随机账号名
            account_name = f"account_{username}_{generate_random_string(6)}"
            # 生成随机API密钥
            apikey = generate_random_string(64)
            secretkey = generate_random_string(64)

            # 插入账号数据
            query = """
                INSERT INTO accounts (
                    user_id, account, apikey, secretkey, created_at, updated_at,
                    status, total_wallet_balance, sub_wallet_balance, wallet_balance,
                    collect_wallet, sum_income, task_status
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                user_id, account_name, apikey, secretkey, created_at, created_at,
                1, 210000, 210000, 0, 0, 0, 1
            )

            execute_query(cursor, query, params, conn, 50, account_count)
            account_count += 1

            account_id = cursor.lastrowid
            account_ids[username] = account_id

            # 为每个账号生成20个子账号
            for i in range(1, 21):
                # 生成随机子账号邮箱
                sub_email = f"sub_{i}_{generate_random_email()}"
                # 生成随机API密钥
                sub_apikey = generate_random_string(64)
                sub_secretkey = generate_random_string(64)

                # 插入子账号数据
                query = """
                    INSERT INTO account_sub (
                        user_id, account_id, email, apikey, secretkey, created_at, updated_at,
                        status, wallet_balance, collect_wallet, sum_income, task_status
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                params = (
                    user_id, account_id, sub_email, sub_apikey, sub_secretkey, created_at, created_at,
                    1, 10500, 0, 0, 1
                )

                execute_query(cursor, query, params, conn, 100, sub_account_count)
                sub_account_count += 1

                sub_account_id = cursor.lastrowid
                if username not in sub_account_ids:
                    sub_account_ids[username] = []
                sub_account_ids[username].append(sub_account_id)

        conn.commit()
        logger.info(f"账号和子账号生成完成，共生成 {account_count} 个账号和 {sub_account_count} 个子账号")
    except Exception as e:
        conn.rollback()
        logger.error(f"创建账号和子账号失败: {e}")
        raise

def create_funding_rate_logs(conn):
    """
    生成资金费率明细

    按照说明文档要求，为每个子账号生成资金费率明细，并更新相关资产数据
    优化版本：
    1. 先生成全部funding_rate_logs表数据
    2. 增加每次提交数量到5000
    3. 再根据funding_rate_logs表更新子账号和账号的余额

    参数:
        conn (pymysql.connections.Connection): 数据库连接对象
    """
    logger.info("3. 开始生成资金费率明细...")
    cursor = conn.cursor()

    try:
        # 获取funding_rate_history表的数据，限制从2023年1月1日00:00:00开始
        start_timestamp = int(datetime(2023, 1, 1, 0, 0, 0).timestamp())
        query = """
            SELECT calc_time, last_funding_rate, mark_price
            FROM funding_rate_history
            WHERE symbol_id = 1 AND calc_time >= %s
            ORDER BY calc_time ASC
        """
        execute_query(cursor, query, (start_timestamp,))
        funding_history = cursor.fetchall()

        if not funding_history:
            logger.warning("未找到资金费率历史数据，跳过生成资金费率明细")
            return

        # 批量插入的最大记录数 (增加到5000)
        batch_size = 5000

        # 用于批量插入的数据
        insert_data = []

        # 记录总数
        total_logs = 0
        # 提交次数
        commit_count = 0

        # 批量插入函数
        def batch_insert():
            nonlocal total_logs, commit_count
            if not insert_data:
                return

            # 构建批量插入SQL
            insert_sql = """
                INSERT INTO funding_rate_logs (
                    user_id, account_id, account_sub_id, coin, amount, markPrice,
                    usdt, time, tranId, created_at, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            # 执行批量插入
            cursor.executemany(insert_sql, insert_data)
            total_logs += len(insert_data)

            # 清空插入数据
            insert_data.clear()

            # 每次批量插入后提交事务
            conn.commit()
            commit_count += 1
            logger.info(f"已提交 {total_logs} 条记录，第 {commit_count} 次提交")

        # 获取用户、账号和子账号数据
        logger.info("获取用户、账号和子账号数据...")

        # 获取用户数据
        query = "SELECT id, username FROM users"
        execute_query(cursor, query)
        users = cursor.fetchall()

        user_ids = {}
        for user in users:
            user_id = user[0]
            username = user[1]
            user_ids[username] = user_id

        # 获取账号数据
        query = """
            SELECT a.id, u.username
            FROM accounts a
            JOIN users u ON a.user_id = u.id
        """
        execute_query(cursor, query)
        accounts = cursor.fetchall()

        account_ids = {}
        for account in accounts:
            account_id = account[0]
            username = account[1]
            account_ids[username] = account_id

        # 获取子账号数据
        query = """
            SELECT s.id, u.username
            FROM account_sub s
            JOIN users u ON s.user_id = u.id
        """
        execute_query(cursor, query)
        sub_accounts = cursor.fetchall()

        sub_account_ids = {}
        for sub_account in sub_accounts:
            sub_account_id = sub_account[0]
            username = sub_account[1]

            if username not in sub_account_ids:
                sub_account_ids[username] = []

            sub_account_ids[username].append(sub_account_id)

        # 处理计数器
        processed_count = 0
        # 总记录数估计
        total_estimate = len(user_ids) * len(funding_history) * 20  # 假设每个用户平均有20个子账号
        logger.info(f"预计需要处理约 {total_estimate} 条记录")

        # 第一步：生成所有funding_rate_logs数据
        logger.info("第一步：生成所有funding_rate_logs数据...")

        # 开始处理数据
        for username, user_id in user_ids.items():
            if username not in account_ids or username not in sub_account_ids:
                continue

            account_id = account_ids[username]
            sub_accounts = sub_account_ids[username]

            # 为每个子账号生成资金费率明细
            for sub_account_id in sub_accounts:
                # 为每个历史记录生成一条资金费率明细
                for history in funding_history:
                    calc_time = history[0]  # 计算时间
                    last_funding_rate = float(history[1])  # 最新资金利率
                    mark_price = float(history[2])  # 标记价格

                    # 计算资金费率 (按照说明文档计算)
                    amount = 10500 * last_funding_rate / mark_price
                    # 计算USDT价值
                    usdt = mark_price * amount

                    # 转换时间戳为日期时间
                    time_str = datetime.fromtimestamp(calc_time).strftime('%Y-%m-%d %H:%M:%S')

                    # 生成唯一交易ID (使用时间戳确保唯一性)
                    tran_id = f"FR_{sub_account_id}_{calc_time}_{generate_random_string(8)}"

                    # 添加到批量插入数据
                    insert_data.append((
                        user_id, account_id, sub_account_id, 'BTC', amount, mark_price,
                        usdt, time_str, tran_id, time_str, time_str
                    ))

                    # 处理计数器增加
                    processed_count += 1

                    # 如果达到批量插入大小，执行批量插入
                    if len(insert_data) >= batch_size:
                        batch_insert()

                    # 每处理10000条记录输出一次进度
                    if processed_count % 10000 == 0:
                        logger.info(f"已处理 {processed_count} 条记录，约 {processed_count / total_estimate * 100:.2f}%")

        # 处理剩余的插入数据
        if insert_data:
            batch_insert()

        # 不再在这里更新子账号和账号余额，已移至单独的函数
        logger.info("资金费率明细数据生成完成，下一步将更新账户余额")

        logger.info(f"资金费率明细生成完成，共生成 {total_logs} 条记录，提交 {commit_count} 次")
    except Exception as e:
        conn.rollback()
        logger.error(f"生成资金费率明细失败: {e}")
        raise

def update_account_balances(conn):
    """
    更新账户余额

    根据funding_rate_logs表更新子账号和账号的余额

    参数:
        conn (pymysql.connections.Connection): 数据库连接对象
    """
    logger.info("4. 开始更新账户余额...")
    cursor = conn.cursor()

    try:
        # 更新子账号余额
        logger.info("更新子账号余额...")
        query = """
            UPDATE account_sub AS a
            JOIN (
                SELECT account_sub_id, SUM(usdt) AS total_usdt
                FROM funding_rate_logs
                GROUP BY account_sub_id
            ) AS f ON a.id = f.account_sub_id
            SET a.wallet_balance = 10500 + f.total_usdt,
                a.collect_wallet = f.total_usdt,
                a.sum_income = f.total_usdt
        """
        execute_query(cursor, query)
        conn.commit()

        # 更新账号余额 (从子账号表获取数据)
        logger.info("更新账号余额...")
        query = """
            UPDATE accounts AS a
            JOIN (
                SELECT account_id, SUM(sum_income) AS total_income,
                       SUM(wallet_balance) AS total_wallet,
                       SUM(collect_wallet) AS total_collect
                FROM account_sub
                GROUP BY account_id
            ) AS s ON a.id = s.account_id
            SET a.total_wallet_balance = s.total_wallet,
                a.sub_wallet_balance = s.total_wallet,
                a.collect_wallet = s.total_collect,
                a.sum_income = s.total_income,
                a.wallet_balance = 0
        """
        execute_query(cursor, query)
        conn.commit()

        # 更新用户累积收益（从accounts表获取数据）
        logger.info("更新用户累积收益...")
        query = """
            UPDATE users AS u
            JOIN (
                SELECT user_id, SUM(sum_income) AS total_income
                FROM accounts
                GROUP BY user_id
            ) AS a ON u.id = a.user_id
            SET u.sum_income = a.total_income
        """
        execute_query(cursor, query)
        conn.commit()

        logger.info("账户余额更新完成")
    except Exception as e:
        conn.rollback()
        logger.error(f"更新账户余额失败: {e}")
        raise



def create_settlement_records(conn):
    """
    生成交收记录

    按照说明文档要求，从2023年2月1日开始，每月1号为每个账号生成交收记录
    同时更新子账号和账号的余额字段：
    - 子账号表account_sub的wallet_balance和collect_wallet减少(归集金额/子账号数量)
    - 账号表accounts的sub_wallet_balance和collect_wallet减少归集金额
    - 账号表accounts的wallet_balance增加归集金额

    优化版本：
    1. 减少重复查询，将用户和账号数据查询移到月份循环外
    2. 批量查询资金费率明细数据
    3. 使用批量更新替代单个更新
    4. 预先获取所有子账号数据
    5. 使用字典缓存中间结果

    参数:
        conn (pymysql.connections.Connection): 数据库连接对象
    """
    logger.info("5. 开始生成交收记录...")
    cursor = conn.cursor()

    try:
        # 从2023年2月1日开始，每月1号生成交收记录
        start_date = datetime(2023, 2, 1)
        end_date = datetime.now()

        # 获取用户和账号数据（移到循环外，避免重复查询）
        logger.info("获取用户和账号数据...")

        # 获取用户数据
        query = "SELECT id, username FROM users"
        execute_query(cursor, query)
        users = cursor.fetchall()

        user_ids = {}
        for user in users:
            user_id = user[0]
            username = user[1]
            user_ids[username] = user_id

        # 获取账号数据
        query = """
            SELECT a.id, a.user_id, u.username
            FROM accounts a
            JOIN users u ON a.user_id = u.id
        """
        execute_query(cursor, query)
        accounts = cursor.fetchall()

        account_data = {}  # 存储账号相关数据
        for account in accounts:
            account_id = account[0]
            user_id = account[1]
            username = account[2]
            account_data[account_id] = {
                'user_id': user_id,
                'username': username
            }

        # 获取子账号数据（预先获取所有子账号信息）
        query = """
            SELECT account_id, COUNT(id) as sub_count
            FROM account_sub
            WHERE status = 1
            GROUP BY account_id
        """
        execute_query(cursor, query)
        sub_accounts = cursor.fetchall()

        sub_account_counts = {}  # 存储每个账号的子账号数量
        for sub_account in sub_accounts:
            account_id = sub_account[0]
            sub_count = int(sub_account[1])
            sub_account_counts[account_id] = sub_count

        # 生成每月的交收记录
        current_date = start_date
        total_records = 0
        batch_size = 100  # 增加批处理大小

        # 准备批量插入的数据
        settlement_records = []
        account_updates = []
        sub_account_updates = []

        while current_date <= end_date:
            # 当前月份的第一天
            first_day = current_date.replace(day=1)
            # 上个月的第一天
            prev_month_first_day = (first_day - timedelta(days=1)).replace(day=1)
            # 上个月的最后一天
            prev_month_last_day = first_day - timedelta(days=1)

            # 上个月的开始和结束时间
            prev_month_start = prev_month_first_day.strftime('%Y-%m-%d 00:00:00')
            prev_month_end = prev_month_last_day.strftime('%Y-%m-%d 23:59:59')

            # 当前交收记录的时间
            settlement_time = first_day.strftime('%Y-%m-%d 00:00:00')
            # 交收时间（结算时间+1天）
            settlement_complete_time = (first_day + timedelta(days=1)).strftime('%Y-%m-%d 00:00:00')
            # 最晚交收时间（结算时间+10天）
            last_time = (first_day + timedelta(days=10)).strftime('%Y-%m-%d 00:00:00')

            logger.info(f"处理 {first_day.strftime('%Y-%m')} 月的交收记录...")

            # 批量获取上个月所有账号的资金费率明细总和
            query = """
                SELECT account_id, SUM(usdt) as total_profit
                FROM funding_rate_logs
                WHERE time BETWEEN %s AND %s
                GROUP BY account_id
                HAVING SUM(usdt) > 0
            """
            params = (prev_month_start, prev_month_end)
            execute_query(cursor, query, params)

            monthly_profits = cursor.fetchall()

            # 处理每个有收益的账号
            for profit_data in monthly_profits:
                account_id = profit_data[0]
                profit = float(profit_data[1]) if profit_data[1] else 0

                # 如果没有收益或账号数据不存在，跳过
                if profit <= 0 or account_id not in account_data:
                    continue

                user_id = account_data[account_id]['user_id']

                # 交收金额为归集金额的50%
                amount = profit * 0.5

                # 添加到交收记录批量插入列表
                settlement_records.append((
                    user_id, account_id, settlement_time, settlement_time, profit,
                    amount, last_time, 50, 1, settlement_complete_time
                ))

                # 添加到账号更新列表
                account_updates.append((profit, profit, profit, account_id))

                # 处理子账号余额更新
                sub_count = sub_account_counts.get(account_id, 0)
                if sub_count > 0:
                    # 计算每个子账号需要减少的金额 (归集金额/子账号数量)
                    per_sub_amount = profit / sub_count
                    sub_account_updates.append((per_sub_amount, per_sub_amount, account_id))

                # 每达到批处理大小就执行一次批量操作
                if len(settlement_records) >= batch_size:
                    # 批量插入交收记录
                    batch_insert_settlement_records(cursor, settlement_records, conn)
                    total_records += len(settlement_records)
                    settlement_records = []

                    # 批量更新账号余额
                    batch_update_accounts(cursor, account_updates, conn)
                    account_updates = []

                    # 批量更新子账号余额
                    batch_update_sub_accounts(cursor, sub_account_updates, conn)
                    sub_account_updates = []

            # 移动到下一个月 (使用更可靠的方法计算下个月)
            current_date = (current_date.replace(day=28) + timedelta(days=4)).replace(day=1)

        # 处理剩余的批量操作
        if settlement_records:
            batch_insert_settlement_records(cursor, settlement_records, conn)
            total_records += len(settlement_records)

        if account_updates:
            batch_update_accounts(cursor, account_updates, conn)

        if sub_account_updates:
            batch_update_sub_accounts(cursor, sub_account_updates, conn)

        conn.commit()
        logger.info(f"交收记录生成完成，共生成 {total_records} 条记录")
    except Exception as e:
        conn.rollback()
        logger.error(f"生成交收记录失败: {e}")
        raise

def batch_insert_settlement_records(cursor, records, conn):
    """
    批量插入交收记录

    参数:
        cursor: 数据库游标
        records: 交收记录列表
        conn: 数据库连接
    """
    if not records:
        return

    query = """
        INSERT INTO settlement_record (
            user_id, account_id, created_at, updated_at, profit,
            amount, last_time, ratio, status, settlement_time
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """

    cursor.executemany(query, records)
    conn.commit()

def batch_update_accounts(cursor, updates, conn):
    """
    批量更新账号余额

    参数:
        cursor: 数据库游标
        updates: 更新数据列表 [(profit, profit, profit, account_id), ...]
        conn: 数据库连接
    """
    if not updates:
        return

    # 使用CASE语句构建批量更新
    account_ids = [update[3] for update in updates]
    profit_map = {update[3]: update[0] for update in updates}

    placeholders = ", ".join(["%s"] * len(account_ids))

    query = f"""
        UPDATE accounts
        SET
            wallet_balance = CASE id
                {" ".join([f"WHEN %s THEN wallet_balance + %s" for _ in account_ids])}
                ELSE wallet_balance
            END,
            sub_wallet_balance = CASE id
                {" ".join([f"WHEN %s THEN sub_wallet_balance - %s" for _ in account_ids])}
                ELSE sub_wallet_balance
            END,
            collect_wallet = CASE id
                {" ".join([f"WHEN %s THEN collect_wallet - %s" for _ in account_ids])}
                ELSE collect_wallet
            END
        WHERE id IN ({placeholders})
    """

    # 构建参数列表
    params = []
    for account_id in account_ids:
        profit = profit_map[account_id]
        params.append(account_id)
        params.append(profit)

    for account_id in account_ids:
        profit = profit_map[account_id]
        params.append(account_id)
        params.append(profit)

    for account_id in account_ids:
        profit = profit_map[account_id]
        params.append(account_id)
        params.append(profit)

    params.extend(account_ids)

    cursor.execute(query, params)
    conn.commit()

def batch_update_sub_accounts(cursor, updates, conn):
    """
    批量更新子账号余额

    参数:
        cursor: 数据库游标
        updates: 更新数据列表 [(per_sub_amount, per_sub_amount, account_id), ...]
        conn: 数据库连接
    """
    if not updates:
        return

    # 对于子账号，由于每个账号的子账号数量不同，我们需要分组处理
    for per_sub_amount, _, account_id in updates:
        query = """
            UPDATE account_sub
            SET wallet_balance = wallet_balance - %s,
                collect_wallet = collect_wallet - %s
            WHERE account_id = %s AND status = 1
        """
        params = (per_sub_amount, per_sub_amount, account_id)
        cursor.execute(query, params)

    conn.commit()

def create_agent_commission_and_withdrawal_records(conn):
    """
    生成代理佣金记录和提现记录

    根据用户代理层级关系和分成比例，计算代理佣金。
    从用户的下级用户和下级的用户的下级用户递归下去的所有用户的交收记录中
    计算代理佣金，生成时间为交收记录表settlement_time的时间，并递增1秒以体现顺序。

    当用户余额更新后，如果balance字段大于1000，立即生成提现记录，
    提现记录的时间比对应的佣金记录晚1秒。

    参数:
        conn (pymysql.connections.Connection): 数据库连接对象
    """
    logger.info("6. 开始生成代理佣金记录和提现记录...")
    cursor = conn.cursor()

    try:
        # 获取代理等级配置
        query = """
            SELECT level, radio
            FROM agent_level_config
            ORDER BY level ASC
        """
        execute_query(cursor, query)
        level_configs = cursor.fetchall()

        # 构建等级配置字典
        level_radio_map = {}
        for config in level_configs:
            level_radio_map[str(config[0])] = float(config[1])

        logger.info(f"代理等级配置: {level_radio_map}")

        # 获取所有用户信息，构建用户关系树
        query = """
            SELECT id, pid, level, username, balance
            FROM users
            ORDER BY id ASC
        """
        execute_query(cursor, query)
        users = cursor.fetchall()

        # 用户ID映射表
        user_map = {}
        # 用户下级映射表 (优化：直接构建层级关系)
        user_relation_map = {}

        # 第一步：构建用户映射
        for user in users:
            user_id = user[0]
            pid = user[1]
            level = user[2]
            username = user[3]
            balance = float(user[4]) if user[4] else 0.0

            user_map[user_id] = {
                'id': user_id,
                'pid': pid,
                'level': level,
                'username': username,
                'balance': balance,
                'last_commission_time': None  # 添加最后佣金记录时间字段，初始为None
            }

        # 第二步：预计算所有用户的层级关系 (避免重复递归计算)
        for user_id in user_map:
            user_relation_map[user_id] = {}
            current_id = user_id
            level = 1

            while True:
                parent_id = user_map.get(current_id, {}).get('pid', 0)
                if parent_id == 0:
                    break

                user_relation_map[user_id][parent_id] = level
                current_id = parent_id
                level += 1

        # 获取所有交收记录
        query = """
            SELECT id, user_id, profit, settlement_time
            FROM settlement_record
            WHERE status = 1
            ORDER BY settlement_time ASC
        """
        execute_query(cursor, query)
        settlement_records = cursor.fetchall()

        # 构建用户交收记录映射表
        user_settlement_map = {}
        for record in settlement_records:
            record_id = record[0]
            user_id = record[1]
            profit = float(record[2])
            settlement_time = record[3]

            if user_id not in user_settlement_map:
                user_settlement_map[user_id] = []

            user_settlement_map[user_id].append({
                'id': record_id,
                'profit': profit,
                'settlement_time': settlement_time
            })

        total_commissions = 0
        total_withdrawals = 0
        batch_size = 100

        # 计算代理佣金函数
        def calculate_commission_ratio(agent_level, relation_level):
            """
            计算代理佣金比例

            参数:
                agent_level: 代理用户等级
                relation_level: 与下级用户的层级关系

            返回:
                float: 佣金比例
            """
            if relation_level <= 0 or relation_level > agent_level:
                return 0

            if relation_level == 1:
                # 对于直接下级，分成比例为level=0和level=1的radio之和
                ratio = level_radio_map.get('0', 0)
                if agent_level >= 1:
                    ratio += level_radio_map.get('1', 0)
                return ratio
            else:
                # 对于间接下级，分成比例为对应level的radio
                level_key = str(relation_level)
                if int(level_key) <= agent_level:
                    return level_radio_map.get(level_key, 0)
            return 0

        # 生成提现记录函数
        def create_withdrawal_record(agent_id, balance, withdrawal_time):
            """
            为代理用户生成提现记录

            参数:
                agent_id: 代理用户ID
                balance: 当前余额
                withdrawal_time: 提现时间（比佣金记录时间晚1秒）

            返回:
                int: 是否生成了提现记录 (1:是, 0:否)
            """
            nonlocal total_withdrawals

            # 如果余额不足1000，不生成提现记录
            if balance <= 1000:
                return 0

            # 提现金额固定为1000
            withdrawal_amount = 1000.0
            # 手续费固定为1
            fee = 1.0
            # 到账金额
            amount_received = withdrawal_amount - fee
            # 剩余余额
            remaining_balance = balance - withdrawal_amount

            # 生成随机提现地址
            address = f"T{generate_random_string(33)}"

            # 插入提现记录
            query = """
                INSERT INTO agent_withdrawal_records (
                    agent_id, network, address, original_balance, amount,
                    fee, amount_received, remaining_balance, status,
                    created_at, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                agent_id, 'Tron(TRC20)', address, balance, withdrawal_amount,
                fee, amount_received, remaining_balance, 1,
                withdrawal_time, withdrawal_time
            )

            total_withdrawals = execute_query(cursor, query, params, conn, 50, total_withdrawals)

            # 更新用户余额
            query = """
                UPDATE users
                SET balance = balance - %s
                WHERE id = %s
            """
            params = (withdrawal_amount, agent_id)
            execute_query(cursor, query, params)

            # 更新内存中的用户余额
            user_map[agent_id]['balance'] = remaining_balance

            return 1

        # 收集所有需要处理的交收记录和代理关系
        all_commission_tasks = []

        # 为每个用户收集代理佣金任务
        for agent_id, agent_info in user_map.items():
            agent_level = agent_info['level']

            # 如果代理等级为0，跳过
            if agent_level == 0:
                continue

            # 遍历所有交收记录
            for user_id, settlements in user_settlement_map.items():
                # 获取用户与代理之间的层级关系 (使用预计算的关系)
                level_relation = user_relation_map.get(user_id, {}).get(agent_id, -1)

                # 如果不是其下级，或者层级关系超过了代理等级，跳过
                if level_relation <= 0 or level_relation > agent_level:
                    continue

                # 计算分成比例
                commission_ratio = calculate_commission_ratio(agent_level, level_relation)

                # 如果分成比例为0，跳过
                if commission_ratio <= 0:
                    continue

                # 为每条交收记录创建佣金任务
                for settlement in settlements:
                    record_id = settlement['id']
                    profit = settlement['profit']
                    settlement_time = settlement['settlement_time']

                    # 计算佣金金额
                    commission_amount = profit * commission_ratio / 100

                    # 如果佣金金额为0，跳过
                    if commission_amount <= 0:
                        continue

                    # 添加到任务列表
                    all_commission_tasks.append({
                        'agent_id': agent_id,
                        'user_id': user_id,
                        'record_id': record_id,
                        'profit': profit,
                        'commission_ratio': commission_ratio,
                        'commission_amount': commission_amount,
                        'base_time': settlement_time
                    })

        # 按基础时间排序所有任务
        all_commission_tasks.sort(key=lambda x: x['base_time'])

        # 处理所有佣金任务
        for task in all_commission_tasks:
            agent_id = task['agent_id']
            user_id = task['user_id']
            record_id = task['record_id']
            profit = task['profit']
            commission_ratio = task['commission_ratio']
            commission_amount = task['commission_amount']
            base_time = task['base_time']

            # 获取当前用户余额
            original_balance = user_map[agent_id]['balance']

            # 计算佣金后的余额
            remaining_balance = original_balance + commission_amount

            # 确定佣金记录时间 - 如果是该代理的第一条记录，使用基础时间，否则在上一条记录时间基础上加1秒
            if user_map[agent_id]['last_commission_time'] is None:
                commission_time = base_time
            else:
                # 获取上一次佣金时间并加1秒
                last_time_str = user_map[agent_id]['last_commission_time']
                # 如果last_time_str已经是datetime对象，直接使用；否则解析字符串
                if isinstance(last_time_str, datetime):
                    last_time = last_time_str
                else:
                    last_time = datetime.strptime(last_time_str, '%Y-%m-%d %H:%M:%S')
                commission_time = (last_time + timedelta(seconds=1)).strftime('%Y-%m-%d %H:%M:%S')

            # 更新代理的最后佣金记录时间
            user_map[agent_id]['last_commission_time'] = commission_time

            # 插入代理佣金记录
            query = """
                INSERT INTO agent_commission_records (
                    agent_id, user_id, revenue_amount, agent_ratio,
                    amount, original_balance, remaining_balance, status, date,
                    created_at, updated_at, settlement_record_id
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                agent_id, user_id, profit, commission_ratio,
                commission_amount, original_balance, remaining_balance, 1, commission_time,
                commission_time, commission_time, record_id
            )

            total_commissions = execute_query(cursor, query, params, conn, batch_size, total_commissions)

            # 更新代理用户余额
            query = """
                UPDATE users
                SET balance = balance + %s
                WHERE id = %s
            """
            params = (commission_amount, agent_id)
            execute_query(cursor, query, params)

            # 更新内存中的用户余额
            user_map[agent_id]['balance'] = remaining_balance

            # 检查余额是否超过1000，如果是则生成提现记录
            if remaining_balance > 1000:
                # 生成提现记录时间比佣金记录晚1秒
                # 如果commission_time是字符串，先解析为datetime对象
                if isinstance(commission_time, str):
                    commission_datetime = datetime.strptime(commission_time, '%Y-%m-%d %H:%M:%S')
                else:
                    commission_datetime = commission_time
                withdrawal_time = commission_datetime + timedelta(seconds=1)
                withdrawal_time_str = withdrawal_time.strftime('%Y-%m-%d %H:%M:%S')
                # 生成提现记录，余额会在create_withdrawal_record函数中更新
                create_withdrawal_record(agent_id, remaining_balance, withdrawal_time_str)
                # 注意：此时user_map[agent_id]['balance']已经在create_withdrawal_record中更新为提现后的余额

        conn.commit()
        logger.info(f"代理佣金记录生成完成，共生成 {total_commissions} 条佣金记录和 {total_withdrawals} 条提现记录")
        return total_commissions, total_withdrawals
    except Exception as e:
        conn.rollback()
        logger.error(f"生成代理佣金和提现记录失败: {e}")
        raise

def main():
    """
    主函数，按顺序执行所有数据生成步骤
    """
    logger.info('开始生成模拟数据...')
    start_time = datetime.now()

    # 获取数据库连接
    conn = None
    try:
        conn = get_db_connection()

        # # 1. 生成用户信息
        # create_users(conn)

        # # 2. 生成账号和子账号
        # create_accounts(conn)

        # # 3. 生成资金费率明细
        # create_funding_rate_logs(conn)

        # # 4. 更新账户余额
        # update_account_balances(conn)

        # # 5. 生成交收记录 (优化版本，减少重复查询)
        # create_settlement_records(conn)

        # 6. 生成代理佣金记录和提现记录
        create_agent_commission_and_withdrawal_records(conn)

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()6
        logger.info(f'模拟数据生成完成！总耗时: {duration:.2f} 秒')

    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"生成模拟数据时出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    main()