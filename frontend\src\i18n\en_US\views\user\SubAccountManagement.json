{"SubAccountManagement": {"title": "Sub-account Management", "subtitle": "Manage your sub-accounts", "selectAccount": "Select Account", "all": "All", "parentAccount": "Parent Account", "subAccount": "Sub-account", "accountAssets": "Account <PERSON>", "totalProfit": "Total Profit", "uncollectedFunds": "Uncollected Funds", "task_status": "Task Status", "status": "Status", "completionTime": "Completion Time", "operation": "Operation", "editApiKey": "Edit API Key", "addApiKey": "Add API Key", "profitDetails": "Profit Details", "noData": "No sub-account data available", "loading": "Loading...", "apikey": "API Key", "secretkey": "Secret Key", "agreeTerms": "I agree to the ", "apiTerms": "API Terms of Use", "cancel": "Cancel", "save": "Save", "agreeTermsError": "Please agree to the API Terms of Use", "apikeyUpdated": "API Key has been updated", "apikeyAdded": "API Key has been added", "saveApiKeyError": "Failed to save API Key", "apiTermsContent": "Using API keys requires compliance with relevant terms and conditions, ensuring the security of API keys.", "error": "Error", "success": "Success", "getSubAccountError": "Failed to get sub-account details", "status_inactive": "Inactive", "status_enabled": "Enabled", "status_expired": "Expired", "status_disabled": "Disabled", "taskStatus_notRunning": "Not Running", "taskStatus_completed": "Completed", "taskStatus_running": "Running", "statistics": "Sub-account Statistics", "subAccountCount": "Sub-account Count", "totalAssets": "Total Assets", "apikeyError": "Please enter API Key", "secretkeyError": "Please enter Secret Key"}}