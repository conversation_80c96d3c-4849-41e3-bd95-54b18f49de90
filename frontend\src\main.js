import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import i18n from './i18n'

// 导入全局样式
import './styles/global.css'

// 导入Font Awesome CSS（为简便起见使用CDN）
import '@fortawesome/fontawesome-free/css/all.min.css'


// 现在使用真实API而非模拟数据

// 创建Vue应用
const app = createApp(App)

// 使用插件
app.use(router)
app.use(store)
app.use(i18n)

// 全局错误处理器
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err)
  console.error('错误信息:', info)
  
  // 在生产环境中记录到分析或错误跟踪服务
  if (import.meta.env.PROD) {
    // 示例：发送到错误跟踪服务
    // errorTrackingService.captureException(err)
  }
  if (import.meta.env.DEBUG){
    // 显示用户友好的错误通知
    store.dispatch('showNotification', {
      type: 'error',
      title: err,
      message: info
    })
  }
}

// 挂载应用
app.mount('#app')
