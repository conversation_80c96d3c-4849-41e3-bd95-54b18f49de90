.table-wrapper {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 1.5rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  background-color: var(--bg-card);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--bg-card);
}

.data-table th,
.data-table td {
  padding: 12px 16px;
  text-align: center;
  border-bottom: 1px solid var(--border-color);
}

.data-table th {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  white-space: nowrap;
}

.data-table tr:hover {
  background-color: var(--bg-secondary);
}

.data-table .no-data {
  text-align: center;
  padding: 40px 0;
}

.no-data-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
}

.no-data-content i {
  font-size: 24px;
  margin-bottom: 8px;
}

.no-data-content p {
  margin: 0;
}

/* 树状表格样式 */
.cell-content {
  align-items: center;
}

.tree-cell {
  display: flex;
  align-items: center;
}

.tree-indent {
  display: flex;
  align-items: center;
}

.tree-expand-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  cursor: pointer;
  margin-right: 8px;
  color: var(--text-secondary);
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.tree-expand-icon:hover {
  color: var(--primary);
  background-color: rgba(0, 0, 0, 0.1);
}

.tree-expand-icon.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.tree-expand-icon.disabled:hover {
  color: var(--text-secondary);
  background-color: rgba(0, 0, 0, 0.05);
}

.tree-placeholder {
  width: 24px;
  margin-right: 8px;
  display: inline-block;
}

/* 表头内容样式 */
.th-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

/* 列信息图标样式 */
.column-info-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: help;
  color: var(--text-tertiary);
  font-size: 14px;
  transition: color 0.2s;
}

.column-info-icon:hover {
  color: var(--primary-color);
}

/* Tooltip样式 */
.column-tooltip {
  position: absolute;
  z-index: var(--z-index-tooltip);
  background-color: var(--bg-tooltip);
  color: var(--text-light);
  padding: 8px 12px;
  border-radius: var(--border-radius-md);
  font-size: 14px;
  max-width: 300px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  line-height: 1.5;
  pointer-events: var(--tooltip-pointer-events, none); /* 默认不接收鼠标事件，锁定时可接收 */
}

/* 添加小三角形指示器 */
.column-tooltip::before {
  content: '';
  position: absolute;
  top: -5px;
  left: var(--arrow-left, 50%); /* 使用CSS变量控制箭头位置，默认居中 */
  transform: translateX(-50%);
  border-width: 0 5px 5px;
  border-style: solid;
  border-color: transparent transparent var(--bg-tooltip) transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-wrapper {
    border-radius: 0;
    box-shadow: none;
  }

  .data-table th,
  .data-table td {
    padding: 8px 12px;
  }
}
