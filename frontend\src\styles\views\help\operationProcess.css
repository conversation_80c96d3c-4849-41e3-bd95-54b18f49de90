.operation-process-page {
  padding: var(--spacing-xl) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.page-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 800px;
  margin: 0 auto;
}

.process-content {
  margin-top: var(--spacing-xl);
}

.process-overview {
  margin-bottom: var(--spacing-xxl);
}

.section-title {
  font-size: 1.75rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  position: relative;
  padding-bottom: var(--spacing-sm);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  border-radius: 2px;
}

.process-steps {
  margin-top: var(--spacing-lg);
}

.process-step {
  display: flex;
  margin-bottom: var(--spacing-lg);
  position: relative;
}

.process-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 50px;
  left: 25px;
  width: 2px;
  height: calc(100% - 30px);
  background-color: var(--border-color);
}

.step-number {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-light) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  font-weight: var(--font-weight-bold);
  font-size: 1.25rem;
  margin-right: var(--spacing-md);
  flex-shrink: 0;
  position: relative;
  z-index: 1;
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.step-text {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.5;
}

.process-section {
  margin-bottom: var(--spacing-xxl);
}

.detail-step {
  margin-bottom: var(--spacing-xl);
}

.detail-title {
  font-size: 1.5rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  display: flex;
  align-items: center;
}

.detail-number {
  display: inline-block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-light) 100%);
  color: var(--text-light);
  font-weight: var(--font-weight-bold);
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}

.detail-content {
  display: flex;
  gap: var(--spacing-lg);
}

.detail-text {
  flex: 1;
}

.detail-text p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.detail-image {
  flex: 1;
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
}

.detail-image img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius-md);
}

.detail-tips {
  display: flex;
  align-items: flex-start;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.tips-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  color: var(--text-light);
  font-size: 1rem;
  flex-shrink: 0;
}

.tips-text {
  flex: 1;
  font-size: 0.95rem;
  line-height: 1.5;
  color: var(--text-secondary);
}

.faq-list {
  margin-top: var(--spacing-lg);
}

.faq-item {
  margin-bottom: var(--spacing-md);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  background-color: var(--bg-card);
  box-shadow: var(--box-shadow);
}

.faq-question {
  padding: var(--spacing-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.faq-answer {
  padding: 0 var(--spacing-md) var(--spacing-md);
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 1rem;
}

.process-cta {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  margin-top: var(--spacing-xxl);
}

.cta-title {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.cta-text {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 800px;
  margin: 0 auto var(--spacing-lg);
}

.cta-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

@media (max-width: 991px) {
  .detail-content {
    flex-direction: column;
  }
  
  .detail-image {
    margin-top: var(--spacing-md);
  }
}

@media (max-width: 767px) {
  .page-title {
    font-size: 2rem;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .detail-title {
    font-size: 1.25rem;
  }
  
  .cta-title {
    font-size: 1.5rem;
  }
}
