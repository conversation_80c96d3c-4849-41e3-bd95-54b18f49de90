<template>
  <div class="not-found-page">
    <div class="not-found-container">
      <div class="not-found-content">
        <div class="error-code">404</div>
        <h1 class="error-title">{{ $t('NotFound.title') }}</h1>
        <p class="error-message">{{ $t('NotFound.message') }}</p>
        <div class="error-actions">
          <router-link to="/" class="btn-home">
            <i class="fa fa-home"></i> {{ $t('NotFound.backHome') }}
          </router-link>
          <button class="btn-back" @click="goBack">
            <i class="fa fa-arrow-left"></i> {{ $t('NotFound.goBack') }}
          </button>
        </div>
      </div>
      <div class="not-found-image">
        <img src="@/assets/banner-image.png" alt="404 Illustration" />
      </div>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'

export default {
  name: 'NotFoundPage',
  setup() {
    const router = useRouter()
    
    const goBack = () => {
      router.go(-1)
    }
    
    return {
      goBack
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/notFound.css';
</style>
