﻿{
    "AgentProfit": {
        "agentProfit": "Statistik Ejen",
        "subtitle": "Lihat statistik ejen anda",
        "promoter": "Promoter",
        "allPromoters": "Semua Promoter",
        "dateRange": "Julat Masa",
        "to": "hingga",
        "status": "Status",
        "allStatus": "Semua Status",
        "profited": "Beruntung",
        "unprofited": "Tidak Beruntung",
        "search": "Cari",
        "reset": "Set Semula",
        "time": "Masa",
        "promoterAccount": "Akaun Promoter",
        "profitRatio": "Nisbah Keuntungan",
        "unprofitedAmount": "Jumlah Tidak Beruntung",
        "profitedAmount": "Jumlah Beruntung",
        "totalUnprofitedAmount": "Jumlah Keseluruhan Tidak Beruntung",
        "totalProfitedAmount": "<PERSON><PERSON><PERSON>seluruhan Beruntung",
        "totalAmount": "<PERSON><PERSON><PERSON>",
        "noData": "Tiada rekod statistik ejen",
        "userAccount": "<PERSON><PERSON><PERSON>",
        "userLevel": "<PERSON>hap Pen<PERSON>",
        "inviteCount": "Bilangan Jemputan",
        "activeUsers": "Bilangan Pengguna Berkesan",
        "userProfit": "Keuntungan Pengguna",
        "commissionRatio": "Nisbah Komisyen",
        "expectedAgentCommission": "Komisyen Ejen Dijangka",
        "agentCommission": "Komisyen Ejen",
        "unpaidProfit": "Keuntungan Belum Diselesaikan",
        "settledProfit": "Keuntungan Diselesaikan",
        "actualAgentCommission": "Komisyen Ejen Sebenar",
        "totalProfit": "Jumlah Keuntungan",
        "unpaidAccountCount": "Bilangan Akaun Belum Diselesaikan",
        "fetchError": "Gagal mendapatkan data statistik ejen",
        "fetchSubUserError": "Gagal mendapatkan data pengguna bawahan, mungkin tahap bintang ejen tidak mencukupi",
        "expandAll": "Kembangkan Semua",
        "collapseAll": "Runtuhkan Semua",
        "subUsers": "Pengguna Bawahan",
        "filterTitle": "Syarat Penapis"
    }
}
