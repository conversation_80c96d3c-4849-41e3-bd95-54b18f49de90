<?php

return [
    // 加密密钥
    'secret_key' => env('CRYPTO_SECRET', 'lianghua_encryption_key'),
    
    // 加密算法 aes-128-cbc, aes-256-cbc等
    'cipher' => 'aes-256-cbc',
    
    // 是否加密响应数据（全局开关）
    'encrypt_response' => false,
    
    // 豁免加密的路径前缀
    'exempt_prefixes' => [
        '/api/v1/public',
        '/api/v1/uploads',
    ],
    
    // 豁免加密的路径（完整匹配）
    'exempt_paths' => [
        '/api/v1/auth/captcha',
    ],
]; 