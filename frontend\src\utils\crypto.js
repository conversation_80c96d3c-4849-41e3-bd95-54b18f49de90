/**
 * 数据加密解密工具类
 */

import CryptoJS from 'crypto-js'

// 加密密钥
const SECRET_KEY = 'lianghua_encryption_key'

// 加密算法
const ALGORITHM = 'AES'

/**
 * 加密数据
 * 
 * @param {object|string} data 要加密的数据
 * @returns {string} 加密后的字符串
 */
export function encrypt(data) {
  // 如果是对象，先转成JSON字符串
  if (typeof data === 'object') {
    data = JSON.stringify(data)
  }
  
  // 生成随机IV
  const iv = CryptoJS.lib.WordArray.random(16)
  
  // 加密数据
  const encrypted = CryptoJS.AES.encrypt(data, CryptoJS.enc.Utf8.parse(SECRET_KEY), {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  
  // 将IV和加密数据合并，并进行base64编码
  const result = iv.toString(CryptoJS.enc.Base64) + encrypted.toString()
  
  return btoa(result)
}

/**
 * 解密数据
 * 
 * @param {string} data 要解密的数据
 * @returns {object|string} 解密后的数据
 */
export function decrypt(data) {
  try {
    // base64解码
    const encryptedData = atob(data)
    
    // 分离IV和加密数据
    const ivLength = 24 // IV的base64编码长度
    const iv = CryptoJS.enc.Base64.parse(encryptedData.slice(0, ivLength))
    const encrypted = encryptedData.slice(ivLength)
    
    // 解密
    const decrypted = CryptoJS.AES.decrypt(encrypted, CryptoJS.enc.Utf8.parse(SECRET_KEY), {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })
    
    // 转换为UTF-8字符串
    const decryptedStr = decrypted.toString(CryptoJS.enc.Utf8)
    
    // 尝试解析JSON
    try {
      return JSON.parse(decryptedStr)
    } catch (e) {
      return decryptedStr
    }
  } catch (e) {
    console.error('解密失败:', e)
    return data
  }
} 