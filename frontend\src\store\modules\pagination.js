export default {
  namespaced: true,
  
  state: {
    // 分页状态
    pagination: {
      current: 1,
      // 优先使用本地存储中的 limit 值，如果不存在则使用默认值 10
      limit: localStorage.getItem('pagination_limit') ? parseInt(localStorage.getItem('pagination_limit')) : 10,
      total: 0
    },
    
    // 可选的每页记录数
    limitOptions: [10, 20, 50, 100],
  },
  
  getters: {
    // 获取分页信息
    pagination: state => state.pagination,
    
    // 当前页码
    currentPage: state => state.pagination.current,
    
    // 每页记录数
    limit: state => state.pagination.limit,
    
    // 总记录数
    total: state => state.pagination.total,
    
    // 总页数
    totalPages: state => Math.max(1, Math.ceil(state.pagination.total / state.pagination.limit)),
    
    // 可选的每页记录数
    limitOptions: state => state.limitOptions,
    
    // 当前页的起始记录
    startItem: state => {
      return state.pagination.total === 0 
        ? 0 
        : (state.pagination.current - 1) * state.pagination.limit + 1
    },
    
    // 当前页的结束记录
    endItem: state => {
      return Math.min(state.pagination.current * state.pagination.limit, state.pagination.total)
    },
  },
  
  mutations: {
    // 设置分页信息
    SET_PAGINATION(state, pagination) {
      state.pagination = { ...state.pagination, ...pagination }
    },
    
    // 设置当前页码
    SET_CURRENT_PAGE(state, page) {
      state.pagination.current = page
    },
    
    // 设置每页记录数
    SET_PAGE_SIZE(state, limit) {
      state.pagination.limit = limit
      // 将新的 limit 值保存到本地存储中
      localStorage.setItem('pagination_limit', limit)
    },
    
    // 设置总记录数
    SET_TOTAL(state, total) {
      state.pagination.total = total
    },
    
    // 设置可选的每页记录数
    SET_LIMIT_OPTIONS(state, options) {
      state.limitOptions = options
    },
  },
  
  actions: {
    // 设置分页信息
    setPagination({ commit }, pagination) {
      commit('SET_PAGINATION', pagination)
    },
    
    // 设置当前页码
    setCurrentPage({ commit, state, dispatch }, page) {
      // 确保页码在有效范围内
      const totalPages = Math.max(1, Math.ceil(state.pagination.total / state.pagination.limit))
      const validPage = Math.max(1, Math.min(page, totalPages))
      
      commit('SET_CURRENT_PAGE', validPage)
      return validPage
    },
    
    // 设置每页记录数
    setlimit({ commit, dispatch }, limit) {
      commit('SET_PAGE_SIZE', limit)
      // 切换每页记录数时，重置为第一页
      dispatch('setCurrentPage', 1)
      return limit
    },
    
    // 设置总记录数
    setTotal({ commit }, total) {
      commit('SET_TOTAL', total)
    },
    
    // 设置可选的每页记录数
    setLimitOptions({ commit }, options) {
      commit('SET_LIMIT_OPTIONS', options)
    },
    
    // 处理分页变化
    handlePaginationChange({ dispatch }, { page, limit }) {
      // 如果每页记录数变化，先设置每页记录数
      if (limit) {
        dispatch('setlimit', limit)
      }
      
      // 设置当前页码
      return dispatch('setCurrentPage', page)
    },
    
    // 重置分页
    resetPagination({ commit }, limit) {
      // 优先级：1. 传入的参数 2. 本地存储的值 3. 默认值10
      let finalLimit;
      
      if (limit !== undefined) {
        // 如果提供了参数，优先使用参数值
        finalLimit = limit;
      } else {
        // 否则使用本地存储中的值，如果不存在则使用默认值 10
        finalLimit = localStorage.getItem('pagination_limit') ? parseInt(localStorage.getItem('pagination_limit')) : 10;
      }
      
      commit('SET_PAGINATION', {
        current: 1,
        limit: finalLimit,
        total: 0
      })
    },
  }
}
