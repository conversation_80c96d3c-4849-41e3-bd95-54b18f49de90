<template>
  <div class="user-page">
    <div class="page-header">
      <h1 class="page-title">{{ $t('AgentProfit.agentProfit') }}</h1>
      <div class="page-subtitle">{{ $t('AgentProfit.subtitle') }}</div>
    </div>

    <div class="agent-profit-content">
      <!-- 筛选组件 -->
      <FilterComponent
        ref="filterComponent"
        :filters="filterConfig"
        :initial-values="filterValues"
        :auto-search="true"
        @search="handleFilterSearch"
        @reset="handleFilterReset"
      />

      <!-- 表格视图 -->
      <Table
        :columns="tableColumns"
        :data="profits"
        :no-data-text="$t('AgentProfit.noData')"
        :is-tree="true"
        :default-expanded-ids="expandedRowIds"
        @expand-change="handleExpandChange"
      >
        <!-- 用户账户列 -->
        <template #userAccount="{ item }">
          {{ item.userAccount }}
        </template>

        <!-- 用户层级列 -->
        <template #userLevel="{ item }">
          {{ item.userLevel }}
        </template>

        <!-- 邀请人数列 -->
        <template #inviteCount="{ item }">
          {{ item.inviteCount }}
        </template>

        <!-- 有效人数列 -->
        <template #activeUsers="{ item }">
          {{ item.activeUsers }}
        </template>

        <!-- 分成比例列 -->
        <template #commissionRatio="{ item }">
          {{ item.commissionRatio }}%
        </template>

        <!-- 用户收益列 -->
        <template #userProfit="{ item }">
          {{ item.userProfit }}
        </template>

        <!-- 未交收收益列 -->
        <template #unpaidProfit="{ item }">
          {{ item.unpaidProfit }}
        </template>

        <!-- 预计代理收益列 -->
        <template #expectedAgentCommission="{ item }">
          {{ item.expectedAgentCommission }}
        </template>

        <!-- 已交收收益列 -->
        <template #settledProfit="{ item }">
          {{ item.settledProfit }}
        </template>

        <!-- 到账代理收益列 -->
        <template #actualAgentCommission="{ item }">
          {{ item.actualAgentCommission }}
        </template>
      </Table>

      <!-- 卡片视图 -->
      <!-- Removed Cards component -->

      <!-- 分页 -->
      <Pagination
        v-if="totalProfits > 0"
        :total="totalProfits"
        :page="currentPage"
        :limit="limit"
        @change="handlePageChange"
        use-store
      />
    </div>
  </div>
</template>

<script>
import { computed, onMounted, onUnmounted, ref, reactive } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import Table from '@/components/common/Table.vue'
import FilterComponent from '@/components/common/FilterComponent.vue'
import Pagination from '@/components/common/Pagination.vue'

export default {
  name: 'AgentProfitPage',
  components: {
    Table,
    FilterComponent,
    Pagination
  },
  setup() {
    const store = useStore()
    const { t } = useI18n()

    // 筛选配置
    const filterConfig = computed(() => [
      {
        type: 'dateRange',
        id: 'profitDateRange',
        label: 'AgentProfit.dateRange',
        startKey: 'startDate',
        endKey: 'endDate'
      }
    ])

    // 获取当月的起始日期和结束日期
    const getCurrentMonthRange = () => {
      const now = new Date()
      const year = now.getFullYear()
      const month = now.getMonth()

      // 当月第一天
      const firstDay = new Date(year, month, 1)
      const firstDayStr = formatDate(firstDay)

      // 当月最后一天
      const lastDay = new Date(year, month + 1, 0)
      const lastDayStr = formatDate(lastDay)

      return { firstDayStr, lastDayStr }
    }

    // 格式化日期为YYYY-MM-DD格式
    const formatDate = (date) => {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }

    // 获取当月日期范围
    const { firstDayStr, lastDayStr } = getCurrentMonthRange()

    // 筛选值，默认设置为当月
    const filterValues = reactive({
      startDate: firstDayStr,
      endDate: lastDayStr
    })

    // 表格列定义
    const tableColumns = computed(() => [
      { key: 'userAccount', title: t('AgentProfit.userAccount') },
      { key: 'userLevel', title: t('AgentProfit.userLevel') },
      { key: 'inviteCount', title: t('AgentProfit.inviteCount'), description: '直接邀请的下级用户总数' },
      { key: 'activeUsers', title: t('AgentProfit.activeUsers'), description: '直接邀请的下级用户有效数量' },
      { key: 'commissionRatio', title: t('AgentProfit.commissionRatio'), description: '根据代理等级计算的分成比例' },
      { key: 'userProfit', title: t('AgentProfit.userProfit'), description: '用户自身产生的收益，不包含下级用户收益和代理收益' },
      { key: 'expectedAgentCommission', title: t('AgentProfit.expectedAgentCommission'), description: '预计代理收益 = 用户收益 × 分成比例' },
      { key: 'unpaidProfit', title: t('AgentProfit.unpaidProfit'), description: '尚未完成交收的收益金额）' },
      { key: 'settledProfit', title: t('AgentProfit.settledProfit'), description: '已交收收益金额' },
      { key: 'actualAgentCommission', title: t('AgentProfit.actualAgentCommission'), description: '到账代理收益 = 已交收收益 × 分成比例' }
    ])

    // 从 store 获取代理统计数据
    const profits = computed(() => store.getters['agent/agentProfit'])
    const isLoading = computed(() => store.state.loading)
    const expandedRowIds = computed(() => store.getters['agent/expandedRowIds'])

    // 分页
    const currentPage = computed(() => store.getters['pagination/pagination'].current)
    const limit = computed(() => store.getters['pagination/pagination'].limit)
    const totalProfits = computed(() => store.getters['pagination/pagination'].total)

    // 处理分页变化
    const handlePageChange = ({ page, limit: size }) => {
      if (page && size) {
        fetchAgentProfit({
          page,
          limit: size
        })
      }
    }

    // 获取代理统计数据
    const fetchAgentProfit = (params = {}) => {
      // 合并时间筛选参数
      const queryParams = {
        ...params,
        startDate: filterValues.startDate || '',
        endDate: filterValues.endDate || ''
      }

      store.dispatch('agent/fetchAgentProfit', queryParams).catch(error => {
        console.error('获取代理统计数据失败:', error)
        // 使用 showNotification 提示错误信息
        store.dispatch('showNotification', {
          type: 'error',
          title: t('common.error'),
          message: error.message || t('AgentProfit.fetchError')
        })
      })
    }

    // 处理筛选搜索
    const handleFilterSearch = (values) => {
      // 重置分页到第一页
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value || 10
      }, { root: true })

      // 更新筛选值
      Object.keys(values).forEach(key => {
        filterValues[key] = values[key]
      })

      // 获取数据
      fetchAgentProfit({
        page: 1,
        limit: limit.value || 10
      })
    }

    // 处理筛选重置
    const handleFilterReset = () => {
      // 重置分页到第一页
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value || 10
      }, { root: true })

      // 重置筛选值
      filterValues.startDate = ''
      filterValues.endDate = ''

      // 获取数据
      fetchAgentProfit({
        page: 1,
        limit: limit.value || 10
      })
    }

    // 处理展开/折叠变化
    const handleExpandChange = ({ row, expanded, expandedIds }) => {
      // 如果是展开操作，并且该行没有子数据，则加载子数据
      if (expanded && (!row.children || row.children.length === 0)) {
        // 加载子数据，设置较大的limit值以获取全部数据，并传递时间筛选参数
        store.dispatch('agent/fetchAgentProfit', {
          parentId: row.id,
          limit: 9999, // 设置一个较大的值以获取全部数据
          startDate: filterValues.startDate || '',
          endDate: filterValues.endDate || ''
        }).then(() => {
          // 加载成功后更新展开的行ID
          store.commit('agent/SET_EXPANDED_ROW_IDS', expandedIds)
        }).catch(error => {
          // 加载失败时，从展开的行ID中移除当前行ID
          const newExpandedIds = expandedIds.filter(id => id !== row.id);
          store.commit('agent/SET_EXPANDED_ROW_IDS', newExpandedIds)

          // 显示错误提示
          store.dispatch('showNotification', {
            type: 'error',
            title: t('common.error'),
            message: error.message || t('AgentProfit.fetchSubUserError')
          })
        })
      } else {
        // 如果是折叠操作或已有子数据，直接更新展开的行ID
        store.commit('agent/SET_EXPANDED_ROW_IDS', expandedIds)
      }
    }

    // 创建FilterComponent的引用
    const filterComponent = ref(null)

    onMounted(() => {
      // 重置分页状态并获取代理统计数据
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value || 10
      }, { root: true })

      // 重置展开的行ID
      store.commit('agent/SET_EXPANDED_ROW_IDS', [])

      // 从 store 获取代理统计数据，使用重置后的分页参数和当月日期范围
      fetchAgentProfit({
        page: 1,
        limit: limit.value || 10,
        startDate: filterValues.startDate,
        endDate: filterValues.endDate
      })
    })

    // 组件卸载时清空展开的行ID
    onUnmounted(() => {
      store.commit('agent/SET_EXPANDED_ROW_IDS', [])
    })

    return {
      tableColumns,
      profits,
      isLoading,
      expandedRowIds,
      currentPage,
      limit,
      totalProfits,
      filterConfig,
      filterValues,
      filterComponent,
      handlePageChange,
      handleExpandChange,
      handleFilterSearch,
      handleFilterReset
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/user/common.css';
@import '@/styles/views/user/agent/agentProfit.css';
</style>
