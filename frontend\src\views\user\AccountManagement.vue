<template>
  <div class="user-page">
    <div class="page-header">
      <h1 class="page-title">{{ $t('AccountManagement.accountManagement') }}</h1>
      <div class="page-subtitle">{{ $t('AccountManagement.subtitle') }}</div>
      <div class="page-actions">
        <button class="btn btn-primary" @click="addAccount">
          <i class="fa fa-plus"></i> {{ $t('AccountManagement.addAccount') }}
        </button>
      </div>
    </div>

    <div class="account-content">
      <div class="summary-section">
        <div class="summary-card">
          <div class="summary-title">{{ $t('AccountManagement.statistics') }}</div>
          <div class="summary-content">
            <div class="summary-item">
              <div class="summary-label">{{ $t('AccountManagement.accountCount') }}</div>
              <div class="summary-value">{{ accountStats.accountCount }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">{{ $t('AccountManagement.subAccountCount') }}</div>
              <div class="summary-value">{{ accountStats.subAccountCount }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">{{ $t('AccountManagement.totalAssets') }}</div>
              <div class="summary-value">{{ accountStats.totalAssets }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">{{ $t('AccountManagement.totalProfit') }}</div>
              <div class="summary-value" :class="{ 'summary-value-positive': Number(accountStats.totalProfit) > 0, 'summary-value-negative': Number(accountStats.totalProfit) <= 0 }">{{ accountStats.totalProfit }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">{{ $t('AccountManagement.uncollectedFunds') }}</div>
              <div class="summary-value">{{ accountStats.uncollectedFunds }}</div>
            </div>
          </div>
        </div>
      </div>
        <Table 
          :columns="tableColumns" 
          :data="accounts" 
          :no-data-text="$t('AccountManagement.noAccountData')"
          class="desktop-only"
        >
          <!-- 账号列 -->
          <template #account="{ item }">
            {{ item.account }}
          </template>
          
          <!-- 子账号数量列 -->
          <template #subAccountCount="{ item }">
            {{ item.subAccountCount }}
          </template>
          
          <!-- 运行数量列 -->
          <template #runningCount="{ item }">
            {{ item.runningCount }}
          </template>
          
          <!-- 完成数量列 -->
          <template #completedCount="{ item }">
            {{ item.completedCount }}
          </template>
          
          <!-- 总资产列 -->
          <template #total_wallet_balance="{ item }">
            {{ item.total_wallet_balance }}
          </template>
          
          <!-- 今日收益列 -->
          <template #todayProfit="{ item }">
            <span :class="{ 'positive-rate': Number(item.todayProfit) > 0, 'negative-rate': Number(item.todayProfit) <= 0 }">
              {{ item.todayProfit || 0 }}
            </span>
          </template>
          
          <!-- 总收益列 -->
          <template #sum_income="{ item }">
            <span :class="{ 'positive-rate': Number(item.sum_income) > 0, 'negative-rate': Number(item.sum_income) <= 0 }">
              {{ item.sum_income || 0 }}
            </span>
          </template>
          
          <!-- 未收集资金列 -->
          <template #collect_wallet="{ item }">
            {{ item.collect_wallet }}
          </template>
          
          <!-- 添加时间列 -->
          <template #created_at="{ item }">
            {{ item.created_at }}
          </template>
          
          <!-- 任务状态列 -->
          <template #task_status="{ item }">
            <span class="task-status" :class="getTaskStatusClass(item.task_status)">
              <span class="task-status-dot" :class="getTaskStatusDotClass(item.task_status)"></span>
              {{ getTaskStatusText(item.task_status) }}
            </span>
          </template>
          
          <!-- 状态列 -->
          <template #status="{ item }">
            <span class="status-badge" :class="getStatusClass(item.status)">
              {{ getStatusText(item.status) }}
            </span>
          </template>
          
          <!-- 操作列 -->
          <template #operation="{ item }">
            <div class="action-buttons">
              <button class="btn btn-sm btn-primary" @click="editAccount(item)">
                <i class="fa fa-edit"></i> {{ $t('AccountManagement.edit') }}
              </button>
              <button v-if="item.task_status != 0" class="btn btn-sm btn-info" @click="viewProfitDetails(item)">
                <i class="fa fa-chart-line"></i> {{ $t('AccountManagement.profitDetails') }}
              </button>
            </div>
          </template>
        </Table>

      <Cards 
        :columns="tableColumns" 
        :data="accounts" 
        :loading="isLoading" 
        :no-data-text="$t('AccountManagement.noAccountData')"
        :loading-text="$t('Table.loading')"
      >
        <!-- 标题插槽 -->
        <template #title="{ item }">
          {{ item.name }}
        </template>
        
        <!-- 任务状态插槽 -->
        <template #task_status="{ item }">
          <span class="task-status" :class="getTaskStatusClass(item.task_status)">
            <span class="task-status-dot" :class="getTaskStatusDotClass(item.task_status)"></span>
            {{ getTaskStatusText(item.task_status) }}
          </span>
        </template>
        
        <!-- 状态插槽 -->
        <template #status="{ item }">
          <span class="status-badge" :class="getStatusClass(item.status)">
            {{ getStatusText(item.status) }}
          </span>
        </template>
        
        <!-- 账号列 -->
        <template #account="{ item }">
          {{ item.account }}
        </template>
        
        <!-- 子账号数量列 -->
        <template #subAccountCount="{ item }">
          {{ item.subAccountCount }}
        </template>
        
        <!-- 运行数量列 -->
        <template #runningCount="{ item }">
          {{ item.runningCount }}
        </template>
        
        <!-- 完成数量列 -->
        <template #completedCount="{ item }">
          {{ item.completedCount }}
        </template>
        
        <!-- 总资产列 -->
        <template #total_wallet_balance="{ item }">
          {{ item.total_wallet_balance }}
        </template>
        
        <!-- 今日收益列 -->
        <template #todayProfit="{ item }">
          <span :class="{ 'positive-rate': Number(item.todayProfit) > 0, 'negative-rate': Number(item.todayProfit) <= 0 }">
            {{ item.todayProfit || 0 }}
          </span>
        </template>
        
        <!-- 总收益列 -->
        <template #sum_income="{ item }">
          <span :class="{ 'positive-rate': Number(item.sum_income) > 0, 'negative-rate': Number(item.sum_income) <= 0 }">
            {{ item.sum_income || 0 }}
          </span>
        </template>
        
        <!-- 未收集资金列 -->
        <template #collect_wallet="{ item }">
          {{ item.collect_wallet }}
        </template>
        
        <!-- 添加时间列 -->
        <template #created_at="{ item }">
          {{ item.created_at }}
        </template>
        
        <!-- 操作列 -->
        <template #operation="{ item }">
          <button class="btn btn-sm btn-primary" @click="editAccount(item)">
            <i class="fa fa-edit"></i> {{ $t('AccountManagement.edit') }}
          </button>
          <button v-if="item.task_status != 0" class="btn btn-sm btn-info" @click="viewProfitDetails(item)">
            <i class="fa fa-chart-line"></i> {{ $t('AccountManagement.profitDetails') }}
          </button>
        </template>
        
        <!-- 无数据时的操作 -->
        <template #no-data-actions>
          <button class="btn btn-primary" @click="addAccount">
            <i class="fa fa-plus"></i> {{ $t('AccountManagement.addAccount') }}
          </button>
        </template>
      </Cards>

      <Pagination v-if="totalAccounts > 0" :total="totalAccounts" :page="currentPage" :page-size="limit"
        @change="handlePageChange" />
    </div>

    <!-- 添加/编辑账号弹窗 -->
    <Modal 
      v-model="showAddAccountModal" 
      :title="editingAccount ? $t('AccountManagement.editAccount') : $t('AccountManagement.addAccount')"
      @confirm="saveAccount"
    >
      <form @submit.prevent="saveAccount">
        <div class="form-group">
          <label for="accountName">{{ $t('AccountManagement.accountRemark') }}</label>
          <input type="text" id="accountName" v-model="accountForm.account" class="form-control" required />
          <small class="form-text text-muted">{{ $t('AccountManagement.accountRemarkHint') }}</small>
        </div>
        <div class="form-group">
          <label for="apikey">API KEY</label>
          <input type="text" id="apikey" v-model="accountForm.apikey" class="form-control" required />
        </div>
        <div class="form-group">
          <label for="secretkey">{{ $t('AccountManagement.secretkey') }}</label>
          <input type="text" id="secretkey" v-model="accountForm.secretkey" class="form-control" required />
        </div>
        <div class="form-group">
          <div class="form-check">
            <input type="checkbox" id="agreeTerms" v-model="accountForm.agreeTerms" class="form-check-input" required />
            <label for="agreeTerms" class="form-check-label">
              {{ $t('AccountManagement.agreeTerms') }}<a href="#" @click.prevent="showTerms">{{ $t('AccountManagement.apiTerms') }}</a>
            </label>
          </div>
        </div>
      </form>
    </Modal>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import Pagination from '@/components/common/Pagination.vue'
import Table from '@/components/common/Table.vue'
import Cards from '@/components/common/Cards.vue'
import Modal from '@/components/common/Modal.vue'
import { focusInput, showError, showSuccess } from '@/utils/utils'

export default {
  name: 'AccountManagementPage',
  components: {
    Pagination,
    Table,
    Cards,
    Modal
  },
  setup() {
    const store = useStore()
    const { t } = useI18n()

    // 表格列定义
    const tableColumns = computed(() => [
      { key: 'account', title: t('AccountManagement.account'), align:'left' },
      { key: 'subAccountCount', title: t('AccountManagement.subAccountCount')},
      { key: 'runningCount', title: t('AccountManagement.runningCount') },
      { key: 'completedCount', title: t('AccountManagement.completedCount') },
      { key: 'total_wallet_balance', title: t('AccountManagement.totalAssets'), align:'right' },
      { key: 'todayProfit', title: t('AccountManagement.todayProfit'), align:'right' },
      { key: 'sum_income', title: t('AccountManagement.totalProfit'), align:'right' },
      { key: 'collect_wallet', title: t('AccountManagement.uncollectedFunds'), align:'right' },
      { key: 'created_at', title: t('AccountManagement.addTime') },
      { key: 'task_status', title: t('AccountManagement.task_status') },
      { key: 'status', title: t('AccountManagement.status') },
      { key: 'operation', title: t('AccountManagement.operation') }
    ])

    // 从 store 获取账号列表
    const accounts = computed(() => store.getters['account/accounts'])
    const isLoading = computed(() => store.getters['account/isLoading'])
    
    // 账号统计数据
    const accountStats = computed(() => {
      const data = store.state.account.accountStats || {}
      return {
        accountCount: data.accountCount || 0,
        subAccountCount: data.subAccountCount || 0,
        totalAssets: data.totalAssets || 0,
        totalProfit: data.totalProfit || 0,
        uncollectedFunds: data.uncollectedFunds || 0
      }
    })

    // 分页
    const currentPage = computed(() => store.getters['pagination/pagination'].current)
    const limit = computed(() => store.getters['pagination/pagination'].limit)
    const totalAccounts = computed(() => store.getters['pagination/pagination'].total)

    // 弹窗显示状态
    const showAddAccountModal = ref(false)

    // 表单数据
    const accountForm = reactive({
      account: '',
      apikey: '',
      secretkey: '',
      agreeTerms: true
    })

    // 编辑的账号
    const editingAccount = ref(null)

    // 处理分页变化
    const handlePageChange = ({ page, limit: size }) => {
      if (page && size) {
        store.dispatch('account/fetchAccounts', {
          page,
          limit: size
        })
      }
    }

    // 添加账号
    const addAccount = () => {
      resetForm() // 重置表单和editingAccount
      showAddAccountModal.value = true
    }

    // 编辑账号
    const editAccount = (account) => {
      editingAccount.value = account

      // 确保只有当属性存在时才赋值给表单
      if (account.account !== undefined) {
        accountForm.account = account.account
      }

      if (account.apikey !== undefined) {
        accountForm.apikey = account.apikey
      }

      if (account.secretkey !== undefined) {
        accountForm.secretkey = account.secretkey
      }

      accountForm.agreeTerms = true
      showAddAccountModal.value = true
    }

    // 保存账号
    const saveAccount = async () => {
      if (!accountForm.account) {
        showError(t,t('AccountManagement.accountRequired'))
        focusInput('accountName')
        return
      }
      if (!accountForm.apikey) {
        showError(t,t('AccountManagement.apikeyRequired'))
        focusInput('apikey')
        return
      }
      if (!accountForm.secretkey) {
        showError(t,t('AccountManagement.secretkeyRequired'))
        focusInput('secretkey')
        return
      }
      if (!accountForm.agreeTerms) {
        showError(t,t('AccountManagement.agreeTermsError'))
        return
      }

      try {
        if (editingAccount.value) {
          // 编辑现有账号
          const updatedAccount = {
            id: editingAccount.value.id,
            account: accountForm.account,
            apikey: accountForm.apikey,
            secretkey: accountForm.secretkey
          }

          await store.dispatch('account/updateAccount', updatedAccount)

          showSuccess(t,t('AccountManagement.accountUpdated'))
        } else {
          // 添加新账号
          const newAccount = {
            account: accountForm.account,
            apikey: accountForm.apikey,
            secretkey: accountForm.secretkey
          }

          await store.dispatch('account/addAccount', newAccount)

          showSuccess(t,t('AccountManagement.accountAdded'))
        }

        // 重置表单
        resetForm()
        showAddAccountModal.value = false
      } catch (error) {
        showError(t,error.message || t('AccountManagement.saveAccountError'))
      }
    }

    // 重置表单
    const resetForm = () => {
      accountForm.account = ''
      accountForm.apikey = ''
      accountForm.secretkey = ''
      accountForm.agreeTerms = true
      editingAccount.value = null
    }

    // 显示条款
    const showTerms = () => {
      // 这里应该显示API密钥使用条款
      console.log('Show terms')
    }

    onMounted(() => {
      // 重置分页状态并获取账号列表
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value
      }, { root: true })
      
      // 从 store 获取账号列表，使用重置后的分页参数
      store.dispatch('account/fetchAccounts', {
        page: 1,
        limit: limit.value
      })
    })

    // 获取状态样式类
    const getStatusClass = (status) => {
      // 数据库中状态: 0：未启用 1：已启用 2：已失效 3：已停用
      switch (parseInt(status)) {
        case 0:
          return 'status-inactive'
        case 1:
          return 'status-enabled'
        case 2:
          return 'status-expired'
        case 3:
          return 'status-disabled'
        default:
          return ''
      }
    }
    
    // 获取状态文本
    const getStatusText = (status) => {
      // 数据库中状态: 0：未启用 1：已启用 2：已失效 3：已停用
      switch (parseInt(status)) {
        case 0:
          return t('AccountManagement.statusInactive')
        case 1:
          return t('AccountManagement.statusEnabled')
        case 2:
          return t('AccountManagement.statusExpired')
        case 3:
          return t('AccountManagement.statusDisabled')
        default:
          return '-'
      }
    }

    // 获取任务状态样式类
    const getTaskStatusClass = (taskStatus) => {
      // 任务状态: 0：未运行 1：已完成 其它：执行中
      switch (parseInt(taskStatus)) {
        case 0:
          return 'task-status-not-running'
        case 1:
          return 'task-status-completed'
        default:
          return 'task-status-running'
      }
    }
    
    // 获取任务状态点样式类
    const getTaskStatusDotClass = (taskStatus) => {
      // 任务状态: 0：未运行 1：已完成 其它：执行中
      switch (parseInt(taskStatus)) {
        case 0:
          return 'task-status-dot-not-running'
        case 1:
          return 'task-status-dot-completed'
        default:
          return 'task-status-dot-running'
      }
    }
    
    // 获取任务状态文本
    const getTaskStatusText = (taskStatus) => {
      // 任务状态: 0：未运行 1：已完成 其它：执行中
      switch (parseInt(taskStatus)) {
        case 0:
          return t('AccountManagement.taskStatusNotRunning')
        case 1:
          return t('AccountManagement.taskStatusCompleted')
        default:
          return t('AccountManagement.taskStatusRunning')
      }
    }
    
    // 查看盈利明细
    const router = useRouter()
    const viewProfitDetails = (account) => {
      // 跳转到盈利明细页面，并传递账号ID参数
      router.push({
        name: 'ProfitDetails',
        query: {
          accountId: account.id
        }
      })
    }

    return {
      tableColumns,
      accounts,
      isLoading,
      currentPage,
      limit,
      totalAccounts,
      showAddAccountModal,
      accountForm,
      editingAccount,
      accountStats,
      getStatusClass,
      getStatusText,
      getTaskStatusClass,
      getTaskStatusDotClass,
      getTaskStatusText,
      handlePageChange,
      addAccount,
      editAccount,
      saveAccount,
      showTerms,
      viewProfitDetails
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/user/common.css';
@import '@/styles/views/user/accountManagement.css';
@import '@/styles/components/taskStatus.css';
@import '@/styles/components/summary.css';
</style>
