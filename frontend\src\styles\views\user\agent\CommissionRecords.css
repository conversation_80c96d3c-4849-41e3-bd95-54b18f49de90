.commission-records-content {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: var(--card-shadow);
}

.status-pending {
  color: var(--warning-color);
  font-weight: 500;
}

.status-completed {
  color: var(--success-color);
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .btn {
  padding: 4px 8px;
  font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .commission-records-content {
    padding: 15px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 5px;
  }
}
