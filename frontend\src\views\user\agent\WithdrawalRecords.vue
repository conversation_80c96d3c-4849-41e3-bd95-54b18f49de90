<template>
  <div class="user-page">
    <div class="page-header">
      <h1 class="page-title">{{ $t('WithdrawalRecords.title') }}</h1>
      <div class="page-subtitle">{{ $t('WithdrawalRecords.subtitle') }}</div>
      <div class="page-actions">
        <button class="btn btn-primary" @click="showWithdrawModal = true">
          <i class="fa fa-plus"></i> {{ $t('WithdrawalRecords.applyWithdrawal') }}
        </button>
      </div>
    </div>

    <div class="withdrawal-records-content">
      <!-- 筛选组件 -->
      <FilterComponent
        :filters="filterConfig"
        :initial-values="filterValues"
        @search="handleFilterSearch"
        @reset="handleFilterReset"
      />

      <Table :columns="tableColumns" :data="records" :no-data-text="$t('WithdrawalRecords.noData')"
        class="desktop-only">
        <template #network="{ item }">
          {{ item.network }}
        </template>
        <template #address="{ item }">
          {{ item.address }}
        </template>
        <template #amount="{ item }">
          {{ item.amount }}
        </template>
        <template #fee="{ item }">
          {{ item.fee }}
        </template>
        <template #amount_received="{ item }">
          {{ item.amount_received }}
        </template>
        <template #original_balance="{ item }">
          {{ item.original_balance }}
        </template>
        <template #remaining_balance="{ item }">
          {{ item.remaining_balance }}
        </template>
        <template #status="{ item }">
          <span :class="getStatusClass(item.status)">
            {{ getStatusText(item.status) }}
          </span>
        </template>
        <template #created_at="{ item }">
          {{ item.created_at }}
        </template>
        <template #operation="{ item }">
          <div class="action-buttons">
            <button class="btn btn-sm btn-info" @click="onViewClick(item)">
              <i class="fa fa-eye"></i> {{ $t('WithdrawalRecords.view') }}
            </button>
            <button v-if="item.status === 0" class="btn btn-sm btn-primary" @click="onConfirmClick(item)">
              <i class="fa fa-check"></i> {{ $t('WithdrawalRecords.confirm') }}
            </button>
            <button v-if="item.status === 0" class="btn btn-sm btn-danger" @click="handleCancelWithdrawal(item.id)">
              <i class="fa fa-times"></i> {{ $t('common.cancel') }}
            </button>
          </div>
        </template>
      </Table>

      <Cards :columns="tableColumns" :data="records" :no-data-text="$t('WithdrawalRecords.noData')">
        <template #network="{ item }">
          {{ item.network }}
        </template>
        <template #address="{ item }">
          {{ item.address }}
        </template>
        <template #amount="{ item }">
          {{ item.amount }}
        </template>
        <template #fee="{ item }">
          {{ item.fee }}
        </template>
        <template #amount_received="{ item }">
          {{ item.amount_received }}
        </template>
        <template #original_balance="{ item }">
          {{ item.original_balance }}
        </template>
        <template #remaining_balance="{ item }">
          {{ item.remaining_balance }}
        </template>
        <template #status="{ item }">
          <span :class="getStatusClass(item.status)">
            {{ getStatusText(item.status) }}
          </span>
        </template>
        <template #created_at="{ item }">
          {{ item.created_at }}
        </template>
        <template #operation="{ item }">
          <div class="action-buttons">
            <button class="btn btn-sm btn-info" @click="onViewClick(item)">
              <i class="fa fa-eye"></i> {{ $t('WithdrawalRecords.view') }}
            </button>
            <button v-if="item.status === 0" class="btn btn-sm btn-primary" @click="onConfirmClick(item)">
              <i class="fa fa-check"></i> {{ $t('WithdrawalRecords.confirm') }}
            </button>
            <button v-if="item.status === 0" class="btn btn-sm btn-danger" @click="handleCancelWithdrawal(item.id)">
              <i class="fa fa-times"></i> {{ $t('common.cancel') }}
            </button>
          </div>
        </template>
      </Cards>

      <Pagination v-if="totalRecords > 0" :total="totalRecords" :page="currentPage" :limit="limit"
        @change="handlePageChange" use-store />
    </div>

    <Modal v-model="showWithdrawModal" :title="$t('WithdrawalRecords.applyWithdrawal')" @confirm="submitWithdrawApply"
      @update:modelValue="val => { if (!val) cancelWithdrawApply() }">
      <form @submit.prevent="submitWithdrawApply">
        <div class="form-group">
          <label for="balance">{{ $t('WithdrawalRecords.currentWalletBalance') }}(USDT):</label>{{ balance }}
        </div>
        <div class="form-group">
          <label for="network">{{ $t('WithdrawalRecords.network') }}</label>
          <input type="text" id="network" v-model="withdrawForm.network" class="form-control" readonly />
        </div>
        <div class="form-group">
          <label for="address">{{ $t('WithdrawalRecords.address') }}</label>
          <input type="text" id="address" v-model="withdrawForm.address" class="form-control" required />
        </div>
        <div class="form-group">
          <label for="amount">{{ $t('WithdrawalRecords.amount') }}</label>
          <input type="number" id="amount" v-model.number="withdrawForm.amount" class="form-control" required
            step="0.01" />
        </div>
        <div class="form-group">
            <label class="form-check-label">
              {{ $t('WithdrawalRecords.withdrawalCheckNotice') }}
            </label>
        </div>
      </form>
    </Modal>

    <Modal v-model="showConfirmModal" :title="$t('WithdrawalRecords.confirmWithdrawal')" @confirm="confirmWithdrawal"
      @update:modelValue="val => { if (!val) closeConfirmModal() }">
      <div v-if="currentRecord">
        <p><strong>{{ $t('WithdrawalRecords.network') }}:</strong> {{ currentRecord.network }}</p>
        <p><strong>{{ $t('WithdrawalRecords.address') }}:</strong> {{ currentRecord.address }}</p>
      </div>
    </Modal>

    <Modal v-model="showDetailModal" :title="$t('WithdrawalRecords.detail')"
      @update:modelValue="val => { if (!val) closeDetailModal() }">
      <div v-if="currentRecord">
        <p><strong>{{ $t('WithdrawalRecords.network') }}:</strong> {{ currentRecord.network }}</p>
        <p><strong>{{ $t('WithdrawalRecords.address') }}:</strong> {{ currentRecord.address }}</p>
        <p><strong>{{ $t('WithdrawalRecords.amount') }}:</strong> {{ currentRecord.amount }}</p>
        <p><strong>{{ $t('WithdrawalRecords.fee') }}:</strong> {{ currentRecord.fee }}</p>
        <p><strong>{{ $t('WithdrawalRecords.amountReceived') }}:</strong> {{ currentRecord.amount_received }}</p>
        <p><strong>{{ $t('WithdrawalRecords.originalBalance') }}:</strong> {{ currentRecord.original_balance }}</p>
        <p><strong>{{ $t('WithdrawalRecords.remainingBalance') }}:</strong> {{ currentRecord.remaining_balance }}</p>
        <p><strong>{{ $t('WithdrawalRecords.status') }}:</strong> {{ getStatusText(currentRecord.status) }}</p>
        <p><strong>{{ $t('WithdrawalRecords.createdAt') }}:</strong> {{ currentRecord.created_at }}</p>
      </div>
    </Modal>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch, reactive } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import Table from '@/components/common/Table.vue'
import Pagination from '@/components/common/Pagination.vue'
import Cards from '@/components/common/Cards.vue'
import Modal from '@/components/common/Modal.vue'
import FilterComponent from '@/components/common/FilterComponent.vue'
import { focusInput, showError, showSuccess } from '@/utils/utils'

export default {
  name: 'WithdrawalRecordsPage',
  components: {
    Table,
    Pagination,
    Cards,
    Modal,
    FilterComponent
  },
  setup() {
    const store = useStore()
    const { t } = useI18n()

    // 筛选配置
    const filterConfig = computed(() => [
      {
        type: 'select',
        id: 'status',
        label: 'WithdrawalRecords.status',
        key: 'status',
        placeholder: t('FilterComponent.all'),
        options: [
          { value: '0', label: t('WithdrawalRecords.pending') },
          { value: '1', label: t('WithdrawalRecords.processing') },
          { value: '2', label: t('WithdrawalRecords.completed') },
          { value: '3', label: t('WithdrawalRecords.failed') },
          { value: '4', label: t('WithdrawalRecords.cancelled') }
        ]
      },
      {
        type: 'dateRange',
        id: 'withdrawalDateRange',
        label: 'FilterComponent.dateRange',
        startKey: 'startDate',
        endKey: 'endDate'
      }
    ])

    // 筛选值
    const filterValues = reactive({
      status: '',
      startDate: '',
      endDate: ''
    })

    const tableColumns = computed(() => [
      { key: 'network', title: t('WithdrawalRecords.network') },
      { key: 'address', title: t('WithdrawalRecords.address') },
      { key: 'amount', title: t('WithdrawalRecords.amount') },
      { key: 'fee', title: t('WithdrawalRecords.fee') },
      { key: 'amount_received', title: t('WithdrawalRecords.amountReceived') },
      { key: 'original_balance', title: t('WithdrawalRecords.originalBalance') },
      { key: 'remaining_balance', title: t('WithdrawalRecords.remainingBalance') },
      { key: 'status', title: t('WithdrawalRecords.status') },
      { key: 'created_at', title: t('WithdrawalRecords.createdAt') },
      { key: 'operation', title: t('WithdrawalRecords.operation') }
    ])

    const records = computed(() => store.getters['agent/withdrawalRecords'])
    const currentPage = computed(() => store.getters['pagination/pagination'].current)
    const limit = computed(() => store.getters['pagination/pagination'].limit)
    const totalRecords = computed(() => store.getters['pagination/pagination'].total)

    // 新增确认提现弹窗显示状态
    const showConfirmModal = ref(false)
    // 新增查看详情弹窗显示状态
    const showDetailModal = ref(false)
    // 新增当前选中提现记录
    const currentRecord = ref(null)

    // 获取状态文本
    const getStatusText = (status) => {
      switch (parseInt(status)) {
        case 0:
          return t('WithdrawalRecords.pending')
        case 1:
          return t('WithdrawalRecords.processing')
        case 2:
          return t('WithdrawalRecords.completed')
        case 3:
          return t('WithdrawalRecords.failed')
        case 4:
          return t('WithdrawalRecords.cancelled') // 新增已取消状态
        default:
          return '-'
      }
    }

    // 获取状态样式类
    const getStatusClass = (status) => {
      switch (parseInt(status)) {
        case 0:
          return 'status-pending'
        case 1:
          return 'status-processing'
        case 2:
          return 'status-completed'
        case 3:
          return 'status-failed'
        case 4:
          return 'status-cancelled' // 新增已取消状态样式
        default:
          return ''
      }
    }

    const handlePageChange = ({ page, limit: size }) => {
      if (page && size) {
        fetchWithdrawalRecords({ page, limit: size })
      }
    }

    const fetchWithdrawalRecords = (params = {}) => {
      // 合并筛选参数
      const queryParams = {
        ...params,
        status: filterValues.status || '',
        startDate: filterValues.startDate || '',
        endDate: filterValues.endDate || ''
      }

      store.dispatch('agent/fetchWithdrawalRecords', queryParams).catch(error => {
        console.error('获取提现记录失败:', error)
        showError(t, error.message || '获取提现记录失败')
      })
    }

    // 处理筛选搜索
    const handleFilterSearch = (values) => {
      // 重置分页到第一页
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value || 10
      }, { root: true })

      // 更新筛选值
      Object.keys(values).forEach(key => {
        filterValues[key] = values[key]
      })

      // 获取数据
      fetchWithdrawalRecords({
        page: 1,
        limit: limit.value || 10
      })
    }

    // 处理筛选重置
    const handleFilterReset = () => {
      // 重置分页到第一页
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value || 10
      }, { root: true })

      // 重置筛选值
      filterValues.status = ''
      filterValues.startDate = ''
      filterValues.endDate = ''

      // 获取数据
      fetchWithdrawalRecords({
        page: 1,
        limit: limit.value || 10
      })
    }

    // 申请提现弹窗显示状态
    const showWithdrawModal = ref(false)

    // 余额
    const balance = ref(null)

    // 申请提现表单数据
    const withdrawForm = ref({
      network: 'Tron(TRC20)',
      address: '',
      amount: null
    })

    // 重置提现表单
    const resetWithdrawForm = () => {
      withdrawForm.value = {
        network: 'Tron(TRC20)',
        address: '',
        amount: null
      }
      balance.value = null
    }

    // 取消提现申请
    const cancelWithdrawApply = () => {
      resetWithdrawForm()
      showWithdrawModal.value = false
    }

    // 监听弹窗显示，弹出时调用fetchProfile获取余额
    watch(showWithdrawModal, async (newVal) => {
      if (newVal) {
        try {
          const profileData = await store.dispatch('user/fetchProfile')
          if (profileData && profileData.data && profileData.data.balance) {
            balance.value = profileData.data.balance
          }
        } catch (error) {
          balance.value = null
          console.error('获取余额失败:', error)
        }
      }
    })

    // 提交提现申请
    const submitWithdrawApply = async () => {
      // 验证地址是否为空
      if (!withdrawForm.value.address) {
        showError(t, t('WithdrawalRecords.addressRequired'))
        focusInput('address')
        return
      }

      // 验证金额是否有效
      if (!withdrawForm.value.amount || withdrawForm.value.amount < 1000) {
        showError(t, t('WithdrawalRecords.amountInvalid'))
        focusInput('amount')
        return
      }

      // 验证金额是否超过余额
      if (withdrawForm.value.amount > balance.value) {
        showError(t, t('WithdrawalRecords.amountExceedsBalance'))
        focusInput('amount')
        return
      }

      try {
        await store.dispatch('agent/withdrawApply', withdrawForm.value)
        // 提交成功，关闭弹窗，重置表单
        cancelWithdrawApply()
        // 刷新提现记录列表
        fetchWithdrawalRecords({
          page: currentPage.value,
          limit: limit.value
        })
        showSuccess(t, t('WithdrawalRecords.withdrawalSubmitted'))
      } catch (error) {
        showError(t, error.message || t('WithdrawalRecords.withdrawalError'))
      }
    }

    // 处理取消提现
    const handleCancelWithdrawal = async (withdrawalId) => {
      try {
        await store.dispatch('agent/cancelWithdrawal', withdrawalId)
        // 取消成功，刷新列表
        fetchWithdrawalRecords({
          page: currentPage.value,
          limit: limit.value
        })
        showSuccess(t, t('WithdrawalRecords.cancelSuccess'))
      } catch (error) {
        showError(t, error.message || t('WithdrawalRecords.cancelFailed'))
      }
    }

    onMounted(() => {
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value || 10
      }, { root: true })

      fetchWithdrawalRecords({
        page: 1,
        limit: limit.value || 10
      })
    })

    // 关闭确认提现弹窗
    const closeConfirmModal = () => {
      showConfirmModal.value = false
      currentRecord.value = null
    }

    // 关闭查看详情弹窗
    const closeDetailModal = () => {
      showDetailModal.value = false
      currentRecord.value = null
    }

    // 点击确认提现按钮
    const onConfirmClick = (record) => {
      currentRecord.value = record
      showConfirmModal.value = true
    }

    // 点击查看按钮
    const onViewClick = (record) => {
      currentRecord.value = record
      showDetailModal.value = true
    }

    // 确认提现操作
    const confirmWithdrawal = async () => {
      try {
        await store.dispatch('agent/withdrawConfirm', currentRecord.value.id)
        showSuccess(t, t('WithdrawalRecords.confirmSuccess'))
        closeConfirmModal()
        fetchWithdrawalRecords({
          page: currentPage.value,
          limit: limit.value
        })
      } catch (error) {
        showError(t, error.message || t('WithdrawalRecords.confirmFailed'))
      }
    }

    return {
      tableColumns,
      records,
      currentPage,
      limit,
      totalRecords,
      handlePageChange,
      getStatusText,
      getStatusClass,
      showWithdrawModal,
      withdrawForm,
      cancelWithdrawApply,
      submitWithdrawApply,
      showConfirmModal,
      showDetailModal,
      currentRecord,
      closeConfirmModal,
      closeDetailModal,
      onConfirmClick,
      onViewClick,
      confirmWithdrawal,
      balance,
      handleCancelWithdrawal,
      // 筛选相关
      filterConfig,
      filterValues,
      handleFilterSearch,
      handleFilterReset
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/user/common.css';
@import '@/styles/views/user/agent/WithdrawalRecords.css';
</style>
