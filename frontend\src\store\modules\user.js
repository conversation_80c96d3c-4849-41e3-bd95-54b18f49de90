export default {
  namespaced: true,
  
  state: {
    // 用户认证
    token: localStorage.getItem('token') || '',
    user: JSON.parse(localStorage.getItem('user')) || '',
    // 用户资料
    profile: null,
  },
  
  getters: {
    isAuthenticated: state => !!state.token,
    currentUser: state => state.user,
    userProfile: state => state.profile
  },
  
  mutations: {
    SET_TOKEN(state, token) {
      state.token = token
      localStorage.setItem('token', token)
    },
    
    SET_USER(state,user) {
      // 从token中解析用户ID和用户名
      if (state.token) {
        try {
          // JWT token由三部分组成：header.payload.signature
          // 我们需要解析payload部分
          const parts = state.token.split('.');
          if (parts.length === 3) {
            // Base64解码payload部分
            const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));
            
            // 从payload中获取用户数据
            if (payload && payload.data) {
              // 使用token中的id和username
              user = {
                id: payload.data.id,
                username: payload.data.username
              };
              state.user = user
              localStorage.setItem('user', JSON.stringify(user))
            }
          }
        } catch (error) {
          console.error('解析token失败:', error);
        }
      }
    },
    
    SET_PROFILE(state, profile) {
      state.profile = profile
    },
    
    LOGOUT(state) {
      state.token = null
      state.user = null
      state.profile = null
      localStorage.removeItem('token')
      localStorage.removeItem('user')
    }
  },
  
  actions: {
    // 获取验证码
    async getCaptcha({ commit }) {
      try {
        commit('SET_LOADING', true, { root: true })
        // 使用API模块
        const userApi = await import('@/api/user').then(m => m.default)
        const data = await userApi.getCaptcha()
        
        return data
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },

    // 初始化用户状态
    async initUserState({ commit, state, dispatch }) {
      // 如果已经有token和用户信息，直接获取用户详细资料
      if (state.token && state.user) {
        try {
          commit('SET_LOADING', true, { root: true })
          
            // 获取用户详细资料
            dispatch('fetchProfile')
            return true
        } catch (error) {
          console.error('初始化用户状态失败:', error)
          return false
        } finally {
          commit('SET_LOADING', false, { root: true })
        }
      }
      return false
    },
    
    async login({ commit, dispatch }, credentials) {
      try {
        commit('SET_LOADING', true, { root: true })
        // 使用API模块
        const userApi = await import('@/api/user').then(m => m.default)
        const data = await userApi.login(credentials)

        // 检查接口返回的数据
        if (!data) {
          throw new Error('登录失败，服务器返回数据为空')
        }
        
        // 检查是否包含错误码
        if (data.code && data.code !== 200) {
          throw new Error(data.message || '登录失败，请检查用户名和密码')
        }
        
        // 检查是否包含必要的字段
        if (!data.data.token) {
          throw new Error('登录失败，返回数据格式不正确')
        }

        // 登录成功，设置token和用户信息
        commit('SET_TOKEN', data.data.token)
        commit('SET_USER')
        
        return data
      } catch (error) {
        // 登录失败，确保清除任何可能的登录状态
        commit('LOGOUT')
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },
    
    async register({ commit }, userData) {
      try {
        commit('SET_LOADING', true, { root: true })
        // 使用API模块
        const userApi = await import('@/api/user').then(m => m.default)
        const data = await userApi.register(userData)


        // 检查接口返回的数据
        if (!data) {
          throw new Error('登录失败，服务器返回数据为空')
        }
        
        // 检查是否包含错误码
        if (data.code && data.code !== 200) {
          throw new Error(data.message || '登录失败，请检查用户名和密码')
        }
        
        // 检查是否包含必要的字段
        if (!data.data.token) {
          throw new Error('登录失败，返回数据格式不正确')
        }

        // // 登录成功，设置token和用户信息
        // commit('SET_TOKEN', data.data.token)
        // commit('SET_USER')
        
        return data
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },
    
    async forgotPassword({ commit }, email) {
      try {
        commit('SET_LOADING', true, { root: true })
        // 使用API模块
        const userApi = await import('@/api/user').then(m => m.default)
        const data = await userApi.forgotPassword(email)
        
        return data
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },
    
    async resetPassword({ commit }, resetData) {
      try {
        commit('SET_LOADING', true, { root: true })
        // 使用API模块
        const userApi = await import('@/api/user').then(m => m.default)
        const data = await userApi.resetPassword(resetData)
        
        return data
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },
    
    async fetchProfile({ commit, state }) {
      if (!state.token) return
      
      try {
        commit('SET_LOADING', true, { root: true })
        
        // 使用API模块
        const userApi = await import('@/api/user').then(m => m.default)
        const data = await userApi.getProfile()
        
        commit('SET_PROFILE', data)
        
        return data
      } catch (error) {
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },
    
    async updateProfile({ commit, state }, profileData) {
      if (!state.token) return
      
      try {
        commit('SET_LOADING', true, { root: true })
        // 使用API模块
        const userApi = await import('@/api/user').then(m => m.default)
        const data = await userApi.updateProfile(profileData)
        
        commit('SET_PROFILE', data)
        
        // 更新用户数据（如果需要）
        if (profileData.name || profileData.email) {
          const updatedUser = { ...state.user }
          if (profileData.name) updatedUser.name = profileData.name
          if (profileData.email) updatedUser.email = profileData.email
          commit('SET_USER', updatedUser)
        }
        
        return data
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },
    
    async changePassword({ commit, state }, passwordData) {
      if (!state.token) return
      
      try {
        commit('SET_LOADING', true, { root: true })
        // 使用API模块
        const userApi = await import('@/api/user').then(m => m.default)
        const data = await userApi.changePassword(passwordData)
        
        return data
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },
    
    async bindEmail({ commit, state }, emailData) {
      if (!state.token) return
      
      try {
        commit('SET_LOADING', true, { root: true })
        // 使用API模块
        const userApi = await import('@/api/user').then(m => m.default)
        const data = await userApi.bindEmail(emailData)
        
        // 更新资料和用户数据
        commit('SET_PROFILE', { ...state.profile, email: emailData.email, emailVerified: true })
        commit('SET_USER', { ...state.user, email: emailData.email })
        
        return data
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },
    
    async bindPhone({ commit, state }, phoneData) {
      if (!state.token) return
      
      try {
        commit('SET_LOADING', true, { root: true })
        // 使用API模块
        const userApi = await import('@/api/user').then(m => m.default)
        const data = await userApi.bindPhone(phoneData)
        
        // 更新资料和用户数据
        commit('SET_PROFILE', { ...state.profile, phone: phoneData.phone, phoneVerified: true })
        commit('SET_USER', { ...state.user, phone: phoneData.phone })
        
        return data
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },
    
    logout({ commit }) {
      commit('LOGOUT')
      // 重定向到登录页面通常在组件中处理
    },
    
  }
}
