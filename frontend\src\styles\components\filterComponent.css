.filter-section {
  margin-bottom: var(--spacing-lg);
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.filter-item {
  display: flex;
  flex-direction: column;
}

.filter-item label {
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.date-range {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.date-separator {
  color: var(--text-secondary);
}

.filter-actions {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-sm);
  justify-self: end;
  /* 使按钮在grid布局中靠右对齐 */
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  font-weight: var(--font-weight-regular);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
  background-color: var(--bg-secondary);
}

.form-control option {
  background-color: var(--bg-secondary);
}

.btn {
  display: inline-block;
  font-weight: var(--font-weight-medium);
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: var(--border-radius-md);
  transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast), box-shadow var(--transition-fast);
  cursor: pointer;
}

.btn-primary {
  color: var(--text-light);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-color-dark);
  border-color: var(--primary-color-dark);
}

.btn-outline-secondary {
  color: var(--text-secondary);
  background-color: transparent;
  border-color: var(--border-color);
}

.btn-outline-secondary:hover {
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  border-color: var(--border-color-dark);
}

/* 响应式调整 */
@media (max-width: 767px) {
  .filter-section {
    grid-template-columns: 1fr;
  }

  .filter-actions {
    flex-direction: column;
    width: 100%;
  }

  .filter-actions button {
    width: 100%;
  }
}

.dp__theme_dark {
  --dp-background-color: var(--bg-secondary);
  --dp-icon-color: var(--text-primary);
}

.dp__input {
  background-color: var(--bg-secondary);
}

.dp__range_end,
.dp__range_start,
.dp__active_date {
  background: var(--primary-color);
}

.dp__today {
  border: 1px solid var(--primary-color);
}

.dp__action_buttons .dp__action_select {
  background: var(--primary-color);
}
.dp__action_buttons .dp__action_select:hover {
  background: var(--primary-color);
}
.dp__action_cancel:hover {
  border-color: var(--primary-color);
}

/* 按钮组样式 */
.filter-button-group {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
}

.filter-button {
  padding: 0.4rem 0.8rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.9rem;
}

.filter-button:hover {
  background-color: var(--bg-hover);
}

.filter-button.active {
  background-color: var(--primary-color);
  color: var(--text-light);
  border-color: var(--primary-color);
}
