export default {
  namespaced: true,

  state: {
    // 代理概览
    overview: {
      settledCommission: 0,
      pendingCommission: 0,
      pendingUserCount: 0,
      invitedUserCount: 0
    },

    // 代理利润
    agentProfit: [],

    // 展开的行ID
    expandedRowIds: [],

    // 代理等级列表
    agentLevelList: [],

    // 提现记录列表
    withdrawalRecords: [],

    // 代理佣金记录列表
    commissionRecords: [],
  },

  getters: {
    overview: state => state.overview,
    agentProfit: state => state.agentProfit,
    agentLevelList: state => state.agentLevelList,
    withdrawalRecords: state => state.withdrawalRecords,
    commissionRecords: state => state.commissionRecords,
    expandedRowIds: state => state.expandedRowIds,

    // 计算属性，计算总佣金
    totalCommission: state => state.overview.settledCommission + state.overview.pendingCommission
  },

  mutations: {
    /**
     * 设置代理概览数据
     * @param {Object} state 状态对象
     * @param {Object} overview 代理概览数据
     */
    SET_OVERVIEW(state, overview) {
      state.overview = overview
    },


    /**
     * 设置代理利润数据
     * @param {Object} state 状态对象
     * @param {Array} profit 代理利润数组
     */
    SET_AGENT_PROFIT(state, profit) {
      state.agentProfit = profit
    },

    /**
     * 设置代理等级列表
     * @param {Object} state 状态对象
     * @param {Array} list 代理等级数组
     */
    SET_AGENT_LEVEL_LIST(state, list) {
      state.agentLevelList = list
    },

    /**
     * 设置提现记录列表
     * @param {Object} state 状态对象
     * @param {Array} records 提现记录数组
     */
    SET_WITHDRAWAL_RECORDS(state, records) {
      state.withdrawalRecords = records
    },

    /**
     * 设置代理佣金记录列表
     * @param {Object} state 状态对象
     * @param {Array} records 代理佣金记录数组
     */
    SET_COMMISSION_RECORDS(state, records) {
      state.commissionRecords = records
    },

    /**
     * 设置展开的行ID
     * @param {Object} state 状态对象
     * @param {Array} ids 展开的行ID数组
     */
    SET_EXPANDED_ROW_IDS(state, ids) {
      state.expandedRowIds = ids
    },

    /**
     * 切换行的展开状态
     * @param {Object} state 状态对象
     * @param {Number} id 行ID
     */
    TOGGLE_ROW_EXPANDED(state, id) {
      const index = state.expandedRowIds.indexOf(id)
      if (index > -1) {
        // 已展开，则折叠
        state.expandedRowIds.splice(index, 1)
      } else {
        // 未展开，则展开
        state.expandedRowIds.push(id)
      }
    },
  },

  actions: {
    /**
     * 获取代理概览数据
     * @param {Object} context Vuex上下文对象
     */
    async fetchAgentOverview({ commit }) {
      try {
        commit('SET_LOADING', true, { root: true })
        const agentApi = await import('@/api/agent').then(m => m.default)
        const data = await agentApi.getAgentOverview()

        if (data.code !== 200) {
          throw new Error(data.message || '获取代理概览失败')
        }

        commit('SET_OVERVIEW', data.data.overview)

        return data
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },


    /**
     * 获取代理利润数据
     * @param {Object} context Vuex上下文对象
     * @param {Object} params 查询参数
     */
    async fetchAgentProfit({ commit, state }, params = {}) {
      try {
        commit('SET_LOADING', true, { root: true })
        // 使用API模块
        const agentApi = await import('@/api/agent').then(m => m.default)
        const data = await agentApi.getAgentProfit(params)

        if (data.code !== 200) {
          throw new Error(data.message || '获取代理利润失败')
        }

        // 如果是加载子项数据
        if (params.parentId && params.parentId > 0) {
          // 递归查找父项并更新其子项
          const updateChildrenRecursively = (items) => {
            for (let i = 0; i < items.length; i++) {
              const item = items[i];

              // 如果找到了父项，更新其子项
              if (item.id === params.parentId) {
                items[i] = {
                  ...item,
                  children: data.data.profit
                };
                return true;
              }

              // 如果当前项有子项，递归查找
              if (item.children && item.children.length > 0) {
                const found = updateChildrenRecursively(item.children);
                if (found) return true;
              }
            }

            return false;
          };

          // 复制当前状态并尝试更新
          const updatedProfit = [...state.agentProfit];
          const updated = updateChildrenRecursively(updatedProfit);

          if (updated) {
            // 如果找到并更新了父项，提交更新
            commit('SET_AGENT_PROFIT', updatedProfit);
          } else {
            // 如果没有找到父项，可能是直接子项
            const parentIndex = state.agentProfit.findIndex(item => item.id === params.parentId);
            if (parentIndex > -1) {
              updatedProfit[parentIndex] = {
                ...updatedProfit[parentIndex],
                children: data.data.profit
              };
              commit('SET_AGENT_PROFIT', updatedProfit);
            } else {
              // 未找到父项，忽略
            }
          }

          // 子数据不更新全局分页状态，避免覆盖主表格的分页信息
        } else {
          // 如果是加载顶级数据，直接更新
          commit('SET_AGENT_PROFIT', data.data.profit);

          // 只有加载顶级数据时才更新全局分页状态
          commit('pagination/SET_PAGINATION', {
            current: data.data.page,
            limit: data.data.limit,
            total: data.data.total
          }, { root: true })
        }

        return data
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },


    /**
     * 获取代理等级列表
     * @param {Object} context Vuex上下文对象
     */
    async fetchAgentLevelList({ commit }) {
      try {
        commit('SET_LOADING', true, { root: true })
        const agentApi = await import('@/api/agent').then(m => m.default)
        const data = await agentApi.getAgentLevelList()

        if (data.code !== 200) {
          throw new Error(data.message || '获取代理等级列表失败')
        }

        commit('SET_AGENT_LEVEL_LIST', data.data.levelList)

        return data
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },

    /**
     * 提交提现申请
     * @param {Object} context Vuex上下文对象
     * @param {Object} data 提现申请数据
     */
    async withdrawApply({ commit }, data) {
      try {
        commit('SET_LOADING', true, { root: true })
        const agentApi = await import('@/api/agent').then(m => m.default)
        const res = await agentApi.withdrawApply(data)

        if (res.code !== 200) {
          throw new Error(res.message || '提现申请失败')
        }

        return res
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },

    /**
     * 获取提现记录列表
     * @param {Object} context Vuex上下文对象
     * @param {Object} params 查询参数
     */
    async fetchWithdrawalRecords({ commit }, params = {}) {
      try {
        commit('SET_LOADING', true, { root: true })
        const agentApi = await import('@/api/agent').then(m => m.default)
        const res = await agentApi.getWithdrawalRecords(params)

        if (res.code !== 200) {
          throw new Error(res.message || '获取提现记录失败')
        }

        commit('SET_WITHDRAWAL_RECORDS', res.data.records)

        commit('pagination/SET_PAGINATION', {
          current: res.data.page,
          limit: res.data.limit,
          total: res.data.total
        }, { root: true })

        return res
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },

    /**
     * 确认提现
     * @param {Object} context Vuex上下文对象
     * @param {Number} withdrawalId 提现记录ID
     */
    async withdrawConfirm({ commit }, withdrawalId) {
      try {
        commit('SET_LOADING', true, { root: true })
        const agentApi = await import('@/api/agent').then(m => m.default)
        const res = await agentApi.withdrawConfirm(withdrawalId)

        if (res.code !== 200) {
          throw new Error(res.message || '提现确认失败')
        }

        return res
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },

    /**
     * 取消提现申请
     * @param {object} context Vuex context
     * @param {number} withdrawalId 提现记录ID
     * @returns Promise
     */
    async cancelWithdrawal({ commit }, withdrawalId) {
      try {
        commit('SET_LOADING', true, { root: true })
        const agentApi = await import('@/api/agent').then(m => m.default)
        const res = await agentApi.cancelWithdrawal(withdrawalId)

        if (res.code !== 200) {
          throw new Error(res.message || '取消提现失败')
        }

        return res
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },

    /**
     * 获取代理佣金记录列表
     * @param {Object} context Vuex上下文对象
     * @param {Object} params 查询参数
     */
    async fetchCommissionRecords({ commit }, params = {}) {
      try {
        commit('SET_LOADING', true, { root: true })
        const agentApi = await import('@/api/agent').then(m => m.default)
        const res = await agentApi.getCommissionRecords(params)

        if (res.code !== 200) {
          throw new Error(res.message || '获取代理佣金记录失败')
        }

        commit('SET_COMMISSION_RECORDS', res.data.records)

        commit('pagination/SET_PAGINATION', {
          current: res.data.page,
          limit: res.data.limit,
          total: res.data.total
        }, { root: true })

        return res
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    }
  }
}
