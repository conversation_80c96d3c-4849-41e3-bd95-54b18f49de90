.forgot-password-page {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
    padding: var(--spacing-lg);
  }
  
  .forgot-password-container {
    width: 100%;
    max-width: 450px;
    background-color: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    overflow: hidden;
  }
  
  .forgot-password-header {
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
    text-align: center;
  }
  
  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
  }
  
  .logo img {
    width: 40px;
    height: 40px;
    margin-right: var(--spacing-sm);
  }
  
  .logo h1 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin: 0;
  }
  
  .forgot-password-title {
    font-size: 1.75rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
  }
  
  .forgot-password-subtitle {
    color: var(--text-secondary);
    margin-bottom: 0;
  }
  
  .forgot-password-form {
    padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
  }
  
  .form-group {
    margin-bottom: var(--spacing-md);
  }
  
  .form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
  }
  
  .input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .input-wrapper i {
    position: absolute;
    left: var(--spacing-md);
    color: var(--text-tertiary);
  }
  
  .input-wrapper input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) calc(var(--spacing-md) * 2 + 1rem);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--bg-input);
    color: var(--text-primary);
    font-size: 1rem;
    transition: border-color 0.3s, box-shadow 0.3s;
  }
  
  .input-wrapper input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
    outline: none;
  }
  
  .toggle-password {
    position: absolute;
    right: var(--spacing-md);
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: 0;
  }
  
  .error-message {
    color: var(--error-color);
    font-size: 0.85rem;
    margin-top: var(--spacing-xs);
  }
  
  .captcha-group {
    margin-bottom: var(--spacing-lg);
  }
  
  .captcha-wrapper {
    display: flex;
    gap: var(--spacing-md);
  }
  
  .captcha-wrapper .input-wrapper {
    flex: 1;
  }
  
  .captcha-image {
    width: 120px;
    height: 40px;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    cursor: pointer;
  }
  
  .captcha-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .resend-code {
    margin-top: var(--spacing-xs);
    font-size: 0.9rem;
    color: var(--text-secondary);
  }
  
  .resend-code a {
    color: var(--primary-color);
    text-decoration: none;
  }
  
  .btn-submit {
    width: 100%;
    padding: var(--spacing-md);
    background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: background 0.3s, transform 0.1s;
    position: relative;
  }
  
  .btn-submit:hover {
    background: linear-gradient(90deg, var(--primary-color-dark), var(--primary-color));
  }
  
  .btn-submit:active {
    transform: translateY(1px);
  }
  
  .btn-submit:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
  
  .loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  
  .success-message {
    text-align: center;
    padding: var(--spacing-lg) 0;
  }
  
  .success-icon {
    font-size: 4rem;
    color: var(--success-color);
    margin-bottom: var(--spacing-md);
  }
  
  .success-message h3 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
  }
  
  .success-message p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
  }
  
  .forgot-password-footer {
    margin-top: var(--spacing-lg);
    text-align: center;
    color: var(--text-secondary);
  }
  
  .forgot-password-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
  }
  
  @media (max-width: 480px) {
    .forgot-password-container {
      box-shadow: none;
    }
    
    .captcha-wrapper {
      flex-direction: column;
    }
    
    .captcha-image {
      width: 100%;
    }
  }