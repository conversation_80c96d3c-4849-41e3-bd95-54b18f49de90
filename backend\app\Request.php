<?php
namespace app;

// 应用请求对象类
class Request extends \think\Request
{
    /**
     * 默认过滤方法
     * @var array
     */
    protected $filter = ['trim', 'htmlspecialchars', 'strip_tags', 'app\\Request::filterSqlInjection'];
    
    /**
     * 自定义SQL注入过滤方法
     * 
     * @param mixed $value 需要过滤的值
     * @return mixed 过滤后的值
     */
    public static function filterSqlInjection($value)
    {
        if (!is_string($value)) {
            return $value;
        }
        
        // 防止SQL注入
        // 移除SQL注释
        $value = preg_replace('/\/\*.*\*\//', '', $value);
        
        // 移除常见的SQL注入模式
        $patterns = [
            '/\s+or\s+[\'\"][^\'"]*[\'\"]=[^\'"]*[\'\"]/i',  // or '1'='1
            '/\s+or\s+\d+=\d+/i',                           // or 1=1
            '/\s+and\s+[\'\"][^\'"]*[\'\"]=[^\'"]*[\'\"]/i', // and '1'='1
            '/\s+and\s+\d+=\d+/i',                          // and 1=1
            '/\s+union\s+select\s+/i',                      // union select
            '/;\s*drop\s+table/i',                          // ; drop table
            '/;\s*delete\s+from/i',                         // ; delete from
            '/;\s*update\s+/i',                             // ; update
            '/;\s*insert\s+into/i',                         // ; insert into
            '/--/i',                                         // SQL注释
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $value)) {
                // 记录可能的SQL注入尝试
                \think\facade\Log::warning('检测到可能的SQL注入尝试: ' . $value);
                // 替换为空字符串或者返回一个安全的值
                $value = '';
                break;
            }
        }
        
        return $value;
    }
}
