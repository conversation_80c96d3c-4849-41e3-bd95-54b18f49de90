<?php
declare(strict_types=1);

namespace app\utils;

use think\facade\Log;

/**
 * API Key 检查工具类
 */
class ApiKeyChecker
{
    /**
     * 检测API Key的有效性
     * 
     * @param string $apikey API Key
     * @param string $secretkey Secret Key
     * @param int $account_sub_id 账号ID  只有子账号时才需要传入
     * @return array 返回检测结果，包含success和message字段
     */
    public static function check(string $apikey, string $secretkey, int $account_sub_id = 0 ): array
    {
        try {
            // 准备请求数据
            $postData = [
                'account_sub_id' => $account_sub_id,
                'apikey' => $apikey,
                'secretkey' => $secretkey
            ];
            
            // 设置请求选项
            $options = [
                'http' => [
                    'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
                    'method'  => 'POST',
                    'content' => http_build_query($postData)
                ]
            ];
            
            // 创建上下文
            $context = stream_context_create($options);
            
            // 从环境变量获取API检查URL
            $apiCheckUrl = env('API_CHECK_URL', 'https://qtdataapi.818163.app/qtapi/checkApiStatus');
            
            // 发送请求
            $result = file_get_contents($apiCheckUrl, false, $context);
            
            // 检查请求是否成功
            if ($result === FALSE) {
                return ['success' => false, 'message' => 'API Key验证请求失败'];
            }
            
            // 解析响应
            $response = json_decode($result, true);
            Log::info('API Key验证响应: ' . print_r($response, true));
            // 检查响应格式
            if (!is_array($response)) {
                return ['success' => false, 'message' => 'API Key验证响应格式错误'];
            }
            
            // 检查API Key是否有效
            if (isset($response['data']['code']) && isset($response['data']['msg'])) {
                if ($response['data']['code'] == -1102) {
                    $errorMessage = '密钥无效,请检查是否复制错误!';
                }else{
                    $errorMessage = $response['data']['msg'] ?? 'API Key无效';
                }
                return ['success' => false, 'message' => $errorMessage];
            } else {
                return ['success' => true, 'message' => 'API Key验证成功'];
            }
        } catch (\Exception $e) {
            return ['success' => false, 'message' => 'API Key验证异常: ' . $e->getMessage()];
        }
    }
}
