<template>
  <footer class="footer tech-accent">
    <div class="container">
      <!-- 非用户中心页面显示完整页脚 -->
      <div v-if="!isUserCenter">
        <div class="footer-links">
          <div class="footer-links-column">
            <h4 class="footer-links-title">{{ $t('Footer.introduction') }}</h4>
            <ul class="footer-links-list">
              <li><router-link to="/introduction#what-is-quant">{{ $t('Footer.quantTrading') }}</router-link></li>
              <li><router-link to="/introduction#quant-advantages">{{ $t('Footer.quantAdvantages') }}</router-link></li>
              <li><router-link to="/introduction#how-to-start">{{ $t('Footer.howToStart') }}</router-link></li>
            </ul>
          </div>
          
          <div class="footer-links-column">
            <h4 class="footer-links-title">{{ $t('Footer.about') }}</h4>
            <ul class="footer-links-list">
              <li><router-link to="/about#company">{{ $t('Footer.company') }}</router-link></li>
              <li><router-link to="/about#values">{{ $t('Footer.values') }}</router-link></li>
            </ul>
          </div>
          
          <div class="footer-links-column">
            <h4 class="footer-links-title">{{ $t('Footer.help') }}</h4>
            <ul class="footer-links-list">
              <li><router-link to="/help/fund-security">{{ $t('Footer.fundSecurity') }}</router-link></li>
              <li><router-link to="/help/profit-model">{{ $t('Footer.profitModel') }}</router-link></li>
              <li><router-link to="/help/operation-process">{{ $t('Footer.operationProcess') }}</router-link></li>
              <li><router-link to="/help/agent-rules">{{ $t('Footer.agentRules') }}</router-link></li>
            </ul>
          </div>
          
          <div class="footer-links-column">
            <h4 class="footer-links-title">{{ $t('Footer.contact') }}</h4>
            <div class="footer-contact-image">
              <img src="@/assets/contact.jpg" :alt="$t('Footer.contact')" />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 所有页面都显示页脚底部版权信息 -->
      <div class="footer-bottom">
        <div class="footer-copyright">
          {{ $t('Footer.copyright', { year: currentYear }) }} 
          <a v-if="isHomePage" href="https://beian.miit.gov.cn/" target="_blank">桂ICP备2023010764号-3</a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
import { useI18n } from 'vue-i18n'
import { computed } from 'vue'
import { useRoute } from 'vue-router'

export default {
  name: 'Footer',
  setup() {
    const { t } = useI18n()
    const route = useRoute()
    
    // 获取当前年份
    const currentYear = computed(() => new Date().getFullYear())
    
    // 判断是否在用户中心
    const isUserCenter = computed(() => {
      return route.path.startsWith('/user') || route.path.startsWith('/zjfl')
    })
    
    // 判断是否在首页
    const isHomePage = computed(() => {
      return route.path === '/' || route.path === '/home'
    })
    
    return {
      currentYear,
      isUserCenter,
      isHomePage
    }
  }
}
</script>

<style scoped>
@import '@/styles/components/footer.css';
</style>
