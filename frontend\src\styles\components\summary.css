  .summary-section {
    margin: var(--spacing-lg) 0;
  }
  
  .summary-card {
    background-color: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
  }
  
  .summary-title {
    font-size: 1.25rem;
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    position: relative;
    padding-bottom: var(--spacing-xs);
  }
  
  .summary-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
    border-radius: 1.5px;
  }
  
  .summary-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
  }
  
  .summary-item {
    text-align: center;
  }
  
  .summary-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
  }
  
  .summary-value {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
  }
  
  .summary-value-positive {
    color: var(--success-color);
  }
  
  .summary-value-negative {
    color: var(--error-color);
  }
  
  @media (max-width: 991px) {
    .summary-content {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 767px) {
    .summary-content {
      grid-template-columns: 1fr;
      gap: var(--spacing-lg);
    }
  }
  