<template>
  <div class="pagination-container">
    <div class="pagination-info">
      {{ $t('Pagination.showing') }} {{ startItem }}-{{ endItem }} {{ $t('Pagination.of') }} {{ total }} {{ $t('Pagination.items') }}
    </div>
    
    <div class="pagination-controls">
      <!-- 上一页按钮 -->
      <button 
        class="pagination-button" 
        :disabled="currentPage === 1" 
        @click="changePage(currentPage - 1)"
      >
        {{ $t('Pagination.previous') }}
      </button>
      
      <!-- 页码按钮 -->
      <template v-for="(page, index) in displayedPages" :key="index">
        <button 
          v-if="page !== '...'" 
          class="pagination-button" 
          :class="{ active: page === currentPage }"
          @click="changePage(page)"
        >
          {{ page }}
        </button>
        <span v-else class="pagination-ellipsis">...</span>
      </template>
      
      <!-- 下一页按钮 -->
      <button 
        class="pagination-button" 
        :disabled="currentPage === totalPages" 
        @click="changePage(currentPage + 1)"
      >
        {{ $t('Pagination.next') }}
      </button>
      
      <!-- 跳转到指定页码 -->
      <div class="pagination-goto">
        {{ $t('Pagination.goTo') }}
        <input 
          type="number" 
          class="pagination-goto-input" 
          v-model.number="goToPage" 
          min="1" 
          :max="totalPages"
          @keyup.enter="handleGoToPage"
        >
        {{ $t('Pagination.page') }}
      </div>
      
      <!-- 每页显示记录数 -->
      <div class="pagination-per-page">
        {{ $t('Pagination.perPage') }}
        <select class="pagination-per-page-select" v-model="localPageSize" @change="handlePageSizeChange">
          <option v-for="size in displayLimitOptions" :key="size" :value="size">{{ size }}</option>
        </select>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Pagination',
  props: {
    // 总记录数
    total: {
      type: Number,
      required: true
    },
    // 当前页码
    page: {
      type: Number,
      default: 1
    },
    // 每页记录数
    limit: {
      type: Number,
      default: 10
    },
    // 可选的每页记录数
    limitOptions: {
      type: Array,
      default: () => [10, 20, 50, 100]
    },
    // 显示的页码数量
    pagerCount: {
      type: Number,
      default: 5
    },
    // 是否使用 Vuex store 中的值
    useStore: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentPage: this.page,
      localPageSize: this.$store.getters['pagination/limit'] ? this.$store.getters['pagination/limit'] : this.limit,
      goToPage: this.page
    }
  },
  computed: {
    ...mapGetters('pagination', {
      storeLimitOptions: 'limitOptions'
    }),
    
    // 使用 store 中的 limitOptions 或 props 中的 limitOptions
    displayLimitOptions() {
      return this.useStore ? this.storeLimitOptions : this.limitOptions
    },
    
    // 总页数
    totalPages() {
      return Math.max(1, Math.ceil(this.total / this.localPageSize))
    },
    // 当前页的起始记录
    startItem() {
      return this.total === 0 ? 0 : (this.currentPage - 1) * this.localPageSize + 1
    },
    // 当前页的结束记录
    endItem() {
      return Math.min(this.currentPage * this.localPageSize, this.total)
    },
    // 显示的页码
    displayedPages() {
      const { currentPage, totalPages, pagerCount } = this
      
      // 如果总页数小于等于要显示的页码数，则全部显示
      if (totalPages <= pagerCount) {
        return Array.from({ length: totalPages }, (_, i) => i + 1)
      }
      
      // 计算左右两侧应该显示的页码数
      const sideCount = Math.floor(pagerCount / 2)
      let leftCount = Math.min(currentPage - 1, sideCount)
      let rightCount = Math.min(totalPages - currentPage, sideCount)
      
      // 如果左侧或右侧页码数不足，则补充到另一侧
      if (leftCount < sideCount) {
        rightCount = Math.min(rightCount + (sideCount - leftCount), totalPages - currentPage)
      }
      if (rightCount < sideCount) {
        leftCount = Math.min(leftCount + (sideCount - rightCount), currentPage - 1)
      }
      
      // 计算起始和结束页码
      const startPage = Math.max(1, currentPage - leftCount)
      const endPage = Math.min(totalPages, currentPage + rightCount)
      
      // 构建页码数组
      const pages = []
      
      // 添加第一页
      if (startPage > 1) {
        pages.push(1)
      }
      
      // 添加左侧省略号
      if (startPage > 2) {
        pages.push('...')
      }
      
      // 添加中间页码
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i)
      }
      
      // 添加右侧省略号
      if (endPage < totalPages - 1) {
        pages.push('...')
      }
      
      // 添加最后一页
      if (endPage < totalPages) {
        pages.push(totalPages)
      }
      
      return pages
    }
  },
  watch: {
    // 监听外部传入的页码变化
    page(newVal) {
      this.currentPage = newVal
      this.goToPage = newVal
    },
    // 监听外部传入的每页记录数变化
    limit(newVal) {
      this.localPageSize = newVal
    },
    // 监听当前页码变化，更新跳转页码
    currentPage(newVal) {
      this.goToPage = newVal
    }
  },
  methods: {
    // 滚动到页面顶部
    scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'auto'
      })
    },
    
    // 切换页码
    changePage(page, force = false) {
      // 如果页码没有变化且不是强制触发，则直接返回
      if (page === this.currentPage && !force) return
      
      this.currentPage = page
      this.$emit('update:page', page)
      this.$emit('change', { page, limit: this.localPageSize })
      
      // 滚动到页面顶部
      this.scrollToTop()
    },
    // 处理跳转到指定页码
    handleGoToPage() {
      let page = parseInt(this.goToPage)
      
      if (isNaN(page) || page < 1) {
        page = 1
      } else if (page > this.totalPages) {
        page = this.totalPages
      }
      
      this.goToPage = page
      this.changePage(page)
      // 注意：不需要在这里调用 scrollToTop，因为 changePage 方法中已经包含了滚动功能
    },
    // 处理每页记录数变化
    handlePageSizeChange() {
      this.$store.dispatch('pagination/setlimit', this.localPageSize)
      
      this.changePage(1, true)
      // 注意：不需要在这里调用 scrollToTop，因为 changePage 方法中已经包含了滚动功能
    }
  }
}
</script>

<style>
@import '@/styles/components/pagination.css';
</style>
