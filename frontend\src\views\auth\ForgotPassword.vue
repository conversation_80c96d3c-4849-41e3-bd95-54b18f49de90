<template>
  <div class="forgot-password-page">
    <div class="forgot-password-container">
      <div class="forgot-password-header">
        <h2 class="forgot-password-title">{{ $t('ForgotPassword.title') }}</h2>
        <p class="forgot-password-subtitle">{{ $t('ForgotPassword.subtitle') }}</p>
      </div>
      
      <form class="forgot-password-form" @submit.prevent="handleSubmit">
        <div v-if="step === 1">
          <div class="form-group">
            <label for="email">{{ $t('ForgotPassword.email') }}</label>
            <div class="input-wrapper">
              <i class="fa fa-envelope"></i>
              <input 
                type="email" 
                id="email" 
                v-model="form.email" 
              :placeholder="$t('ForgotPassword.emailPlaceholder')"
                required
              />
            </div>
            <div class="error-message" v-if="errors.email">{{ errors.email }}</div>
          </div>
          
          <div class="form-group captcha-group">
            <label for="captcha">{{ $t('ForgotPassword.captcha') }}</label>
            <div class="captcha-wrapper">
              <div class="input-wrapper">
                <i class="fa fa-shield-alt"></i>
                <input 
                  type="text" 
                  id="captcha" 
                  v-model="form.captcha" 
                  :placeholder="$t('ForgotPassword.captchaPlaceholder')"
                  required
                />
              </div>
              <div class="captcha-image" @click="refreshCaptcha">
                <img :src="captchaUrl" alt="Captcha" />
              </div>
            </div>
            <div class="error-message" v-if="errors.captcha">{{ errors.captcha }}</div>
          </div>
          
          <button type="submit" class="btn-submit" :disabled="loading">
            <span v-if="!loading">{{ $t('ForgotPassword.sendCodeButton') }}</span>
            <span v-else class="loading-spinner"></span>
          </button>
        </div>
        
        <div v-if="step === 2">
          <div class="form-group">
            <label for="verificationCode">{{ $t('ForgotPassword.verificationCode') }}</label>
            <div class="input-wrapper">
              <i class="fa fa-key"></i>
              <input 
                type="text" 
                id="verificationCode" 
                v-model="form.verificationCode" 
                :placeholder="$t('ForgotPassword.verificationCodePlaceholder')"
                required
              />
            </div>
            <div class="error-message" v-if="errors.verificationCode">{{ errors.verificationCode }}</div>
            <div class="resend-code">
              <span v-if="countdown > 0">{{ $t('ForgotPassword.resendCodeIn', { seconds: countdown }) }}</span>
              <a href="#" v-else @click.prevent="resendCode">{{ $t('ForgotPassword.resendCode') }}</a>
            </div>
          </div>
          
          <div class="form-group">
            <label for="newPassword">{{ $t('ForgotPassword.newPassword') }}</label>
            <div class="input-wrapper">
              <i class="fa fa-lock"></i>
              <input 
                :type="showPassword ? 'text' : 'password'" 
                id="newPassword" 
                v-model="form.newPassword" 
                :placeholder="$t('ForgotPassword.newPasswordPlaceholder')"
                required
              />
              <button 
                type="button" 
                class="toggle-password" 
                @click="showPassword = !showPassword"
              >
                <i :class="showPassword ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
              </button>
            </div>
            <div class="error-message" v-if="errors.newPassword">{{ errors.newPassword }}</div>
          </div>
          
          <div class="form-group">
            <label for="confirmPassword">{{ $t('ForgotPassword.confirmPassword') }}</label>
            <div class="input-wrapper">
              <i class="fa fa-lock"></i>
              <input 
                :type="showConfirmPassword ? 'text' : 'password'" 
                id="confirmPassword" 
                v-model="form.confirmPassword" 
                :placeholder="$t('ForgotPassword.confirmPasswordPlaceholder')"
                required
              />
              <button 
                type="button" 
                class="toggle-password" 
                @click="showConfirmPassword = !showConfirmPassword"
              >
                <i :class="showConfirmPassword ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
              </button>
            </div>
            <div class="error-message" v-if="errors.confirmPassword">{{ errors.confirmPassword }}</div>
          </div>
          
          <button type="submit" class="btn-submit" :disabled="loading">
            <span v-if="!loading">{{ $t('ForgotPassword.resetPasswordButton') }}</span>
            <span v-else class="loading-spinner"></span>
          </button>
        </div>
        
        <div v-if="step === 3" class="success-message">
          <div class="success-icon">
            <i class="fa fa-check-circle"></i>
          </div>
          <h3>{{ $t('ForgotPassword.resetSuccess') }}</h3>
          <p>{{ $t('ForgotPassword.resetSuccessMessage') }}</p>
          <button type="button" class="btn-submit" @click="goToLogin">
            {{ $t('ForgotPassword.backToLogin') }}
          </button>
        </div>
      </form>
      
      <div class="forgot-password-footer">
        <p>{{ $t('ForgotPassword.rememberPassword') }} <router-link to="/auth/login">{{ $t('ForgotPassword.loginNow') }}</router-link></p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

export default {
  name: 'ForgotPasswordPage',
  setup() {
    const store = useStore()
    const router = useRouter()
    const { t } = useI18n()
    
    const step = ref(1)
    const form = reactive({
      email: '',
      captcha: '',
      verificationCode: '',
      newPassword: '',
      confirmPassword: ''
    })
    
    const errors = reactive({
      email: '',
      captcha: '',
      verificationCode: '',
      newPassword: '',
      confirmPassword: ''
    })
    
    const loading = ref(false)
    const showPassword = ref(false)
    const showConfirmPassword = ref(false)
    const captchaUrl = ref('')
    const countdown = ref(0)
    let countdownTimer = null
    
    const refreshCaptcha = async () => {
      try {
        // 使用store的getCaptcha action获取验证码
        const result = await store.dispatch('user/getCaptcha')
        
        // 处理API返回的数据格式
        if (typeof result === 'string') {
          // API返回的base64字符串
          captchaUrl.value = `${result}`
        } else if (result.captchaImage) {
          // 兼容其他格式
          captchaUrl.value = result.captchaImage
        }
      } catch (error) {
        store.dispatch('showNotification', {
          type: 'error',
          title: t('common.error'),
          message: '获取验证码失败'
        })
      }
    }
    
    const startCountdown = () => {
      countdown.value = 60
      countdownTimer = setInterval(() => {
        if (countdown.value > 0) {
          countdown.value--
        } else {
          clearInterval(countdownTimer)
        }
      }, 1000)
    }
    
    const validateStep1 = () => {
      let isValid = true
      
      // 重置错误信息
      errors.email = ''
      errors.captcha = ''
      
      // 验证邮箱
      if (!form.email) {
        errors.email = t('ForgotPassword.emailRequired')
        isValid = false
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
        errors.email = t('ForgotPassword.emailInvalid')
        isValid = false
      }
      
      // 验证验证码
      if (!form.captcha) {
        errors.captcha = t('ForgotPassword.captchaRequired')
        isValid = false
      }
      
      return isValid
    }
    
    const validateStep2 = () => {
      let isValid = true
      
      // 重置错误信息
      errors.verificationCode = ''
      errors.newPassword = ''
      errors.confirmPassword = ''
      
      // 验证验证码
      if (!form.verificationCode) {
        errors.verificationCode = t('ForgotPassword.verificationCodeRequired')
        isValid = false
      } else if (form.verificationCode.length !== 6) {
        errors.verificationCode = t('ForgotPassword.verificationCodeInvalid')
        isValid = false
      }
      
      // 验证新密码
      if (!form.newPassword) {
        errors.newPassword = t('ForgotPassword.newPasswordRequired')
        isValid = false
      } else if (form.newPassword.length < 6) {
        errors.newPassword = t('ForgotPassword.newPasswordLength')
        isValid = false
      }
      
      // 验证确认密码
      if (!form.confirmPassword) {
        errors.confirmPassword = t('ForgotPassword.confirmPasswordRequired')
        isValid = false
      } else if (form.newPassword !== form.confirmPassword) {
        errors.confirmPassword = t('ForgotPassword.passwordsNotMatch')
        isValid = false
      }
      
      return isValid
    }
    
    const handleSubmit = async () => {
      if (step.value === 1) {
        if (!validateStep1()) {
          return
        }
        
        loading.value = true
        
        try {
          // 调用store的forgotPassword action
          const result = await store.dispatch('user/forgotPassword', form.email)
          
          // 检查接口返回的数据
          if (!result) {
            throw new Error(t('ForgotPassword.sendCodeFailed'))
          }
          
          // 检查是否包含错误码
          if (result.code && result.code !== 200) {
            throw new Error(result.message || t('ForgotPassword.sendCodeFailed'))
          }
          
          // 发送成功，进入第二步
          store.dispatch('showNotification', {
            type: 'success',
            title: t('common.success'),
            message: t('ForgotPassword.sendCodeSuccess')
          })
          step.value = 2
          startCountdown()
        } catch (error) {
          // 发送失败，显示错误信息
          console.error('发送验证码失败:', error)
          
          // 获取错误消息
          const errorMsg = error.message || '';
          
          // 根据错误类型显示不同的错误信息
          if (errorMsg.includes('验证码') || errorMsg.includes('captcha')) {
            errors.captcha = t('ForgotPassword.captchaError')
            // 刷新验证码
            refreshCaptcha()
          } else if (errorMsg.includes('邮箱') || errorMsg.includes('email')) {
            errors.email = errorMsg
          } else {
            // 通用错误信息
            errors.email = t('ForgotPassword.sendCodeFailed')
          }
          
          store.dispatch('showNotification', {
            type: 'error',
            title: t('common.error'),
            message: errorMsg || t('ForgotPassword.sendCodeFailed')
          })
        } finally {
          loading.value = false
        }
      } else if (step.value === 2) {
        if (!validateStep2()) {
          return
        }
        
        loading.value = true
        
        try {
          // 调用store的resetPassword action
          const result = await store.dispatch('user/resetPassword', {
            email: form.email,
            code: form.verificationCode,
            password: form.newPassword
          })
          
          // 检查接口返回的数据
          if (!result) {
            throw new Error(t('ForgotPassword.resetPasswordFailed'))
          }
          
          // 检查是否包含错误码
          if (result.code && result.code !== 200) {
            throw new Error(result.message || t('ForgotPassword.resetPasswordFailed'))
          }
          
          // 重置成功，进入第三步
          store.dispatch('showNotification', {
            type: 'success',
            title: t('common.success'),
            message: t('ForgotPassword.resetSuccess')
          })
          step.value = 3
        } catch (error) {
          // 重置失败，显示错误信息
          console.error('重置密码失败:', error)
          
          // 获取错误消息
          const errorMsg = error.message || '';
          
          // 根据错误类型显示不同的错误信息
          if (errorMsg.includes('验证码') || errorMsg.includes('code')) {
            errors.verificationCode = t('ForgotPassword.verificationCodeInvalid')
          } else if (errorMsg.includes('密码') || errorMsg.includes('password')) {
            errors.newPassword = errorMsg
          } else {
            // 通用错误信息
            errors.verificationCode = t('ForgotPassword.resetPasswordFailed')
          }
          
          store.dispatch('showNotification', {
            type: 'error',
            title: t('common.error'),
            message: errorMsg || t('ForgotPassword.resetPasswordFailed')
          })
        } finally {
          loading.value = false
        }
      }
    }
    
    const resendCode = async () => {
      if (countdown.value > 0) {
        return
      }
      
      loading.value = true
      
      try {
        // 再次调用store的forgotPassword action
        const result = await store.dispatch('user/forgotPassword', form.email)
        
        // 检查接口返回的数据
        if (!result) {
            throw new Error(t('ForgotPassword.resendCodeFailed'))
        }
        
        // 检查是否包含错误码
        if (result.code && result.code !== 200) {
            throw new Error(result.message || t('ForgotPassword.resendCodeFailed'))
        }
        
        // 发送成功，开始倒计时
        store.dispatch('showNotification', {
          type: 'success',
          title: t('common.success'),
            message: t('ForgotPassword.resendCodeSuccess')
        })
        startCountdown()
      } catch (error) {
        // 发送失败，显示错误信息
        console.error('重新发送验证码失败:', error)
        errors.verificationCode = t('ForgotPassword.resendCodeFailed')
        store.dispatch('showNotification', {
          type: 'error',
          title: t('common.error'),
            message: error.message || t('ForgotPassword.resendCodeFailed')
        })
      } finally {
        loading.value = false
      }
    }
    
    const goToLogin = () => {
      router.push('/auth/login')
    }
    
    onMounted(() => {
      refreshCaptcha()
    })
    
    onUnmounted(() => {
      if (countdownTimer) {
        clearInterval(countdownTimer)
      }
    })
    
    return {
      step,
      form,
      errors,
      loading,
      showPassword,
      showConfirmPassword,
      captchaUrl,
      countdown,
      refreshCaptcha,
      handleSubmit,
      resendCode,
      goToLogin
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/auth/forgotPassword.css';
</style>
