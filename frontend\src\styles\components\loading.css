/* 全局加载动画样式 */
.global-loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  
  /* 添加淡入淡出效果 */
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* 当加载状态激活时的样式 */
.global-loading-container.active {
  opacity: 1;
  visibility: visible;
}

.global-loading {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
  
  /* 添加缩放和淡入效果 */
  transform: scale(0.9);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* 当加载状态激活时的内容样式 */
.global-loading-container.active .global-loading {
  transform: scale(1);
  opacity: 1;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color, #1890ff);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 10px;
  
  /* 添加阴影效果增强视觉体验 */
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.loading-text {
  font-size: 16px;
  color: #333;
  
  /* 添加文字淡入效果 */
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 暗黑模式样式 */
.dark-mode .global-loading {
  background-color: #1f1f1f;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark-mode .loading-text {
  color: #f0f0f0;
}

.dark-mode .spinner {
  border-color: rgba(255, 255, 255, 0.1);
  border-top-color: var(--primary-color, #1890ff);
}
