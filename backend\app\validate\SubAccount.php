<?php
declare(strict_types=1);

namespace app\validate;

use think\Validate;

/**
 * 子账号相关验证器
 */
class SubAccount extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'apikey' => 'require|max:255',
        'secretkey' => 'require|max:255',
        'passphrase' => 'max:255',
        'page' => 'integer|egt:1',
        'limit' => 'integer|between:10,9999',
        'status' => 'integer|in:0,1,2',
        'startDate' => 'date',
        'endDate' => 'date|checkEndDate',
        'accountId' => 'integer|egt:0'
    ];

    /**
     * 错误消息
     * @var array
     */
    protected $message = [
        'apikey.require' => 'API密钥不能为空',
        'apikey.max' => 'API密钥不能超过255个字符',
        'secretkey.require' => '密钥不能为空',
        'secretkey.max' => '密钥不能超过255个字符',
        'passphrase.max' => '口令不能超过255个字符',
        'page.integer' => '页码必须为整数',
        'page.egt' => '页码必须大于等于1',
        'limit.integer' => '每页数量必须为整数',
        'limit.between' => '每页数量必须在1-100之间',
        'status.integer' => '状态必须为整数',
        'status.in' => '状态值不合法',
        'startDate.date' => '开始日期格式不正确',
        'endDate.date' => '结束日期格式不正确',
        'endDate.checkEndDate' => '结束日期不能早于开始日期',
        'accountId.integer' => '账户ID必须为整数',
        'accountId.egt' => '账户ID必须大于等于0'
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        'index' => ['page', 'limit', 'status'],
        'updateApi' => ['apikey', 'secretkey', 'passphrase'],
        'collectionRecords' => ['page', 'limit', 'startDate', 'endDate'],
        'profitDetails' => ['page', 'limit', 'startDate', 'endDate', 'accountId']
    ];

    /**
     * 验证结束日期不早于开始日期
     * @param $value
     * @param $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkEndDate($value, $rule, $data = [])
    {
        // 验证结束日期不早于开始日期
        if (!empty($data['startDate']) && !empty($value)) {
            if (strtotime($value) < strtotime($data['startDate'])) {
                return false;
            }
        }
        return true;
    }
}
