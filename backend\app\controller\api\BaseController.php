<?php
declare(strict_types=1);

namespace app\controller\api;

use app\utils\ResponseHelper;
use think\App;
use think\Response;
use think\facade\Config;

/**
 * API基础控制器
 */
class BaseController
{
    /**
     * App实例
     * @var App
     */
    protected $app;

    /**
     * 是否批量验证
     * @var bool
     */
    protected $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];

    /**
     * 构造方法
     * @param App $app App对象
     */
    public function __construct(App $app)
    {
        $this->app = $app;
        // 控制器初始化
        $this->initialize();
    }

    /**
     * 初始化
     */
    protected function initialize()
    {
        
    }

    /**
     * 返回成功响应
     * @param mixed $data 响应数据
     * @param string $msg 响应消息
     * @param int $code 响应状态码
     * @return Response
     */
    protected function success($data = [], string $msg = '操作成功', int $code = 200): Response
    {
        return ResponseHelper::success($data, $msg, $code);
    }

    /**
     * 返回错误响应
     * @param string $msg 错误消息
     * @param int $code 错误状态码
     * @param array $errors 错误详情
     * @return Response
     */
    protected function error(string $msg = '操作失败', int $code = 400, array $errors = []): Response
    {
        return ResponseHelper::error($msg, $code, $errors);
    }
} 