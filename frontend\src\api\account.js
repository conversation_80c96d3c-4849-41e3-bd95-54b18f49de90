import api from './index'

export default {
  // 账户相关
  getAccounts(params) {
    return api.get('/accounts', { params })
  },

  getAccount(accountId) {
    return api.get(`/accounts/${accountId}`)
  },

  addAccount(accountData) {
    // 确保字段名与后端一致
    const data = {
      account: accountData.account,
      apikey: accountData.apikey,
      secretkey: accountData.secretkey
    }
    return api.post('/accounts', data)
  },

  updateAccount(accountId, accountData) {
    // 确保字段名与后端一致
    const data = {
      account: accountData.account,
      apikey: accountData.apikey,
      secretkey: accountData.secretkey
    }
    return api.put(`/accounts/${accountId}`, data)
  },

  // 子账号相关
  getSubAccounts(params) {
    return api.get('/sub-accounts', { params })
  },

  getSubAccount(subAccountId) {
    return api.get(`/sub-accounts/${subAccountId}`)
  },

  updateSubAccountApi(subAccountId, apiData) {
    // 确保字段名与后端一致
    const data = {
      apikey: apiData.apikey,
      secretkey: apiData.secretkey
    }
    return api.put(`/sub-accounts/${subAccountId}/api`, data)
  },

  // 归集记录相关
  getCollectionRecords(params) {
    return api.get('/collection-records', { params })
  },

  collectFunds() {
    return api.post('/accounts/collect-funds')
  },

  // 盈利明细相关
  getProfitDetails(params) {
    return api.get('/profit-details', { params })
  },

  // 获取收益趋势
  getProfitTrend() {
    return api.get('/accounts/profit-trend')
  },

  // 仪表盘数据
  getDashboardData() {
    return api.get('/accounts/dashboard')
  },

  // 每日收益记录
  getDailyProfitRecords(params) {
    return api.get('/accounts/daily-profit-records', { params })
  },

  // 账户统计数据
  getAccountStatistics(params) {
    return api.get('/accounts/account-statistics', { params })
  }
}
