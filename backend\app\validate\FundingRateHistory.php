<?php
declare(strict_types=1);

namespace app\validate;

use think\Validate;
use think\facade\Db;

/**
 * 资金费率历史相关验证器
 */
class FundingRateHistory extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'page' => 'integer|egt:1',
        'limit' => 'integer|between:1,100',
        'symbol_id' => 'require|integer|gt:0|checkSymbolExists',
        'start_date' => 'date',
        'end_date' => 'date|checkEndDate',
    ];

    /**
     * 错误消息
     * @var array
     */
    protected $message = [
        'page.integer' => '页码必须为整数',
        'page.egt' => '页码必须大于等于1',
        'limit.integer' => '每页数量必须为整数',
        'limit.between' => '每页数量必须在1-100之间',
        'symbol_id.require' => '币种ID不能为空',
        'symbol_id.integer' => '币种ID必须为整数',
        'symbol_id.gt' => '币种ID必须大于0',
        'symbol_id.checkSymbolExists' => '币种不存在',
        'start_date.date' => '开始日期格式不正确',
        'end_date.date' => '结束日期格式不正确',
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        'historyList' => ['page', 'limit', 'symbol_id', 'start_date', 'end_date'],
    ];

    /**
     * 验证结束日期不早于开始日期
     * @param $value
     * @param $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkEndDate($value, $rule, $data = [])
    {
        // 验证结束日期不早于开始日期
        if (!empty($data['start_date']) && !empty($value)) {
            if (strtotime($value) < strtotime($data['start_date'])) {
                return '结束日期不能早于开始日期';
            }
        }
        return true;
    }

    /**
     * 验证币种是否存在
     * @param $value
     * @param $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkSymbolExists($value, $rule, $data = [])
    {
        // 检查币种是否存在
        $symbol = Db::name('funding_rate_symbol')
            ->where('id', $value)
            ->where('status', 1)
            ->find();
            
        if (!$symbol) {
            return '币种不存在或未启用';
        }
        
        return true;
    }
}
