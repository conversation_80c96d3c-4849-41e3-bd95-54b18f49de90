<template>
  <div class="cards-wrapper">
    <div v-if="data.length > 0" class="cards-container">
      <div v-for="(item, index) in data" :key="index" class="card-item">
        <div class="card-header">
          <div class="card-title">
            <!-- 使用具名插槽允许自定义标题内容 -->
            <slot name="title" :item="item">
              {{ item.name || item.account }}
            </slot>
          </div>
          <div class="card-status">
            <!-- 任务状态插槽 -->
            <slot name="task_status" :item="item">
              <span
                v-if="item.task_status !== undefined"
                class="task-status"
                :class="getTaskStatusClass(item.task_status)"
              >
                <span
                  class="task-status-dot"
                  :class="getTaskStatusDotClass(item.task_status)"
                ></span>
                {{ getTaskStatusText(item.task_status) }}
              </span>
            </slot>
            <!-- 状态插槽 -->
            <slot name="status" :item="item">
              <span
                v-if="item.status !== undefined"
                class="status-badge"
                :class="getStatusClass(item.status)"
              >
                {{ getStatusText(item.status) }}
              </span>
            </slot>
          </div>
        </div>
        <div class="card-body">
          <!-- 为每个列创建一个卡片项，排除操作列 -->
          <div
            v-for="column in columns.filter((col) => col.key !== 'operation')"
            :key="column.key"
            class="card-item"
          >
            <div class="item-label">{{ column.title }}</div>
            <div class="item-value">
              <!-- 使用具名插槽允许自定义单元格内容 -->
              <slot :name="column.key" :item="item" :column="column">
                {{ item[column.key] }}
              </slot>
            </div>
          </div>
        </div>
        <div class="card-footer">
          <!-- 操作插槽 -->
          <slot name="operation" :item="item">
            <!-- 默认操作按钮 -->
          </slot>
        </div>
      </div>
    </div>
    <div v-else-if="!loading" class="no-data-card">
      <div class="no-data-content">
        <i class="fa fa-info-circle"></i>
        <p>{{ noDataText }}</p>
        <slot name="no-data-actions"></slot>
      </div>
    </div>
    <div v-if="loading" class="loading-card">
      <div class="loading-content">
        <i class="fa fa-spinner fa-spin"></i>
        <p>{{ loadingText }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import { useI18n } from "vue-i18n";

export default defineComponent({
  name: "Cards",
  props: {
    // 卡片列配置，与表格列配置相同
    columns: {
      type: Array,
      required: true,
      // 每列的格式: { key: 'fieldName', title: '列标题' }
    },
    // 卡片数据，与表格数据相同
    data: {
      type: Array,
      default: () => [],
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false,
    },
    // 无数据时显示的文本
    noDataText: {
      type: String,
      default: "",
    },
    // 加载中显示的文本
    loadingText: {
      type: String,
      default: "",
    },
  },
  setup(props) {
    const { t } = useI18n();

    // 获取状态样式类
    const getStatusClass = (status) => {
      // 数据库中状态: 0：未启用 1：已启用 2：已失效 3：已停用
      switch (parseInt(status)) {
        case 0:
          return "status-inactive";
        case 1:
          return "status-enabled";
        case 2:
          return "status-expired";
        case 3:
          return "status-disabled";
        default:
          return "";
      }
    };

    // 获取状态文本
    const getStatusText = (status) => {
      // 数据库中状态: 0：未启用 1：已启用 2：已失效 3：已停用
      switch (parseInt(status)) {
        case 0:
          return t("Table.statusInactive");
        case 1:
          return t("Table.statusEnabled");
        case 2:
          return t("Table.statusExpired");
        case 3:
          return t("Table.statusDisabled");
        default:
          return "-";
      }
    };

    // 获取任务状态样式类
    const getTaskStatusClass = (taskStatus) => {
      // 任务状态: 0：未运行 1：已完成 其它：执行中
      switch (parseInt(taskStatus)) {
        case 0:
          return "task-status-not-running";
        case 1:
          return "task-status-completed";
        default:
          return "task-status-running";
      }
    };

    // 获取任务状态点样式类
    const getTaskStatusDotClass = (taskStatus) => {
      // 任务状态: 0：未运行 1：已完成 其它：执行中
      switch (parseInt(taskStatus)) {
        case 0:
          return "task-status-dot-not-running";
        case 1:
          return "task-status-dot-completed";
        default:
          return "task-status-dot-running";
      }
    };

    // 获取任务状态文本
    const getTaskStatusText = (taskStatus) => {
      // 任务状态: 0：未运行 1：已完成 其它：执行中
      switch (parseInt(taskStatus)) {
        case 0:
          return t("Table.taskStatusNotRunning");
        case 1:
          return t("Table.taskStatusCompleted");
        default:
          return t("Table.taskStatusRunning");
      }
    };

    return {
      t,
      getStatusClass,
      getStatusText,
      getTaskStatusClass,
      getTaskStatusDotClass,
      getTaskStatusText,
    };
  },
});
</script>

<style scoped>
@import "@/styles/components/cards.css";
@import "@/styles/components/taskStatus.css";
</style>
