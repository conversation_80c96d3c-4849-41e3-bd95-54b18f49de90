<?php
declare(strict_types=1);

namespace app\validate;

use think\Validate;

/**
 * 消息相关验证器
 */
class Message extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'page' => 'integer|egt:1',
        'limit' => 'integer|between:1,100',
        'read' => 'in:read,unread'
    ];

    /**
     * 错误消息
     * @var array
     */
    protected $message = [
        'page.integer' => '页码必须为整数',
        'page.egt' => '页码必须大于等于1',
        'limit.integer' => '每页数量必须为整数',
        'limit.between' => '每页数量必须在1-100之间',
        'read.in' => '已读状态值不合法'
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        'index' => ['page', 'limit', 'read'],
        'markAsRead' => [],
        'markAllAsRead' => [],
        'read' => []
    ];

    /**
     * 获取消息列表场景
     */
    public function sceneIndex()
    {
        return $this->only(['page', 'limit', 'read']);
    }

    /**
     * 标记消息为已读场景
     */
    public function sceneMarkAsRead()
    {
        return $this;
    }

    /**
     * 标记所有消息为已读场景
     */
    public function sceneMarkAllAsRead()
    {
        return $this;
    }

    /**
     * 获取消息详情场景
     */
    public function sceneRead()
    {
        return $this;
    }
}
