<?php
declare(strict_types=1);

namespace app\middleware;

use app\utils\Jwt;
use app\utils\ResponseHelper;
use think\facade\Config;

/**
 * API接口身份验证中间件
 */
class AuthCheck
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure $next
     * @return \think\Response
     */
    public function handle($request, \Closure $next)
    {
        // 获取当前请求路径
        $path = $request->pathinfo();
        
        // 检查是否为开放接口（不需要验证token的接口）
        if ($this->isOpenApi($path)) {
            return $next($request);
        }
        
        // 获取token
        $token = Jwt::getToken();
        if (empty($token)) {
            return ResponseHelper::error('请先登录', 401);
        }
        
        // 验证token
        $payload = Jwt::verify($token);
        if ($payload === false) {
            return ResponseHelper::error('登录已过期，请重新登录', 401);
        }

        if (isset($payload['id']) && $payload['id'] != 0){
            // 将用户信息绑定到请求中
            $request->user = $payload;

        }else{
            return ResponseHelper::error('登录已过期，请重新登录', 401);
        }
        
        return $next($request);
    }
    
    /**
     * 判断是否为开放API（不需要验证token）
     *
     * @param string $path
     * @return bool
     */
    protected function isOpenApi(string $path): bool
    {
        // 开放的API路径前缀
        $openPrefixes = Config::get('auth.open_api_prefixes');
        
        // 完全开放的API路径（精确匹配）
        $openPaths = Config::get('auth.open_api_paths');
        
        // 检查是否在完全开放的API路径中
        if (in_array($path, $openPaths)) {
            return true;
        }
        
        // 检查是否以开放的API前缀开头
        foreach ($openPrefixes as $prefix) {
            if (strpos($path, $prefix) === 0) {
                return true;
            }
        }
        
        return false;
    }
} 