<template>
  <div class="user-page">
    <div class="page-header">
      <h1 class="page-title">{{ $t('ProfitDetails.title') }}</h1>
      <div class="page-subtitle">{{ $t('ProfitDetails.subtitle') }}</div>
    </div>

    <div class="profit-content">
      <div class="summary-section">
        <div class="summary-card">
          <div class="summary-title">{{ $t('ProfitDetails.statistics') }}</div>
          <div class="summary-content">
            <div class="summary-item">
              <div class="summary-label">{{ $t('ProfitDetails.totalCount') }}</div>
              <div class="summary-value">{{ summary.totalCount }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">{{ $t('ProfitDetails.totalAmount') }}</div>
              <div class="summary-value" :class="{ 'summary-value-positive': Number(summary.totalAmount) > 0, 'summary-value-negative': Number(summary.totalAmount) <= 0 }">≈{{ summary.totalAmount }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">{{ $t('ProfitDetails.averageAmount') }}</div>
              <div class="summary-value" :class="{ 'summary-value-positive': Number(summary.averageAmount) > 0, 'summary-value-negative': Number(summary.averageAmount) <= 0 }">≈{{ summary.averageAmount }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">{{ $t('ProfitDetails.maxAmount') }}</div>
              <div class="summary-value" :class="{ 'summary-value-positive': Number(summary.maxAmount) > 0, 'summary-value-negative': Number(summary.maxAmount) <= 0 }">≈{{ summary.maxAmount }}</div>
            </div>
          </div>
        </div>
      </div>

      <FilterComponent
        ref="filterComponentRef"
        :filters="filterConfig"
        v-model="filters"
        :initialValues="filters"
        @search="applyFilters"
        @reset="resetFilters"
        @select-change="handleSelectChange"
      />

      <Table
        :columns="tableColumns"
        :data="profitDetails"
        :no-data-text="$t('ProfitDetails.noData')"
        class="desktop-only"
      >
        <!-- 时间列 -->
        <template #date="{ item }">
          {{ item.time }}
        </template>

        <!-- 账号列 -->
        <template #accountName="{ item }">
          {{ getAccountName(item.account_id) }}
        </template>

        <!-- 子账号列 -->
        <template #subAccountName="{ item }">
          {{ getSubAccountName(item.account_sub_id) }}
        </template>

        <!-- 收益类型列 -->
        <template #type="{ item }">
          {{ item.coin }}
        </template>

        <!-- 币种数量列 -->
        <template #coinAmount="{ item }">
          {{ item.amount }}
        </template>

        <!-- 收益金额列 -->
        <template #amount="{ item }">
          {{ item.usdt }}
        </template>
      </Table>

      <Cards
        :columns="tableColumns"
        :data="profitDetails"
        :no-data-text="$t('ProfitDetails.noData')"
      >
        <!-- 标题插槽 -->
        <template #title="{ item }">
          {{ item.time }}
        </template>

        <!-- 账号插槽 -->
        <template #accountName="{ item }">
          {{ getAccountName(item.account_id) }}
        </template>

        <!-- 子账号插槽 -->
        <template #subAccountName="{ item }">
          {{ getSubAccountName(item.account_sub_id) }}
        </template>

        <!-- 收益类型插槽 -->
        <template #type="{ item }">
          {{ item.coin }}
        </template>

        <!-- 币种数量插槽 -->
        <template #coinAmount="{ item }">
          {{ item.amount }}
        </template>

        <!-- 收益金额插槽 -->
        <template #amount="{ item }">
          {{ item.usdt }}
        </template>
      </Cards>

      <Pagination
        v-if="profitDetails.length > 0"
        :total="totalProfits"
        :page="currentPage"
        :limit="limit"
        @change="handlePageChange"
      />
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import Pagination from '@/components/common/Pagination.vue'
import FilterComponent from '@/components/common/FilterComponent.vue'
import Table from '@/components/common/Table.vue'
import Cards from '@/components/common/Cards.vue'

export default {
  name: 'ProfitDetailsPage',
  components: {
    Pagination,
    FilterComponent,
    Table,
    Cards
  },
  setup() {
    const store = useStore()
    const { t } = useI18n()
    const route = useRoute()
    const filterComponentRef = ref(null)

    // 从 store 获取账号和子账号数据
    const accounts = computed(() => store.getters['account/accounts'])
    const subAccounts = computed(() => store.getters['account/subAccounts'])
    const profitDetails = computed(() => store.getters['account/profitDetails'])

    // 表格列定义
    const tableColumns = computed(() => [
      { key: 'date', title: t('ProfitDetails.time') },
      { key: 'accountName', title: t('ProfitDetails.account'), align:'left' },
      { key: 'subAccountName', title: t('ProfitDetails.subAccount'), align:'left' },
      { key: 'type', title: t('ProfitDetails.profitType') },
      { key: 'coinAmount', title: t('ProfitDetails.coinAmount') },
      { key: 'amount', title: t('ProfitDetails.profit'), align:'right' }
    ])

    // 从 store 获取分页状态
    const currentPage = computed(() => store.getters['pagination/pagination'].current)
    const limit = computed(() => store.getters['pagination/pagination'].limit)
    const totalProfits = computed(() => store.getters['pagination/pagination'].total)

    // 筛选条件
    const filters = reactive({
      accountId: '',
      subAccountId: '',
      startDate: '',
      endDate: ''
    })

    // 根据URL参数初始化筛选条件
    if (route.query.accountId) {
      filters.accountId = parseInt(route.query.accountId)
    }

    if (route.query.subAccountId) {
      filters.subAccountId = parseInt(route.query.subAccountId)
    }

    // 筛选配置 - 改为响应式对象而不是计算属性
    const filterConfig = ref([
      {
        type: 'select',
        id: 'accountSelect',
        label: t('ProfitDetails.account'),
        key: 'accountId',
        placeholder: t('ProfitDetails.allAccounts'),
        options: []
      },
      {
        type: 'select',
        id: 'subAccountSelect',
        label: t('ProfitDetails.subAccount'),
        key: 'subAccountId',
        placeholder: t('ProfitDetails.allSubAccounts'),
        options: []
      },
      {
        type: 'dateRange',
        id: 'dateRange',
        label: t('ProfitDetails.dateRange'),
        startKey: 'startDate',
        endKey: 'endDate'
      }
    ])

    // 监听账号数据变化，更新账号下拉列表选项
    watch(accounts, (newAccounts) => {
      if (newAccounts && newAccounts.length > 0) {
        filterConfig.value[0].options = newAccounts.map(account => ({
          value: account.id,
          text: account.account
        }))
      }
    }, { immediate: true })
    watch(subAccounts, (newSubAccounts) => {
      if (newSubAccounts && newSubAccounts.length > 0) {
        filterConfig.value[1].options = newSubAccounts.map(subAccount => ({
          value: subAccount.id,
          text: subAccount.email
        }))
      }
    }, { immediate: true })


    // 统计数据
    const profitStats = ref({
      total: 0,
      totalProfit: 0,
      averageProfit: 0,
      maxProfit: 0
    })

    // 统计数据展示
    const summary = computed(() => {
      return {
        totalCount: profitStats.value.total,
        totalAmount: profitStats.value.totalProfit,
        averageAmount: profitStats.value.averageProfit,
        maxAmount: profitStats.value.maxProfit
      }
    })

    // 根据 accountId 获取账号名称
    const getAccountName = (accountId) => {
      if (!accountId) return '-'
      const account = accounts.value.find(acc => acc.id === accountId)
      return account ? account.account : '-'
    }

    // 根据 subAccountId 获取子账号名称
    const getSubAccountName = (subAccountId) => {
      if (!subAccountId) return '-'
      const subAccount = subAccounts.value.find(sub => sub.id === subAccountId)
      return subAccount ? subAccount.email : '-'
    }

    // 加载盈利明细数据
    const loadProfitDetails = async (page = currentPage.value, size = limit.value) => {
      // 构建请求参数，确保分页参数正确
      const params = {
        page: page,
        limit: size
      }

      // 只添加有值的筛选条件，确保空值不会被发送
      if (filters.accountId !== undefined && filters.accountId !== null && filters.accountId !== '') {
        params.accountId = filters.accountId
      }

      if (filters.subAccountId !== undefined && filters.subAccountId !== null && filters.subAccountId !== '') {
        params.subAccountId = filters.subAccountId
      }

      if (filters.startDate) {
        params.startDate = filters.startDate
      }

      if (filters.endDate) {
        params.endDate = filters.endDate
      }

      // 调试日志，帮助排查问题
      console.log('发送查询参数:', params)

      try {
        const response = await store.dispatch('account/fetchProfitDetails', params)
        if (response && response.code === 200 && response.data) {
          // 更新统计数据
          profitStats.value = {
            total: response.data.total || 0,
            totalProfit: response.data.totalProfit || 0,
            averageProfit: response.data.averageProfit || 0,
            maxProfit: response.data.maxProfit || 0
          }
        }
      } catch (error) {
        console.error('加载盈利明细数据失败:', error)
      }
    }

    // 加载账号数据
    const loadAccounts = async () => {
      try {
        // 传递一个大的 limit 参数，确保获取所有账号数据用于下拉列表
        await store.dispatch('account/fetchAccounts', { limit: 9999 })
      } catch (err) {
        console.error('加载账号数据失败:', err)
      }
    }

    // 加载子账号数据
    const loadSubAccounts = async () => {
      try {
        // 传递一个大的 limit 参数，确保获取所有子账号数据用于下拉列表
        await store.dispatch('account/fetchSubAccounts', { limit: 9999 })
      } catch (err) {
        console.error('加载子账号数据失败:', err)
      }
    }

    // 处理分页变化
    const handlePageChange = ({ page, limit: size }) => {
      if (page && size) {
        loadProfitDetails(page, size)
      }
    }

    // 应用筛选
    const applyFilters = (filterParams) => {
      console.log('接收到的筛选参数:', filterParams)

      // 更新本地筛选条件，确保空字符串也被正确处理
      filters.accountId = filterParams.accountId !== undefined ? filterParams.accountId : filters.accountId
      filters.subAccountId = filterParams.subAccountId !== undefined ? filterParams.subAccountId : filters.subAccountId
      filters.startDate = filterParams.startDate !== undefined ? filterParams.startDate : filters.startDate
      filters.endDate = filterParams.endDate !== undefined ? filterParams.endDate : filters.endDate

      console.log('更新后的筛选条件:', filters)

      // 调用加载函数，使用第一页
      loadProfitDetails(1, limit.value)
    }

    // 重置筛选
    const resetFilters = () => {
      filters.accountId = ''
      filters.subAccountId = ''
      filters.startDate = ''
      filters.endDate = ''

      // 调用加载函数，使用第一页
      loadProfitDetails(1, limit.value)
    }

    // 监听路由参数变化
    watch(() => route.query, (newQuery) => {
      if (newQuery.accountId) {
        filters.accountId = parseInt(newQuery.accountId)
      }

      if (newQuery.subAccountId) {
        filters.subAccountId = parseInt(newQuery.subAccountId)
      }

      // 调用加载函数，使用第一页
      loadProfitDetails(1, limit.value)
    })

    onMounted(() => {
      // 重置分页状态
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value
      }, { root: true })

      // 加载账号和子账号数据
      loadAccounts()
      loadSubAccounts()

      // 加载盈利明细数据
      loadProfitDetails(1, limit.value)
    })

    // 处理下拉选择变化
    const handleSelectChange = ({ key, value }) => {
      // 如果是账号选择变化
      if (key === 'accountId') {
        console.log('账号选择变化:', value)

        // 将子账号设置为空字符串，这会使下拉框显示"全部子账号"选项
        filters.subAccountId = ''

        // 手动更新子账号下拉列表
        if (subAccounts.value && subAccounts.value.length > 0) {
          let filteredList = subAccounts.value;

          // 如果选择了特定的母账号，则过滤出该母账号下的子账号
          if (value) {
            filteredList = subAccounts.value.filter(sa => sa.account_id === parseInt(value))
          }

          // 更新子账号下拉列表的选项
          filterConfig.value[1].options = filteredList.map(subAccount => ({
            value: subAccount.id,
            text: subAccount.email
          }))
        }

        // 手动触发FilterComponent中的update:modelValue事件
        if (filterComponentRef.value) {
          // 直接修改FilterComponent中的filterValues
          filterComponentRef.value.filterValues.subAccountId = ''
          // 手动触发emit('update:modelValue', {...filterValues})
          filterComponentRef.value.$emit('update:modelValue', {...filterComponentRef.value.filterValues})

          console.log('已清空子账号选择:', filterComponentRef.value.filterValues)
        }

        // 确保在下一次查询时不会带上旧的子账号ID
        setTimeout(() => {
          // 再次确认子账号ID已被清空
          if (filters.subAccountId !== '') {
            console.log('强制清空子账号ID')
            filters.subAccountId = ''
          }
        }, 0)
      }
    }

    return {
      tableColumns,
      accounts,
      subAccounts,
      profitDetails,
      filters,
      filterConfig,
      currentPage,
      limit,
      totalProfits,
      profitStats,
      summary,
      getAccountName,
      getSubAccountName,
      handlePageChange,
      applyFilters,
      resetFilters,
      handleSelectChange,
      filterComponentRef
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/user/common.css';
@import '@/styles/views/user/profitDetails.css';
@import '@/styles/components/summary.css';
</style>
