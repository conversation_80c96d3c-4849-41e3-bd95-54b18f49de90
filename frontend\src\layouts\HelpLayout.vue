<template>
  <div class="app-container">
    <Navbar />
    <div class="help-container">
      <div class="help-sidebar">
        <div class="help-sidebar-header">
          <h3>{{ t('HelpLayout.title') }}</h3>
        </div>
        <ul class="help-sidebar-menu">
          <li 
            v-for="item in helpMenuItems" 
            :key="item.path" 
            class="help-sidebar-item"
            :class="{ 'active': isActive(item.path) }"
          >
            <router-link :to="item.path" class="help-sidebar-link">
              <i :class="'fa fa-' + item.icon"></i>
              <span>{{ item.title }}</span>
            </router-link>
          </li>
        </ul>
      </div>
      <div class="help-content">
        <Breadcrumb :items="breadcrumbItems" variant="tech" separatorType="chevron" />
        <div class="help-content-body">
          <router-view></router-view>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>

<script>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Navbar from '@/components/common/Navbar.vue'
import Footer from '@/components/common/Footer.vue'
import Breadcrumb from '@/components/common/Breadcrumb.vue'

export default {
  name: 'HelpLayout',
  components: {
    Navbar,
    Footer,
    Breadcrumb
  },
  setup() {
    const route = useRoute()
    const { t } = useI18n()
    
    // 帮助菜单项
    const helpMenuItems = computed(() => [
      {
        title: t('HelpLayout.fundSecurity'),
        icon: 'shield',
        path: '/help/fund-security'
      },
      {
        title: t('HelpLayout.profitModel'),
        icon: 'line-chart',
        path: '/help/profit-model'
      },
      {
        title: t('HelpLayout.operationProcess'),
        icon: 'list-ol',
        path: '/help/operation-process'
      },
      {
        title: t('HelpLayout.agentRules'),
        icon: 'user-shield',
        path: '/help/agent-rules'
      }
    ])
    
    // 检查当前路由是否匹配菜单项路径
    const isActive = (path) => {
      return route.path === path
    }
    
    // 基于当前路由的面包屑项
    const breadcrumbItems = computed(() => {
      const items = [
        {
          text: t('Breadcrumb.home'),
          to: '/'
        },
        {
          text: t('HelpLayout.title'),
          to: '/help'
        }
      ]
      
      // 将当前页面添加到面包屑
      const currentMenuItem = helpMenuItems.value.find(item => item.path === route.path)
      if (currentMenuItem) {
        items.push({
          text: currentMenuItem.title,
          to: currentMenuItem.path
        })
      }
      
      return items
    })
    
    return {
      t,
      helpMenuItems,
      isActive,
      breadcrumbItems
    }
  }
}
</script>

<style scoped>
@import '@/styles/layouts/helpLayout.css';
</style>
