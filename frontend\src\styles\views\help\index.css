.help-page {
  padding: var(--spacing-xl) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.page-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 800px;
  margin: 0 auto;
}

.help-content {
  margin-top: var(--spacing-xl);
}

.help-card {
  display: block;
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  height: 100%;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  box-shadow: var(--box-shadow);
  text-decoration: none;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

.help-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--box-shadow-hover);
}

.help-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
  color: var(--text-light);
  font-size: 1.75rem;
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}

.help-title {
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.help-description {
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: var(--spacing-md);
}

.help-link {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  transition: color var(--transition-fast);
}

.help-link i {
  transition: transform var(--transition-fast);
}

.help-card:hover .help-link {
  color: var(--primary-color-dark);
}

.help-card:hover .help-link i {
  transform: translateX(5px);
}

.help-faq {
  margin-top: var(--spacing-xxl);
}

.section-title {
  font-size: 2rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  position: relative;
  padding-bottom: var(--spacing-sm);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  border-radius: 2px;
}

.faq-list {
  margin-top: var(--spacing-lg);
}

.faq-item {
  margin-bottom: var(--spacing-md);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  background-color: var(--bg-card);
  box-shadow: var(--box-shadow);
}

.faq-question {
  padding: var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  transition: background-color var(--transition-fast);
}

.faq-question:hover {
  background-color: var(--bg-hover);
}

.faq-question i {
  color: var(--primary-color);
  transition: transform var(--transition-fast);
}

.faq-answer {
  padding: 0 var(--spacing-md);
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal), padding var(--transition-normal);
  color: var(--text-secondary);
  line-height: 1.6;
}

.faq-answer.open {
  padding: 0 var(--spacing-md) var(--spacing-md);
  max-height: 500px;
}

.help-contact {
  margin-top: var(--spacing-xxl);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
}

.contact-text {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

.contact-methods {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
}

.contact-method {
  display: flex;
  align-items: center;
}

.contact-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  color: var(--text-light);
  font-size: 1.25rem;
  flex-shrink: 0;
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}

.contact-info h3 {
  font-size: 1.1rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.contact-info p {
  color: var(--text-secondary);
  font-size: 0.95rem;
}

@media (max-width: 991px) {
  .contact-methods {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 767px) {
  .page-title {
    font-size: 2rem;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .contact-methods {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}
