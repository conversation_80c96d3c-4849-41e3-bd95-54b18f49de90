<template>
  <div class="fund-security-page">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">资金安全</h1>
        <div class="page-subtitle">了解我们如何保障您的资金安全</div>
      </div>
      
      <div class="security-content">
        <div class="security-section">
          <h2 class="section-title">API权限隔离</h2>
          <div class="section-content">
            <div class="section-icon">
              <i class="fa fa-key"></i>
            </div>
            <div class="section-text">
              <p>
                我们的平台只需要交易权限的API密钥，不需要提现权限，因此无法动用您的资金。这意味着即使在极端情况下，您的资金也是安全的，因为我们没有提现的权限。
              </p>
              <p>
                我们建议您在创建API密钥时，只授予交易权限，禁用提现权限，以确保最大程度的安全性。
              </p>
            </div>
          </div>
        </div>
        
        <div class="security-section">
          <h2 class="section-title">数据加密存储</h2>
          <div class="section-content">
            <div class="section-icon">
              <i class="fa fa-lock"></i>
            </div>
            <div class="section-text">
              <p>
                您的API密钥在我们的系统中使用高级加密算法进行加密存储，即使是我们的技术人员也无法查看您的原始API密钥。
              </p>
              <p>
                我们采用行业标准的AES-256加密算法，确保您的敏感信息在存储和传输过程中都得到最高级别的保护。
              </p>
            </div>
          </div>
        </div>
        
        <!-- <div class="security-section">
          <h2 class="section-title">多重安全验证</h2>
          <div class="section-content">
            <div class="section-icon">
              <i class="fa fa-shield-alt"></i>
            </div>
            <div class="section-text">
              <p>
                我们的平台支持多重安全验证，包括手机验证码、邮箱验证码和谷歌验证器等多种验证方式，确保只有您本人才能访问和操作您的账户。
              </p>
              <p>
                我们强烈建议您启用多重安全验证，以提高账户的安全性。特别是在进行重要操作时，如修改API密钥、提现等，都需要通过多重验证才能完成。
              </p>
            </div>
          </div>
        </div> -->
        
        <div class="security-section">
          <h2 class="section-title">风险控制系统</h2>
          <div class="section-content">
            <div class="section-icon">
              <i class="fa fa-chart-line"></i>
            </div>
            <div class="section-text">
              <p>
                我们的平台内置了先进的风险控制系统，可以实时监控市场行情和交易情况，在异常情况下自动暂停交易，防止因市场剧烈波动导致的资金损失。
              </p>
              <p>
                我们的风险控制系统包括止损机制、资金使用比例控制、单笔交易金额限制等多种保护措施，确保您的资金在交易过程中得到最大程度的保护。
              </p>
            </div>
          </div>
        </div>
        
        <div class="security-section">
          <h2 class="section-title">24*7安全监控</h2>
          <div class="section-content">
            <div class="section-icon">
              <i class="fa fa-eye"></i>
            </div>
            <div class="section-text">
              <p>
                我们的安全团队全天候监控系统运行状态和安全情况，及时发现和处理潜在的安全威胁。
              </p>
              <p>
                我们采用先进的入侵检测系统和防火墙技术，防止黑客攻击和未授权访问，确保平台的安全稳定运行。
              </p>
            </div>
          </div>
        </div>
        
        <div class="security-section">
          <h2 class="section-title">安全建议</h2>
          <div class="section-content">
            <div class="section-icon">
              <i class="fa fa-lightbulb"></i>
            </div>
            <div class="section-text">
              <p>为了确保您的账户和资金安全，我们建议您：</p>
              <ul class="security-tips">
                <li>使用强密码，包含大小写字母、数字和特殊字符</li>
                <li>定期更换密码</li>
                <li>不要在公共场所或不安全的网络环境下登录账户</li>
                <li>不要将您的账户信息和API密钥分享给他人</li>
                <li>定期检查账户活动，及时发现异常情况</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <div class="security-cta">
        <h2 class="cta-title">准备好开始安全的量化交易之旅了吗？</h2>
        <p class="cta-text">
          立即注册，体验我们安全、高效、稳定的量化交易服务。
        </p>
        <div class="cta-buttons">
          <router-link to="/auth/register" class="btn btn-primary btn-lg pulse-animation">
            立即注册
          </router-link>
          <router-link to="/help" class="btn btn-outline-secondary btn-lg">
            返回帮助中心
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FundSecurityPage'
}
</script>

<style scoped>
@import '@/styles/views/help/fundSecurity.css';
</style>
