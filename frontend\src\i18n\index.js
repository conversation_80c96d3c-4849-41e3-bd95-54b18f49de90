import { createI18n } from 'vue-i18n'
import store from '../store'
import { getAppLocale, DEFAULT_LOCALE } from '@/utils/locale'

// 动态导入所有翻译文件
const loadLocaleMessages = () => {
  const locales = {
    zh_CN: {},
    zh_TW: {},
    zh_HK: {},
    en_US: {},
    vi_VN: {},
    ko_KR: {},
    th_TH: {},
    ms_MY: {}
  }

  // 支持的语言列表
  const supportedLocales = ['zh_CN', 'zh_TW', 'zh_HK', 'en_US', 'vi_VN', 'ko_KR', 'th_TH', 'ms_MY']

  // 动态导入每种语言的所有JSON文件
  supportedLocales.forEach(locale => {
    const modules = import.meta.glob(`./**/*.json`, { eager: true })
    for (const path in modules) {
      // 检查路径是否属于当前语言
      if (path.includes(`/${locale}/`)) {
        const module = modules[path].default
        // 合并翻译内容到对应语言，保持命名空间
        for (const key in module) {
          locales[locale][key] = module[key]
        }
      }
    }
  })

  return locales
}

// 从store中获取locale，确保一致性
const getLocale = () => {
  // 如果store已初始化，使用store中的locale
  if (store.state.locale) {
    return store.state.locale
  }
  // 否则使用智能语言检测
  return getAppLocale()
}

const i18n = createI18n({
  legacy: false, // 使用Composition API
  locale: getLocale(),
  fallbackLocale: DEFAULT_LOCALE, // 使用英文作为默认回退语言
  messages: loadLocaleMessages(),
  // 添加i18n ally扩展所需的配置
  silentTranslationWarn: true, // 在开发环境中禁用翻译警告
  missingWarn: false, // 禁用缺失翻译警告
  fallbackWarn: false, // 禁用回退翻译警告
})

export default i18n
