<?php
declare(strict_types=1);

namespace app\validate;

use think\Validate;
use think\facade\Db;

/**
 * 代理相关验证器
 */
class Agent extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'page' => 'integer|egt:1',
        'limit' => 'integer|between:1,100',
        'status' => 'integer|in:0,1',
        'startDate' => 'date',
        'endDate' => 'date|checkEndDate', // 新增 checkEndDate 验证规则
        'user_id' => 'require|integer|gt:0|checkUserExists',
        'amount' => 'require|float|gt:1000',
        'address' => 'require|max:255',
        'withdrawal_id' => 'require|integer|gt:0',
    ];

    /**
     * 错误消息
     * @var array
     */
    protected $message = [
        'page.integer' => '页码必须为整数',
        'page.egt' => '页码必须大于等于1',
        'limit.integer' => '每页数量必须为整数',
        'limit.between' => '每页数量必须在1-100之间',
        'status.integer' => '状态必须为整数',
        'status.in' => '状态值不合法',
        'startDate.date' => '开始日期格式不正确',
        'endDate.date' => '结束日期格式不正确',
        'user_id.require' => '用户ID不能为空',
        'user_id.integer' => '用户ID必须为整数',
        'user_id.gt' => '用户ID必须大于0',
        'user_id.checkUserExists' => '用户不存在',
        'amount.require' => '提现金额不能为空',
        'amount.float' => '提现金额必须为数字',
        'amount.gt' => '提现金额必须大于1000',
        'address.require' => '提现地址不能为空',
        'address.length' => '提现地址长度不合法',
        'withdrawal_id.require' => '提现记录ID不能为空',
        'withdrawal_id.integer' => '提现记录ID必须为整数',
        'withdrawal_id.gt' => '提现记录ID必须大于0',
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        'overview' => ['page', 'limit'], // 只验证可能需要的分页参数
        'profit' => ['page', 'startDate', 'endDate'],
        'withdrawApply' => ['amount', 'address'],
        'withdrawalRecords' => ['page', 'limit'],
        'withdrawConfirm' => ['withdrawal_id'],
        'cancelWithdrawal' => ['withdrawal_id'], // 新增取消提现验证场景
        'commissionRecords' => ['page', 'limit', 'startDate', 'endDate'], // 代理佣金记录验证场景
    ];

    /**
     * 验证结束日期不早于开始日期
     * @param $value
     * @param $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkEndDate($value, $rule, $data = [])
    {
        // 验证结束日期不早于开始日期
        if (!empty($data['startDate']) && !empty($value)) {
            if (strtotime($value) < strtotime($data['startDate'])) {
                return '结束日期不能早于开始日期';
            }
        }
        return true;
    }

    /**
     * 验证用户是否存在
     * @param $value
     * @param $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkUserExists($value, $rule, $data = [])
    {
        // 检查用户是否存在
        $user = Db::name('users')
            ->where('id', $value)
            ->find();

        if (!$user) {
            return false;
        }
        return true;
    }
}
