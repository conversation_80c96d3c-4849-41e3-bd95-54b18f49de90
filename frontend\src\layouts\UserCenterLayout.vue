<template>
  <div class="app-container" :class="{ 'sidebar-collapsed': isSidebarCollapsed }">
    <Navbar />
    <div class="user-center-container">
      <!-- 侧边栏内容 -->
      <div class="user-sidebar">
        <!-- 侧边栏切换按钮 -->
        <div class="sidebar-toggle-btn" @click="toggleSidebar">
          <i class="fa fa-angle-left"></i>
        </div>
        <div class="user-sidebar-header" v-show="!isSidebarCollapsed">
          <h3>{{ t('UserCenterLayout.userCenter') }}</h3>
        </div>

        <ul class="user-sidebar-menu">
          <li
            v-for="item in sidebarMenuItems"
            :key="item.path"
            class="user-sidebar-item"
            :class="{ 'active': item.active, 'has-children': item.children, 'open': isItemOpen(item) }"
          >
            <a
              class="user-sidebar-link"
              @click="handleItemClick(item)"
            >
              <i :class="`fa fa-${item.icon}`"></i>
            <span class="user-sidebar-item-title" v-show="!isSidebarCollapsed">{{ item.title }}</span>
              <span v-if="item.badge && !isSidebarCollapsed" class="user-sidebar-badge">{{ item.badge }}</span>
              <span v-if="item.children && !isSidebarCollapsed" class="user-sidebar-arrow">
                <i class="fa fa-angle-right"></i>
              </span>
            </a>

            <!-- 子菜单 -->
            <transition name="dropdown">
              <ul v-if="item.children && isItemOpen(item)" class="user-sidebar-submenu">
                <li
                  v-for="child in item.children"
                  :key="child.path"
                  class="user-sidebar-submenu-item"
                  :class="{ 'active': child.active }"
                >
                  <a
                    class="user-sidebar-submenu-link"
                    @click="navigateTo(child.path)"
                  >
                    <i v-if="child.icon" :class="`fa fa-${child.icon}`"></i>
                    <span>{{ child.title }}</span>
                  </a>
                </li>
              </ul>
            </transition>
          </li>
        </ul>
      </div>

      <!-- 主内容区域 -->
      <div class="user-content">
        <Breadcrumb :items="breadcrumbItems" variant="tech" separatorType="chevron" />
        <div class="user-content-body">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import Navbar from '@/components/common/Navbar.vue'
import Footer from '@/components/common/Footer.vue'
import Breadcrumb from '@/components/common/Breadcrumb.vue'

// 路由和状态管理
const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const store = useStore()


// 侧边栏状态
const openItems = ref([])
const isSidebarCollapsed = ref(false)

// 切换侧边栏展开/收起状态
const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
  // 保存状态到本地存储，以便在页面刷新后保持状态
  localStorage.setItem('sidebarCollapsed', isSidebarCollapsed.value)
}

// 检查是否为移动端
const isMobileView = () => {
  return window.innerWidth < 992
}

// 初始化侧边栏状态
const initSidebarState = () => {
  // 如果是移动端，则自动收起侧边栏
  if (isMobileView()) {
    isSidebarCollapsed.value = true
  } else {
    // 否则使用本地存储的状态
    const savedState = localStorage.getItem('sidebarCollapsed')
    if (savedState !== null) {
      isSidebarCollapsed.value = savedState === 'true'
    }
  }
}

// 窗口大小变化时更新侧边栏状态
const handleResize = () => {
  if (isMobileView()) {
    isSidebarCollapsed.value = true
  }
}

// 在组件挂载时初始化侧边栏状态并添加窗口大小变化监听
onMounted(() => {
  initSidebarState()
  window.addEventListener('resize', handleResize)
})

// 在组件卸载前移除事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 侧边栏菜单数据
const menuItems = computed(() => {
  const items = [
    {
      title: t('UserCenterLayout.overview'),
      icon: 'dashboard',
      path: '/user'
    },
    {
      title: t('UserCenterLayout.personalInfo'),
      icon: 'user',
      path: '/user/personal-info'
    },
    {
      title: t('UserCenterLayout.accountManagement'),
      icon: 'credit-card',
      path: '/user/account-management'
    },
    {
      title: t('UserCenterLayout.subAccountManagement'),
      icon: 'users',
      path: '/user/sub-account-management'
    },
    {
      title: t('UserCenterLayout.collectionRecords'),
      icon: 'exchange',
      path: '/user/collection-records'
    },
    {
      title: t('UserCenterLayout.profitDetails'),
      icon: 'line-chart',
      path: '/user/profit-details'
    }
  ]


  items.push({
    title: t('UserCenterLayout.agentCenter'),
    icon: 'user-shield',
    path: '/user/agent-center',
    children: [
      {
        title: t('UserCenterLayout.overview'),
        icon: 'tachometer-alt',
        path: '/user/agent-center'
      },
      {
        title: t('UserCenterLayout.agentProfit'),
        icon: 'pie-chart',
        path: '/user/agent-profit'
      },
      {
        title: t('WithdrawalRecords.title'),
        icon: 'money-check-alt',
        path: '/user/withdrawal-records'
      },
      {
        title: t('CommissionRecords.title'),
        icon: 'file-invoice-dollar',
        path: '/user/commission-records'
      }
    ]
  })

  return items
})

// 侧边栏菜单项，添加active状态
const sidebarMenuItems = computed(() => {
  return menuItems.value.map(item => {
    // 处理有子菜单的项目
    if (item.children) {
      const children = item.children.map(child => ({
        ...child,
        active: isActive(child.path)
      }))

      return {
        ...item,
        active: isActive(item.path) || children.some(child => child.active),
        children
      }
    }

    // 处理没有子菜单的项目
    return {
      ...item,
      active: isActive(item.path)
    }
  })
})

// 判断路径是否激活
const isActive = (path) => {
  // 特殊处理根路径 /user
  if (path === '/user') {
    return route.path === '/user'
  }

  // 其他路径精确匹配
  return route.path === path
}

// 面包屑导航项
const breadcrumbItems = computed(() => {
  const items = [
    {
      text: t('UserCenterLayout.home'),
      to: '/'
    },
    {
      text: t('UserCenterLayout.userCenter'),
      to: '/user'
    }
  ]

  // 根据当前路由添加额外的面包屑项
  if (route.path !== '/user') {
    // 特殊处理每日收益页面
    if (route.path === '/user/daily-profit') {
      items.push({
        text: t('DailyProfit.title'),
        to: '/user/daily-profit'
      })
    } else if (route.path === '/user/account-statistics') {
      // 特殊处理账户统计页面
      items.push({
        text: t('AccountStatistics.title'),
        to: '/user/account-statistics'
      })
    } else {
      // 处理其他页面
      const currentMenuItem = findMenuItem(sidebarMenuItems.value, route.path)
      if (currentMenuItem) {
        if (currentMenuItem.parent) {
          items.push({
            text: currentMenuItem.parent.title,
            to: currentMenuItem.parent.path
          })
        }

        items.push({
          text: currentMenuItem.title,
          to: currentMenuItem.path
        })
      }
    }
  }

  return items
})

// 查找当前菜单项及其父项
const findMenuItem = (items, path) => {
  for (const item of items) {
    if (item.path === path) {
      return item
    }

    if (item.children) {
      for (const child of item.children) {
        if (child.path === path) {
          return { ...child, parent: item }
        }
      }
    }
  }

  return null
}

// 检查菜单项是否展开
const isItemOpen = (item) => {
  return openItems.value.includes(item.path) ||
         (item.children && item.children.some(child => child.active))
}

// 处理菜单项点击
const handleItemClick = (item) => {
  if (item.children) {
    // 切换下拉菜单
    const index = openItems.value.indexOf(item.path)
    if (index === -1) {
      openItems.value.push(item.path)
    } else {
      openItems.value.splice(index, 1)
    }
  } else if (item.path) {
    // 导航到路径
    navigateTo(item.path)
  }
}

// 导航到指定路径
const navigateTo = (path) => {
  router.push(path)
}

// 监听路由变化以更新活动项
watch(() => route.path, () => {
  // 自动展开活动子项的父项
  sidebarMenuItems.value.forEach(item => {
    if (item.children) {
      const hasActiveChild = item.children.some(child => child.active)
      const index = openItems.value.indexOf(item.path)

      if (hasActiveChild && index === -1) {
        openItems.value.push(item.path)
      } else if (!hasActiveChild && index !== -1 && !item.active) {
        openItems.value.splice(index, 1)
      }
    }
  })
}, { immediate: true })
</script>

<style scoped>
@import '@/styles/layouts/userCenterLayout.css';
</style>
