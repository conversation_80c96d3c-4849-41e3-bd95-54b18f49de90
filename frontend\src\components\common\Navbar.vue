<template>
  <nav class="navbar tech-accent">
    <div class="navbar-logo">
      <router-link to="/">
        <img :src="logoPath" alt="Logo" class="navbar-logo-image" />
      </router-link>
    </div>

    <ul class="navbar-nav navbar-nav-center d-none-md">
      <li class="nav-item" v-for="item in navItems" :key="item.path">
        <router-link :to="item.path" class="nav-link" :class="{ 'active': isActive(item.path) }">
          {{ item.title }}
        </router-link>
      </li>
    </ul>

    <div class="navbar-nav navbar-nav-right">
      <!-- 主题切换 -->
      <div class="nav-item">
        <div class="navbar-icon theme-toggle" @click="toggleDarkMode" :class="{'theme-toggle-animation': themeAnimating}">
            <i :class="isDarkMode ? 'fa fa-sun' : 'fa fa-moon'"></i>
        </div>
      </div>

      <!-- 语言选择器 -->
      <div class="nav-item">
        <div class="navbar-icon" @click="toggleLanguageDropdown">
          <i class="fa fa-globe"></i>
          <div class="dropdown-menu" v-if="showLanguageDropdown">
            <a
              v-for="lang in availableLanguages"
              :key="lang.code"
              class="dropdown-item"
              @click.stop.prevent="changeLanguage(lang.code)"
              :class="{ 'active': currentLanguage === lang.code }"
            >
              <span>{{ lang.name }}</span>
            </a>
          </div>
        </div>
      </div>

      <!-- 下载应用 -->
      <!-- <div class="nav-item d-none-md">
        <div class="navbar-icon" @click="toggleQrCodeDropdown">
          <i class="fa fa-download"></i>
          <div class="dropdown-menu qrcode-dropdown" v-if="showQrCodeDropdown">
            <div class="qrcode-title">{{ $t('Navbar.downloadApp') }}</div>
            <div class="qrcode-tabs">
              <div
                class="qrcode-tab"
                :class="{ 'active': qrCodeTab === 'ios' }"
                @click.stop="qrCodeTab = 'ios'"
              >
                iOS
              </div>
              <div
                class="qrcode-tab"
                :class="{ 'active': qrCodeTab === 'android' }"
                @click.stop="qrCodeTab = 'android'"
              >
                Android
              </div>
            </div>
            <div class="qrcode-image">
              <div class="qrcode-placeholder">
                <i class="fa fa-qrcode"></i>
                <div>{{ qrCodeTab === 'ios' ? $t('Navbar.iosVersion') : $t('Navbar.androidVersion') }}</div>
              </div>
            </div>
            <div class="qrcode-description">
              {{ $t('Navbar.scanQrCode') }}
            </div>
          </div>
        </div>
      </div> -->

      <!-- 消息通知 -->
      <div class="nav-item" v-if="isLoggedIn">
        <div class="navbar-icon" @click="toggleMessageDropdown">
          <i class="fa fa-bell"></i>
          <span v-if="unreadMessageCount > 0" class="navbar-icon-badge">{{ unreadMessageCount }}</span>
          <div class="dropdown-menu message-dropdown" v-if="showMessageDropdown">
            <div class="message-header">
              <div class="message-title">{{ $t('Navbar.message') }}</div>
              <div class="message-actions" @click="markAllAsRead">{{ $t('Navbar.markAllAsRead') }}</div>
            </div>
            <ul class="message-list">
              <li
                v-for="message in navbarMessages"
                :key="message.id"
                class="message-item"
                :class="{ 'unread': message.status === 0 }"
                @click="openMessageDetail(message)"
              >
                <div class="message-item-icon">
                  <i :class="getMessageIcon(message)"></i>
                </div>
                <div class="message-item-content">
                  <div class="message-item-header">
                    <div class="message-item-title">{{ message.title }}</div>
                    <div class="message-item-meta">
                      <div class="message-item-time">{{ formatTime(message.created_at) }}</div>
                      <div
                        class="message-type"
                        :class="`type${message.type}`"
                      >
                        {{ $t(`Messages.type${message.type}`) }}
                      </div>
                      <div
                        class="message-urgency"
                        :class="`urgency${message.urgency}`"
                      >
                        {{ $t(`Messages.urgency${message.urgency}`) }}
                      </div>
                    </div>
                  </div>
                  <div class="message-item-body">{{ message.content }}</div>
                </div>
              </li>
            </ul>
            <div class="message-footer" v-if="navbarMessages.length > 0">
              <router-link to="/messages">{{ $t('Navbar.viewAll') }}</router-link>
            </div>
            <div class="message-empty" v-else>
              {{ $t('Navbar.noMessages') }}
            </div>
          </div>
        </div>
      </div>

      <!-- 用户菜单 -->
      <div class="nav-item" v-if="isLoggedIn">
        <div class="user-dropdown" @click="toggleUserDropdown">
          <div class="user-avatar-placeholder">
            <i class="fa fa-user"></i>
          </div>
          <div class="user-info">
            <div class="user-name">{{ userName }}</div>
          </div>
          <i class="fa fa-chevron-down"></i>
          <div class="dropdown-menu" v-if="showUserDropdown">
            <router-link to="/user" class="dropdown-item">
              <i class="fa fa-user"></i>
              {{ $t('Navbar.userCenter') }}
            </router-link>
            <div class="dropdown-divider"></div>
            <a href="#" class="dropdown-item" @click.prevent="logout">
              <i class="fa fa-sign-out-alt"></i>
              {{ $t('Navbar.logout') }}
            </a>
          </div>
        </div>
      </div>

      <!-- 登录按钮 -->
      <div class="nav-item" v-else>
        <router-link to="/auth/login" class="btn btn-primary btn-sm">
          {{ $t('Navbar.login') }}
        </router-link>
      </div>

      <!-- 移动端菜单切换 -->
      <button class="navbar-toggle d-md-none" @click="toggleMobileMenu">
        <i class="fa fa-bars"></i>
      </button>
    </div>

    <!-- 移动端菜单 -->
    <div class="navbar-mobile" :class="{ 'active': showMobileMenu }">
      <div class="navbar-mobile-header">
        <div class="navbar-logo">
          <i class="fa fa-chart-line logo-icon"></i>
          <span class="navbar-logo-text">{{ $t('Navbar.webname') }}</span>
        </div>
        <button class="navbar-mobile-close" @click="toggleMobileMenu">
          <i class="fa fa-times"></i>
        </button>
      </div>
      <ul class="navbar-mobile-nav">
        <li class="nav-item" v-for="item in navItems" :key="item.path">
          <router-link
            :to="item.path"
            class="nav-link"
            :class="{ 'active': isActive(item.path) }"
            @click="showMobileMenu = false"
          >
            {{ item.title }}
          </router-link>
        </li>
        <!-- 移动端主题切换按钮 -->
        <li class="nav-item theme-toggle-mobile">
          <div class="nav-link" @click="toggleDarkMode">
            <i :class="[isDarkMode ? 'fa fa-sun' : 'fa fa-moon', {'theme-toggle-animation': themeAnimating}]"></i>
            <span>{{ isDarkMode ? '切换到亮色模式' : '切换到暗色模式' }}</span>
          </div>
        </li>
      </ul>
    </div>

    <!-- 移动端菜单背景遮罩 -->
    <div
      class="navbar-backdrop"
      :class="{ 'active': showMobileMenu }"
      @click="showMobileMenu = false"
    ></div>

    <!-- 消息详情弹窗 -->
    <div class="modal-backdrop" :class="{ 'show': showMessageModal }" @click="closeMessageModal">
      <div class="modal" :class="{ 'show': showMessageModal }" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">{{ $t('Messages.viewDetails') }}</h3>
          <button class="modal-close" @click="closeMessageModal">
            <i class="fa fa-times"></i>
          </button>
        </div>
        <div class="modal-body" v-if="selectedMessage">
          <div class="message-modal-header">
            <div class="message-modal-icon">
              <i :class="getMessageIcon(selectedMessage)"></i>
            </div>
            <div class="message-modal-info">
              <h4>{{ selectedMessage.title }}</h4>
              <div class="message-modal-meta">
                <div class="message-modal-time">{{ formatTime(selectedMessage.created_at) }}</div>
                <div
                  class="message-type"
                  :class="`type${selectedMessage.type}`"
                >
                  {{ $t(`Messages.type${selectedMessage.type}`) }}
                </div>
                <div
                  class="message-urgency"
                  :class="`urgency${selectedMessage.urgency}`"
                >
                  {{ $t(`Messages.urgency${selectedMessage.urgency}`) }}
                </div>
              </div>
            </div>
          </div>
          <div class="message-modal-content">
            {{ selectedMessage.content }}
          </div>
          <div class="message-modal-action" v-if="selectedMessage.action_name">
            <button
              class="btn btn-primary"
              @click="handleMessageAction"
            >
              {{ selectedMessage.action_name }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import { setAppLocale, getLocaleDisplayName, SUPPORTED_LOCALES } from '@/utils/locale'

// 导入所有logo图片
import logoMobile from '@/assets/logo_mobile.png'
import logoZhCnDark from '@/assets/logo_zh_CN_dark.png'
import logoZhCnLight from '@/assets/logo_zh_CN_light.png'
import logoEnUsDark from '@/assets/logo_en_US_dark.png'
import logoEnUsLight from '@/assets/logo_en_US_light.png'

export default {
  name: 'Navbar',
  setup() {
    const router = useRouter()
    const { t, locale } = useI18n()
    const store = useStore()

    // Dark mode state
    const isDarkMode = computed(() => store.getters.isDarkMode)
    const themeAnimating = ref(false)

    // 切换暗色模式
    const toggleDarkMode = () => {
      themeAnimating.value = true
      store.dispatch('toggleDarkMode')

      // 动画结束后重置状态
      setTimeout(() => {
        themeAnimating.value = false
      }, 500)
    }

    // 导航项目
    const navItems = computed(() => [
      {
        title: t('Navbar.home'),
        path: '/'
      },
      {
        title: t('Navbar.introduction'),
        path: '/introduction'
      },
      {
        title: t('Navbar.about'),
        path: '/about'
      },
      {
        title: t('Navbar.community'),
        path: '/#'
      },
      {
        title: t('Navbar.help'),
        path: '/help'
      },
      {
        title: t('FundingRateHistory.title'),
        path: '/zjfl'
      }
    ])

    // 检查当前路由是否匹配导航项目路径
    const isActive = (path) => {
      return router.currentRoute.value.path === path ||
             router.currentRoute.value.path.startsWith(path + '/')
    }

    // 语言下拉菜单
    const showLanguageDropdown = ref(false)
    const currentLanguage = computed(() => locale.value)
    const availableLanguages = computed(() => {
      return Object.keys(SUPPORTED_LOCALES).map(code => ({
        code,
        name: getLocaleDisplayName(code)
      }))
    })

    const toggleLanguageDropdown = () => {
      showLanguageDropdown.value = !showLanguageDropdown.value
      if (showLanguageDropdown.value) {
        showMessageDropdown.value = false
        showQrCodeDropdown.value = false
        showUserDropdown.value = false
      }
    }

    const changeLanguage = (lang) => {
      // 使用语言工具设置语言
      setAppLocale(lang)
      // 使用 store 中的 setLocale action 来切换语言
      store.dispatch('setLocale', lang)
      locale.value = lang
      showLanguageDropdown.value = false
    }

    // 消息下拉菜单
    const showMessageDropdown = ref(false)
    const navbarMessages = computed(() => store.getters['app/navbarMessages'])
    const unreadMessageCount = computed(() => store.getters['app/unreadMessagesCount'])

    const toggleMessageDropdown = () => {
      showMessageDropdown.value = !showMessageDropdown.value
      if (showMessageDropdown.value) {
        showLanguageDropdown.value = false
        showQrCodeDropdown.value = false
        showUserDropdown.value = false
        fetchMessages()
      }
    }

    const fetchMessages = async () => {
      try {
        // 从app模块获取消息列表
        await store.dispatch('app/fetchNavbarMessages')
      } catch (error) {
        console.error('获取消息失败:', error)
      }
    }

    const markAllAsRead = async () => {
      try {
        await store.dispatch('app/markAllMessagesAsRead')
        // 重新获取消息列表
        fetchMessages()
      } catch (error) {
        console.error('标记全部已读失败:', error)
      }
    }

    // 格式化消息时间
    const formatTime = (time) => {
      const date = new Date(time)
      return date.toLocaleString()
    }

    // 二维码下拉菜单
    const showQrCodeDropdown = ref(false)
    const qrCodeTab = ref('ios')

    const toggleQrCodeDropdown = () => {
      showQrCodeDropdown.value = !showQrCodeDropdown.value
      if (showQrCodeDropdown.value) {
        showLanguageDropdown.value = false
        showMessageDropdown.value = false
        showUserDropdown.value = false
      }
    }

    // 用户下拉菜单
    const showUserDropdown = ref(false)
    const isLoggedIn = computed(() => {
      return store.getters['user/isAuthenticated']
    })
    const userName = computed(() => {
      const user = store.getters['user/currentUser']
      return user ? user.username : '用户'
    })

    const toggleUserDropdown = () => {
      showUserDropdown.value = !showUserDropdown.value
      if (showUserDropdown.value) {
        showLanguageDropdown.value = false
        showMessageDropdown.value = false
        showQrCodeDropdown.value = false
      }
    }

    const logout = async () => {
      try {
        // 调用登出action
        await store.dispatch('user/logout')
        router.push('/auth/login')
      } catch (error) {
        console.error('登出失败:', error)
      }
    }

    // 移动端菜单
    const showMobileMenu = ref(false)

    const toggleMobileMenu = () => {
      showMobileMenu.value = !showMobileMenu.value
    }

    // 消息详情模态框
    const showMessageModal = ref(false)
    const selectedMessage = ref(null)

    const openMessageDetail = async (message) => {
      showMessageDropdown.value = false
      try {
        selectedMessage.value = message
        showMessageModal.value = true

        // 如果消息未读，标记为已读
        if (message.status === 0) {
          await store.dispatch('app/markMessageAsRead', message.id)
          // 重新获取消息列表
          fetchMessages()
        }
      } catch (error) {
        console.error('获取消息详情失败:', error)
      }
    }

    const closeMessageModal = () => {
      showMessageModal.value = false
      selectedMessage.value = null
    }

    // 获取消息图标
    const getMessageIcon = (message) => {
      switch (message.type) {
        case 0:
          return 'fa fa-bell'
        case 1:
          return 'fa fa-bullhorn'
        case 2:
          return 'fa fa-exclamation-circle'
        case 3:
          return 'fa fa-chart-line'
        default:
          return 'fa fa-envelope'
      }
    }

    const handleMessageAction = () => {
      if (selectedMessage.value && selectedMessage.value.action_url) {
        closeMessageModal()
        router.push(selectedMessage.value.action_url)
      }
    }

    // 点击外部关闭下拉菜单
    const handleClickOutside = (event) => {
      if (showLanguageDropdown.value || showMessageDropdown.value ||
          showQrCodeDropdown.value || showUserDropdown.value) {
        const isOutside = !event.target.closest('.navbar-icon') &&
                          !event.target.closest('.user-dropdown') &&
                          !event.target.closest('.dropdown-menu')

        if (isOutside) {
          showLanguageDropdown.value = false
          showMessageDropdown.value = false
          showQrCodeDropdown.value = false
          showUserDropdown.value = false
        }
      }
    }

    // 消息轮询定时器
    let messageTimer = null

    // 启动消息轮询
    const startMessagePolling = () => {
      // 如果已经有定时器在运行，先清除
      if (messageTimer) {
        clearInterval(messageTimer)
      }
      // 立即获取一次消息
      fetchMessages()
      // 设置定时器，每10秒调用一次fetchMessages
      messageTimer = setInterval(() => {
        fetchMessages()
      }, 10000)
    }

    // 停止消息轮询
    const stopMessagePolling = () => {
      if (messageTimer) {
        clearInterval(messageTimer)
        messageTimer = null
      }
    }

    // 监听登录状态变化
    watch(isLoggedIn, (newValue) => {
      if (newValue) {
        // 用户已登录，启动消息轮询
        startMessagePolling()
      } else {
        // 用户已登出，停止消息轮询
        stopMessagePolling()
      }
    })

    // 获取初始数据
    onMounted(() => {
      document.addEventListener('click', handleClickOutside)

      // 如果用户已登录，启动消息轮询
      if (isLoggedIn.value) {
        startMessagePolling()
      }
    })

    onBeforeUnmount(() => {
      document.removeEventListener('click', handleClickOutside)

      // 清除定时器
      stopMessagePolling()
    })

    // logo路径动态属性
    const logoPath = ref('')

    // 更新logo路径函数
    const updateLogoPath = () => {
      const lang = locale.value
      const mode = isDarkMode.value ? 'dark' : 'light'

      if (window.innerWidth <= 768) {
        logoPath.value = logoMobile
      } else {
        if (lang === 'zh_CN') {
          logoPath.value = mode === 'dark' ? logoZhCnDark : logoZhCnLight
        } else {
          logoPath.value = mode === 'dark' ? logoEnUsDark : logoEnUsLight
        }
      }
    }

    // 监听语言、主题和屏幕尺寸变化
    watch([locale, isDarkMode], updateLogoPath)

    // 初始化logo路径
    onMounted(() => {
      updateLogoPath()
      window.addEventListener('resize', updateLogoPath)
    })

    // 组件卸载时移除监听器
    onBeforeUnmount(() => {
      window.removeEventListener('resize', updateLogoPath)
    })

    return {
      navItems,
      isActive,
      isDarkMode,
      themeAnimating,
      toggleDarkMode,
      showLanguageDropdown,
      currentLanguage,
      availableLanguages,
      toggleLanguageDropdown,
      changeLanguage,
      showMessageDropdown,
      navbarMessages,
      unreadMessageCount,
      toggleMessageDropdown,
      markAllAsRead,
      formatTime,
      showQrCodeDropdown,
      qrCodeTab,
      toggleQrCodeDropdown,
      showUserDropdown,
      isLoggedIn,
      userName,
      toggleUserDropdown,
      logout,
      showMobileMenu,
      toggleMobileMenu,
      showMessageModal,
      selectedMessage,
      openMessageDetail,
      closeMessageModal,
      getMessageIcon,
      handleMessageAction,
      logoPath
    }
  }
}
</script>

<style scoped>
@import '@/styles/components/navbar.css';
</style>
