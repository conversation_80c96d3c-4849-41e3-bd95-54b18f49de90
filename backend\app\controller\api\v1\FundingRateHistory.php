<?php
declare(strict_types=1);

namespace app\controller\api\v1;

use app\controller\api\BaseController;
use think\Response;
use think\facade\Db;
use app\validate\FundingRateHistory as FundingRateHistoryValidate;
use think\facade\Log;

/**
 * 资金费率历史控制器
 */
class FundingRateHistory extends BaseController
{
    /**
     * 获取合约列表
     */
    public function symbolList(): Response
    {        
        // 查询状态为1的币种列表
        $symbols = Db::name('funding_rate_symbol')
            ->where('status', 1)
            ->field('id, symbol_name')
            ->select()
            ->toArray();
        
        return $this->success([
            'symbols' => $symbols
        ], '获取合约列表成功');
    }
    
    /**
     * 获取历史资金费率
     */
    public function historyList(): Response
    {
        // 验证请求参数
        $validate = new FundingRateHistoryValidate();
        if (!$validate->scene('historyList')->check(request()->param())) {
            return $this->error($validate->getError());
        }
        
        // 获取参数
        $symbolId = input('symbol_id/d');
        $page = input('page/d', 0);
        $limit = input('limit/d', 0);
        $startDate = input('start_date', '');
        $endDate = input('end_date', '');
        
        // 构建查询条件
        $where = [
            'symbol_id' => $symbolId
        ];
        
        // 判断是否有分页参数
        $hasPagination = ($page > 0 && $limit > 0);
        
        // 如果没有分页参数，则使用日期条件查询
        if (!$hasPagination) {
            // 如果有时间筛选条件，添加到查询条件中
            if (!empty($startDate) && !empty($endDate)) {
                $startTimestamp = strtotime($startDate);
                $endTimestamp = strtotime($endDate) + 86399; // 加上一天减一秒，即23:59:59
                
                $where[] = ['calc_time', 'between', [$startTimestamp, $endTimestamp]];
            } elseif (!empty($startDate)) {
                $startTimestamp = strtotime($startDate);
                $where[] = ['calc_time', '>=', $startTimestamp];
            } elseif (!empty($endDate)) {
                $endTimestamp = strtotime($endDate) + 86399; // 加上一天减一秒，即23:59:59
                $where[] = ['calc_time', '<=', $endTimestamp];
            }
        }
        
        // 构建查询对象
        $query = Db::name('funding_rate_history')
            ->where($where)
            ->field('calc_time, last_funding_rate, mark_price, funding_interval_hours')
            ->order('calc_time', 'desc');
        
        // 根据是否有分页参数决定是否分页
        if ($hasPagination) {
            $historyList = $query->page($page, $limit)->select()->toArray();
            $total = Db::name('funding_rate_history')->where($where)->count();
        } else {
            $historyList = $query->select()->toArray();
            $total = count($historyList);
        }
        
        // 格式化时间戳为日期
        foreach ($historyList as &$item) {
            $item['calc_time'] = date('Y-m-d H:i:s', $item['calc_time']);
        }
        
        return $this->success([
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'history' => $historyList
        ], '获取历史资金费率成功');
    }
    /**
     * 爬取资金费率历史数据
     */
    public function fetchFundingRateHistory(): Response
    {
        try {
            // Binance API 请求配置
            $url = "https://www.binance.com/bapi/futures/v1/public/delivery/common/get-funding-rate-history";
            $headers = [
                "Content-Type: application/json"
            ];
            
            // 设置代理（如果需要）
            $proxy = "127.0.0.1:7890";
            
            // 查询funding_rate_symbol表中所有数据
            $symbols = Db::name('funding_rate_symbol')->select()->toArray();
            $successCount = 0;
            $errorCount = 0;
            $errorMessages = [];
            
            foreach ($symbols as $symbol) {
                $payload = [
                    "symbol" => $symbol['symbol_value'],
                    "page" => 1,
                    "rows" => 10
                ];
                
                // 发送 POST 请求获取数据
                $retry = 0;
                $maxRetries = 3;
                $success = false;
                
                while ($retry < $maxRetries && !$success) {
                    try {
                        // 初始化CURL
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, $url);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_POST, true);
                        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
                        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                        
                        // 设置代理
                        // curl_setopt($ch, CURLOPT_PROXY, $proxy);
                        // curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_HTTP);
                        
                        // 执行请求
                        $response = curl_exec($ch);
                        
                        // 检查是否有错误
                        if (curl_errno($ch)) {
                            throw new \Exception('Curl error: ' . curl_error($ch));
                        }
                        
                        // 关闭CURL
                        curl_close($ch);
                        
                        // 解析响应
                        $result = json_decode($response, true);
                        
                        if (isset($result['data']) && is_array($result['data'])) {
                            $data = $result['data'];
                            
                            // 将数据批量写入数据库，忽略唯一索引错误
                            foreach ($data as $item) {
                                $calcTime = intval($item['calcTime'] / 1000);
                                $fundingIntervalHours = $item['fundingIntervalHours'];
                                $lastFundingRate = $item['lastFundingRate'];
                                $markPrice = $item['markPrice'];
                                
                                // 使用INSERT IGNORE语法忽略唯一索引错误
                                Db::execute("INSERT IGNORE INTO funding_rate_history 
                                    (calc_time, symbol_id, funding_interval_hours, last_funding_rate, mark_price) 
                                    VALUES (?, ?, ?, ?, ?)", 
                                    [$calcTime, $symbol['id'], $fundingIntervalHours, $lastFundingRate, $markPrice]
                                );
                            }
                            
                            $successCount++;
                            $success = true;
                        } else {
                            throw new \Exception('Invalid response format');
                        }
                    } catch (\Exception $e) {
                        $retry++;
                        Log::error("获取资金费率历史数据失败: " . $e->getMessage() . "，正在重试...");
                        
                        if ($retry >= $maxRetries) {
                            $errorCount++;
                            $errorMessages[] = "符号 {$symbol['symbol_value']} 获取失败: " . $e->getMessage();
                        }
                        
                        // 等待一秒后重试
                        sleep(1);
                    }
                }
            }
            
            // 提交事务
            Db::commit();
            
            return $this->success([
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'error_messages' => $errorMessages
            ], '资金费率历史数据爬取完成');
            
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            Log::error("资金费率历史数据爬取失败: " . $e->getMessage());
            return $this->error("资金费率历史数据爬取失败: " . $e->getMessage());
        }
    }
}
