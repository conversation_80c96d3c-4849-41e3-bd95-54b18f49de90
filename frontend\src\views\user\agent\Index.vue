<template>
  <div class="user-page">
    <div class="page-header">
      <h1 class="page-title">代理中心</h1>
      <div class="page-subtitle">管理您的代理业务</div>
    </div>

    <div class="agent-content">
      <div class="stats-cards">

        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-l"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.level }}</div>
            <div class="stats-label">代理星级</div>
            <div class="stats-info">您是尊贵的{{ stats.level }}星代理</div>
          </div>
        </div>

        <!-- 新增钱包余额卡片 -->
        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-wallet"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.balance }}</div>
            <div class="stats-label">钱包余额</div>
            <div class="stats-info">
              <button class="btn btn-primary withdraw-btn" @click="openWithdrawModal">提现</button>
            </div>
          </div>
        </div>

        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-users"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.totalReferrals }}</div>
            <div class="stats-label">邀请人数</div>
            <div class="stats-info">您已成功邀请{{ stats.totalReferrals }}位用户注册</div>
          </div>
        </div>
        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-user-plus"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.invitedUsers }}</div>
            <div class="stats-label">有效人数</div>
            <div class="stats-info">您已成功邀请{{ stats.invitedUsers }}位有效用户</div>
          </div>
        </div>

        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-money-bill"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.totalCommission }}</div>
            <div class="stats-label">代理总收益</div>
            <div class="stats-info">您的代理业务总收益(USDT)</div>
          </div>
        </div>

        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-check-circle"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.settledCommission }}</div>
            <div class="stats-label">已交收代理收益</div>
            <div class="stats-info">已交收的代理收益金额(USDT)</div>
          </div>
        </div>

        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-clock"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.unsettledCommission }}</div>
            <div class="stats-label">未交收代理收益</div>
            <div class="stats-info">未交收的代理收益金额(USDT)</div>
          </div>
        </div>

        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-check-double"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.withdrawnAmount }}</div>
            <div class="stats-label">已提现金额</div>
            <div class="stats-info">已成功提现的金额(USDT)</div>
          </div>
        </div>

        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-spinner"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.withdrawingAmount }}</div>
            <div class="stats-label">提现中金额</div>
            <div class="stats-info">正在处理中的提现金额(USDT)</div>
          </div>
        </div>
      </div>

      <div class="agent-sections">
        <div class="agent-section">
          <h2 class="section-title">推广链接</h2>
          <div class="referral-card">
            <div class="referral-content">
              <div class="referral-link">
                <div class="link-label">您的专属推广链接：</div>
                <div class="link-value">
                  <input type="text" readonly :value="referralLink" class="form-control" />
                  <button class="btn btn-primary" @click="copyReferralLink">
                    <i class="fa fa-copy"></i> 复制
                  </button>
                </div>
              </div>
              <div class="referral-qr">
                <div class="qr-code">
                  <canvas ref="qrCanvas" alt="推广二维码" @click="toggleQRCode"></canvas>
                </div>
                <div class="qr-actions">
                  <button class="btn btn-outline-primary" @click="downloadQRCode">
                    <i class="fa fa-download"></i> 下载二维码
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="agent-section">
          <h2 class="section-title">收益概览</h2>
          <div class="chart-container">
            <canvas ref="commissionChart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- 放大二维码遮罩层 -->
    <div v-if="showFullQRCode" class="qr-overlay" @click="toggleQRCode">
      <div class="qr-overlay-content" @click.stop>
        <canvas ref="qrCanvasLarge" alt="放大推广二维码"></canvas>
      </div>
    </div>

    <!-- 提现申请弹窗 -->
    <Modal v-model="showWithdrawModal" :title="$t('WithdrawalRecords.applyWithdrawal')" @confirm="submitWithdrawApply"
      @update:modelValue="val => { if (!val) cancelWithdrawApply() }">
      <form @submit.prevent="submitWithdrawApply">
        <div class="form-group">
          <label for="balance">{{ $t('WithdrawalRecords.currentWalletBalance') }}(USDT):</label>{{ stats.balance }}
        </div>
        <div class="form-group">
          <label for="network">{{ $t('WithdrawalRecords.network') }}</label>
          <input type="text" id="network" v-model="withdrawForm.network" class="form-control" readonly />
        </div>
        <div class="form-group">
          <label for="address">{{ $t('WithdrawalRecords.address') }}</label>
          <input type="text" id="address" v-model="withdrawForm.address" class="form-control" required />
        </div>
        <div class="form-group">
          <label for="amount">{{ $t('WithdrawalRecords.amount') }}</label>
          <input type="number" id="amount" v-model.number="withdrawForm.amount" class="form-control" required min="1000"
            step="0.01" />
        </div>
        <div class="form-group">
            <label class="form-check-label">
              {{ $t('WithdrawalRecords.withdrawalCheckNotice') }}
            </label>
        </div>
      </form>
    </Modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import QRCode from 'qrcode'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import Chart from 'chart.js/auto'
import Modal from '@/components/common/Modal.vue'  // 新增导入Modal组件
import { focusInput, showError, showSuccess } from '@/utils/utils'

export default {
  name: 'AgentCenterPage',
  components: {
    Modal  // 注册Modal组件
  },
  setup() {
    const store = useStore()
    const { t } = useI18n()
    const router = useRouter()
    const commissionChart = ref(null)
    const qrCanvas = ref(null)
    const qrCanvasLarge = ref(null)
    const showFullQRCode = ref(false)

    // 统计数据
    const stats = reactive({
      level: 0,
      invitedUsers: 0,
      totalReferrals: 0,
      balance: 0, // 余额字段
      totalCommission: 0, // 代理总收益
      settledCommission: 0, // 已交收代理收益
      unsettledCommission: 0, // 未交收代理收益
      withdrawnAmount: 0, // 已提现金额
      withdrawingAmount: 0, // 提现中金额
    })

    // 推广链接
    const referralLink = ref('')

    // 提现弹窗显示状态
    const showWithdrawModal = ref(false)

    // 申请提现表单数据
    const withdrawForm = ref({
      network: 'Tron(TRC20)',
      address: '',
      amount: null
    })

    // 获取代理概览数据
    const fetchAgentData = async () => {
      try {
        const response = await store.dispatch('agent/fetchAgentOverview')

        // 更新统计数据
        if (response && response.data) {
          const overview = response.data

          // 映射数据到stats对象
          stats.level = overview.level || 0
          stats.invitedUsers = overview.invitedUsers || 0
          stats.totalReferrals = overview.totalReferrals || 0
          stats.balance = overview.balance || 0 // 获取余额
          stats.totalCommission = overview.totalCommission || 0 // 代理总收益
          stats.settledCommission = overview.settledCommission || 0 // 已交收代理收益
          stats.unsettledCommission = overview.unsettledCommission || 0 // 未交收代理收益
          stats.withdrawnAmount = overview.withdrawnAmount || 0 // 已提现金额
          stats.withdrawingAmount = overview.withdrawingAmount || 0 // 提现中金额

          // 生成推广链接
          referralLink.value = window.location.origin + '/auth/register?invitation=' + (overview.invitation || '')
        }
      } catch (error) {
        console.error('获取代理概览数据失败:', error)
        store.dispatch('showNotification', {
          type: 'error',
          title: '数据获取失败',
          message: '无法获取代理概览数据，请稍后再试'
        }, { root: true })
      }
    }

    // 格式化日期
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    // 复制推广链接
    /**
     * @function copyReferralLink
     * @description 复制推广链接到剪贴板
     */
    const copyReferralLink = () => {
      navigator.clipboard.writeText(referralLink.value).then(() => {
        showSuccess(t, '推广链接已复制到剪贴板')
      }).catch(() => {
        showError(t, '无法复制到剪贴板，请手动复制')
      })
    }

    // 生成二维码
    const generateQRCode = async () => {
      if (!qrCanvas.value || !referralLink.value) return

      try {
        await QRCode.toCanvas(qrCanvas.value, referralLink.value, {
          width: 150,
          margin: 1,
          color: {
            dark: '#000000',
            light: '#ffffff'
          }
        })
      } catch (error) {
        console.error('生成二维码失败:', error)
        showError(t, '无法生成推广二维码')
      }
    }

    // 生成放大二维码
    const generateLargeQRCode = async () => {
      if (!qrCanvasLarge.value || !referralLink.value) return

      try {
        await QRCode.toCanvas(qrCanvasLarge.value, referralLink.value, {
          width: 300,
          margin: 1,
          color: {
            dark: '#000000',
            light: '#ffffff'
          }
        })
      } catch (error) {
        console.error('生成放大二维码失败:', error)
        showError(t, '无法生成放大推广二维码')
      }
    }

    // 切换二维码放大显示
    const toggleQRCode = () => {
      showFullQRCode.value = !showFullQRCode.value
      if (showFullQRCode.value) {
        nextTick(() => {
          generateLargeQRCode()
        })
      }
    }

    // 下载二维码
    const downloadQRCode = () => {
      if (!qrCanvas.value) return

      try {
        // 创建一个临时链接
        const link = document.createElement('a')
        link.download = '推广二维码.png'
        link.href = qrCanvas.value.toDataURL('image/png')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        showSuccess(t, '推广二维码已下载')
      } catch (error) {
        console.error('下载二维码失败:', error)
        showError(t, '无法下载推广二维码')
      }
    }

    // 初始化图表
    const initChart = () => {
      const ctx = commissionChart.value.getContext('2d')

      // 模拟数据
      const labels = ['1月', '2月', '3月', '4月', '5月', '6月']
      const data = {
        labels: labels,
        datasets: [
          {
            label: '已结算收益',
            data: [350, 420, 580, 620, 750, 900],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.4,
            fill: true
          },
          {
            label: '待结算收益',
            data: [150, 180, 220, 250, 300, 350],
            borderColor: 'rgb(255, 159, 64)',
            backgroundColor: 'rgba(255, 159, 64, 0.2)',
            tension: 0.4,
            fill: true
          }
        ]
      }

      new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
          responsive: true,
          plugins: {
            legend: {
              position: 'top'
            },
            title: {
              display: true,
              text: '月度收益趋势'
            }
          }
        }
      })
    }

    // 监听推广链接变化，重新生成二维码
    watch(referralLink, (newValue) => {
      if (newValue) {
        generateQRCode()
      }
    })

    // 打开提现弹窗
    const openWithdrawModal = () => {
      showWithdrawModal.value = true
      fetchBalance()
    }

    // 关闭提现弹窗并重置表单
    const cancelWithdrawApply = () => {
      withdrawForm.value = {
        network: 'Tron(TRC20)',
        address: '',
        amount: null
      }
      showWithdrawModal.value = false
    }

    // 获取余额接口调用
    const fetchBalance = async () => {
      try {
        const profileData = await store.dispatch('user/fetchProfile')
        if (profileData && profileData.data && typeof profileData.data.balance === 'number') {
          stats.balance = profileData.data.balance
        }
      } catch (error) {
        console.error('获取余额失败:', error)
        showError(t, '无法获取余额，请稍后再试')
      }
    }

    /**
     * @function submitWithdrawApply
     * @description 提交提现申请，包含表单验证
     */
    const submitWithdrawApply = async () => {
      // 表单验证
      if (!withdrawForm.value.address) {
        showError(t, t('WithdrawalRecords.addressRequired'))
        focusInput('address')
        return
      }
      if (!withdrawForm.value.amount || withdrawForm.value.amount < 1000) {
        showError(t, t('WithdrawalRecords.amountInvalid'))
        focusInput('amount')
        return
      }
      if (withdrawForm.value.amount > stats.balance) {
        showError(t, t('WithdrawalRecords.amountExceedsBalance'))
        focusInput('amount')
        return
      }

      try {
        await store.dispatch('agent/withdrawApply', withdrawForm.value)
        cancelWithdrawApply()
        // 重新获取代理数据，更新余额
        await fetchAgentData()
        showSuccess(t, '提现申请提交成功')
        // 申请成功后跳转到提现记录页面
        router.push('/user/withdrawal-records')
      } catch (error) {
        showError(t, error.message || '提现申请失败')
      }
    }

    onMounted(() => {
      // 获取代理数据
      fetchAgentData()
      // 初始化图表
      initChart()
      // 如果已有推广链接，生成二维码
      if (referralLink.value) {
        generateQRCode()
      }
    })

    return {
      stats,
      referralLink,
      commissionChart,
      qrCanvas,
      qrCanvasLarge,
      showFullQRCode,
      formatDate,
      copyReferralLink,
      downloadQRCode,
      toggleQRCode,
      showWithdrawModal,
      withdrawForm,
      openWithdrawModal,
      cancelWithdrawApply,
      submitWithdrawApply,
      fetchBalance
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/user/common.css';
@import '@/styles/views/user/agent/index.css';
</style>
