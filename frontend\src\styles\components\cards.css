.cards-wrapper {
  width: 100%;
  margin-bottom: 1.5rem;
  display: none; /* 默认隐藏卡片 */
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--spacing-md);
}

.card-item {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.card-status {
  display: flex;
  gap: var(--spacing-sm);
}

.card-body {
  padding: var(--spacing-md);
}

.card-body .card-item {
  margin-bottom: var(--spacing-sm);
  display: flex;
  justify-content: space-between;
  background-color: transparent;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.card-body .card-item:last-child {
  margin-bottom: 0;
}

.item-label {
  color: var(--text-secondary);
}

.item-value {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.card-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: var(--spacing-sm);
}

.no-data-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-xl);
}

.no-data-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
}

.no-data-content i {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
}

.no-data-content p {
  margin-bottom: var(--spacing-md);
}

.loading-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-xl);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
}

.loading-content i {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
}

.loading-content p {
  margin-bottom: var(--spacing-md);
}

/* 响应式设计 */
@media (max-width: 991px) {
  .cards-wrapper {
    display: block; /* 在移动端显示卡片 */
  }
}
