.filter-section {
  margin-bottom: var(--spacing-lg);
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.filter-item {
  display: flex;
  flex-direction: column;
}

.filter-item label {
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.date-range {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.date-separator {
  color: var(--text-secondary);
}

.filter-actions {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-sm);
}

.collection-table-wrapper {
  overflow-x: auto;
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  margin-bottom: var(--spacing-lg);
}

.collection-table {
  width: 100%;
  border-collapse: collapse;
}

.collection-table th,
.collection-table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.collection-table th {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
}

.collection-table tr:last-child td {
  border-bottom: none;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
}

.status-completed {
  background-color: var(--success-color-light);
  color: var(--success-color);
}

.status-processing {
  background-color: var(--info-color-light);
  color: var(--info-color);
}

.status-failed {
  background-color: var(--error-color-light);
  color: var(--error-color);
}

.no-data {
  text-align: center;
  padding: var(--spacing-xl) !important;
}

.no-data-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
}

.no-data-content i {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
}

.no-data-content p {
  margin-bottom: var(--spacing-md);
}

.collection-cards {
  display: none;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.collection-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.card-body {
  padding: var(--spacing-md);
}

.card-item {
  margin-bottom: var(--spacing-sm);
  display: flex;
  justify-content: space-between;
}

.card-item:last-child {
  margin-bottom: 0;
}

.item-label {
  color: var(--text-secondary);
}

.item-value {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.no-data-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-xl);
}

.summary-section {
  margin-top: var(--spacing-lg);
}

.summary-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
}

.summary-title {
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  position: relative;
  padding-bottom: var(--spacing-xs);
}

.summary-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  border-radius: 1.5px;
}

.summary-content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
}

.summary-item {
  text-align: center;
}

.summary-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: var(--spacing-xs);
}

.summary-value {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

@media (max-width: 991px) {
  .collection-table-wrapper {
    display: none;
  }
  
  .collection-cards {
    display: grid;
  }
  
  .summary-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 767px) {
  .filter-section {
    grid-template-columns: 1fr;
  }
  
  .filter-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .filter-actions button {
    width: 100%;
  }
  
  .summary-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
}
