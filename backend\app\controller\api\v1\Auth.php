<?php
declare(strict_types=1);

namespace app\controller\api\v1;

use app\controller\api\BaseController;
use think\facade\Config;
use app\utils\Jwt;
use think\Response;
use think\facade\Db;
use think\captcha\facade\Captcha;

/**
 * 认证控制器
 */
class Auth extends BaseController
{
    /**
     * 登录
     */
    public function login(): Response
    {
        // 验证请求参数
        $validate = new \app\validate\Auth();
        if (!$validate->scene('login')->check(request()->param())) {
            return $this->error($validate->getError());
        }
        
        // 获取请求参数
        $username = input('username', '');
        $password = input('password', '');
        $remember = input('remember', false);
        
        // 查询用户
        $user = Db::name('users')
            ->where('username', $username)
            ->where('isdel', 0)
            ->where('isblack', 0)
            ->find();
        
        if (empty($user)) {
            return $this->error('用户名或密码错误');
        }
        
        // 验证密码
        if (!password_verify($password, $user['password'])) {
            // 记录登录失败次数，可以实现登录锁定功能
            return $this->error('用户名或密码错误');
        }
        
        // 更新登录信息
        Db::name('users')->where('id', $user['id'])->update([
            'lastip' => request()->ip(),
            'logintime' => date('Y-m-d H:i:s'),
            'loginsum' => $user['loginsum'] + 1,
        ]);
        
        // 根据"记住我"选项设置不同的过期时间
        $config = Config::get('jwt', []);
        $expireTime = $remember 
            ? ($config['remember_expire'] ?? 2592000) // 30天，如果没有配置则默认30天
            : ($config['expire_time'] ?? 86400);      // 默认24小时
        
        // 生成TOKEN
        $tokenData = [
            'id' => $user['id'],
            'username' => $user['username'],
            'remember' => $remember, // 记录是否是"记住我"登录
        ];
        $token = Jwt::generate($tokenData, $expireTime);
        
        // 安全起见，过滤敏感字段
        unset($user['password']);
        
        // 返回用户信息和TOKEN
        return $this->success([
            'token' => $token,
            'is_agent' => $user['is_agent']
        ]);
    }
    
    /**
     * 注册
     */
    public function register(): Response
    {
        // 验证请求参数
        $validate = new \app\validate\Auth();
        if (!$validate->scene('register')->check(request()->param())) {
            return $this->error($validate->getError());
        }
        
        // 获取请求参数
        $username = input('username', '');
        $password = input('password', '');
        // $email = input('email', '');
        $inviteCode = input('invitation', ''); // 邀请码
        
        // 处理邀请码
        $pid = 0;
        if (!empty($inviteCode)) {
            $inviter = Db::name('users')->where('invitation', $inviteCode)->find();
            if ($inviter) {
                $pid = $inviter['id'];
            }
        }
        
        // 生成随机邀请码
        $invitation = $this->generateInvitationCode();
        // 获取默认交收比例
        $default_settlement_ratio = Db::name('system_config')->where('key', 'default_settlement_ratio')->value('value');
        // 注册用户
        $userId = Db::name('users')->insertGetId([
            'username' => $username,
            'password' => password_hash($password, PASSWORD_DEFAULT),
            // 'email' => $email,
            'reg_ip' => request()->ip(),
            'invitation' => $invitation,
            'pid' => $pid,
            'lastip' => request()->ip(),
            'logintime' => date('Y-m-d H:i:s'),
            'loginsum' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'settlement_ratio' => $default_settlement_ratio,
        ]);
        
        if (!$userId) {
            return $this->error('注册失败，请稍后重试');
        }
        // 根据"记住我"选项设置不同的过期时间
        $config = Config::get('jwt', []);
        $expireTime = $config['expire_time'] ?? 86400;      // 默认24小时
        
        // 生成TOKEN
        $tokenData = [
            'id' => $userId,
            'username' => $username,
            'remember' => false, // 记录是否是"记住我"登录
        ];
        $token = Jwt::generate($tokenData, $expireTime);
        
        // 如果有上级，建立代理关系
        // if ($pid > 0) {
        //     Db::name('user_team')->insert([
        //         'uid' => $userId,
        //         'pid' => $pid,
        //         'level' => 1,
        //         'created_at' => date('Y-m-d H:i:s'),
        //         'updated_at' => date('Y-m-d H:i:s'),
        //     ]);
        // }
        
        return $this->success([
            'token' => $token,
        ], '注册成功');
    }
    
    /**
     * 生成唯一邀请码
     */
    private function generateInvitationCode($length = 8): string
    {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $code = '';
        
        for ($i = 0; $i < $length; $i++) {
            $code .= $characters[rand(0, $charactersLength - 1)];
        }
        
        // 检查邀请码是否已存在
        $exists = Db::name('users')->where('invitation', $code)->find();
        if ($exists) {
            // 如果存在，递归生成新的
            return $this->generateInvitationCode($length);
        }
        
        return $code;
    }
    
    /**
     * 获取验证码图片
     * 
     * @return Response
     */
    public function getCaptcha()
    {
        // 返回验证码图片
        return Captcha::create(null, true)['img'];
    }
    
    /**
     * 校验验证码
     * 
     * @return Response
     */
    public function checkCaptcha()
    {
        // 验证请求参数
        $validate = new \app\validate\Auth();
        if (!$validate->scene('checkCaptcha')->check(request()->param())) {
            return $this->error($validate->getError());
        }
        
        // 验证器中已处理了验证码检查
        return $this->success([], '验证成功');
    }
    
    /**
     * 忘记密码
     */
    public function forgotPassword(): Response
    {
        // 验证请求参数
        $validate = new \app\validate\Auth();
        if (!$validate->scene('forgotPassword')->check(request()->param())) {
            return $this->error($validate->getError());
        }
        
        // 获取请求参数
        $email = input('email', '');
        
        // TODO: 实现发送重置密码邮件逻辑
        
        return $this->success([], '重置密码链接已发送到您的邮箱');
    }
    
    /**
     * 重置密码
     */
    public function resetPassword(): Response
    {
        // 验证请求参数
        $validate = new \app\validate\Auth();
        if (!$validate->scene('resetPassword')->check(request()->param())) {
            return $this->error($validate->getError());
        }
        
        // 获取请求参数
        $token = input('token', '');
        $password = input('password', '');
        
        // TODO: 验证重置密码令牌
        
        // TODO: 实现重置密码逻辑
        
        return $this->success([], '密码重置成功');
    }
    
    /**
     * 退出登录
     */
    public function logout(): Response
    {
        // 在前端实现，将本地存储的token删除即可
        
        return $this->success([], '退出成功');
    }
}
