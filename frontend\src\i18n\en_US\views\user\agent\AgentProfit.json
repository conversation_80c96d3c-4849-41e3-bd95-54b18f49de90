{"AgentProfit": {"agentProfit": "Agent Profit", "subtitle": "View your agent profit details", "promoter": "Promoter", "allPromoters": "All Promoters", "dateRange": "Date Range", "to": "to", "status": "Status", "allStatus": "All Status", "profited": "Profited", "unprofited": "Unprofited", "search": "Search", "reset": "Reset", "time": "Time", "promoterAccount": "Promoter Account", "profitRatio": "Profit Ratio", "unprofitedAmount": "Unprofited Amount", "profitedAmount": "Profited Amount", "totalUnprofitedAmount": "Total Unprofited Amount", "totalProfitedAmount": "Total Profited Amount", "totalAmount": "Total Amount", "noData": "No agent profit records", "userAccount": "User Account", "userLevel": "User Level", "inviteCount": "Invite <PERSON>", "activeUsers": "Active Users", "userProfit": "User Profit", "commissionRatio": "Commission Ratio", "expectedAgentCommission": "Expected Agent Commission", "agentCommission": "Agent Commission", "unpaidProfit": "Unpaid Profit", "settledProfit": "Settled Profit", "actualAgentCommission": "Actual Agent Commission", "totalProfit": "Total Profit", "unpaidAccountCount": "Unpaid Account Count", "fetchError": "Failed to fetch agent profit data", "fetchSubUserError": "Failed to fetch sub-user data, your agent level might be insufficient", "expandAll": "Expand All", "collapseAll": "Collapse All", "subUsers": "Sub Users", "filterTitle": "Filter Conditions"}}