{"WithdrawalRecords": {"title": "Withdrawal Records", "subtitle": "View your withdrawal history and status", "noData": "No withdrawal records found", "network": "Network", "address": "<PERSON><PERSON><PERSON> Address", "amount": "<PERSON><PERSON><PERSON> Amount", "fee": "Fee", "amountReceived": "Amount Received", "originalBalance": "Original Balance", "remainingBalance": "Remaining Balance", "status": "Status", "createdAt": "Request Time", "pending": "Pending", "processing": "Processing", "completed": "Completed", "failed": "Failed", "applyWithdrawal": "Apply Withdrawal", "applySuccess": "Withdrawal application submitted successfully", "applyFailed": "Withdrawal application submission failed", "confirm": "Confirm", "view": "View", "confirmWithdrawal": "Confirm <PERSON>", "detail": "Detail", "operation": "Operation", "confirmSuccess": "<PERSON><PERSON><PERSON> confirmed successfully", "confirmFailed": "<PERSON><PERSON><PERSON> confirmation failed", "withdrawalCheckNotice": "After the withdrawal application is successful, you need to check the withdrawal address again and confirm the withdrawal to ensure the address is correct.", "currentWalletBalance": "Current Wallet Balance", "cancelled": "Cancelled", "addressRequired": "Withdrawal address is required", "amountInvalid": "Withdrawal amount must be greater than or equal to 1000", "amountExceedsBalance": "Withdrawal amount cannot exceed the current balance", "withdrawalSubmitted": "Withdrawal application submitted successfully", "withdrawalError": "Withdrawal application submission failed", "cancelSuccess": "Withdrawal cancelled successfully", "cancelFailed": "Failed to cancel withdrawal", "fetchRecordsFailed": "Failed to fetch withdrawal records", "fetchBalanceFailed": "Failed to fetch balance"}}