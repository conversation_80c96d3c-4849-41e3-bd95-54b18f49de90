<template>
  <div id="app" :class="{ 'dark-mode': isDarkMode }">
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
    
    <!-- 全局加载组件 -->
    <div class="global-loading-container" :class="{ 'active': isLoading }">
      <div class="global-loading">
        <div class="spinner"></div>
        <div class="loading-text">{{ $t('common.loading') }}</div>
      </div>
    </div>
    
    <!-- 全局通知组件 -->
    <div class="notification-container" v-if="notification">
      <div 
        class="notification" 
        :class="'notification-' + notification.type"
        @click="closeNotification"
      >
        <div class="notification-icon">
          <i :class="notificationIcon"></i>
        </div>
        <div class="notification-content">
          <div class="notification-title">{{ notification.title }}</div>
          <div class="notification-message">{{ notification.message }}</div>
        </div>
        <button class="notification-close">
          <i class="fa fa-times"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, watch } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'

export default {
  name: 'App',
  setup() {
    const store = useStore()
    const { locale } = useI18n()
    
    // 暗黑模式状态 - 从根store获取
    const isDarkMode = computed(() => store.getters.isDarkMode)
    
    // 加载状态 - 从根store获取
    const isLoading = computed(() => store.getters.isLoading)
    
    // 通知状态 - 从根store获取
    const notification = computed(() => store.getters.notification)
    
    // 根据通知类型设置图标
    const notificationIcon = computed(() => {
      if (!notification.value) return 'fa fa-bell'
      
      const type = notification.value.type
      if (type === 'success') return 'fa fa-check-circle'
      if (type === 'error') return 'fa fa-exclamation-circle'
      if (type === 'warning') return 'fa fa-exclamation-triangle'
      if (type === 'info') return 'fa fa-info-circle'
      return 'fa fa-bell'
    })
    
    // 关闭通知
    const closeNotification = () => {
      store.commit('CLEAR_NOTIFICATION')
    }
    
    // 初始化应用
    const initApp = async () => {
      // 初始化全局状态
      await store.dispatch('initGlobalState')
      
      // // 初始化用户状态
      // await store.dispatch('user/initUserState')
      
      // 根据当前语言设置html的lang属性
      document.documentElement.lang = locale.value
    }
    
    // 监听语言变化以更新html的lang属性
    watch(locale, (newLocale) => {
      document.documentElement.lang = newLocale
    })
    
    // 调用初始化方法
    initApp()
    
    return {
      isDarkMode,
      isLoading,
      notification,
      notificationIcon,
      closeNotification
    }
  }
}
</script>

<style>
/* 导入全局样式 */
@import './styles/global.css';
@import './styles/app.css';
@import './styles/themes/theme-utils.css';
@import './styles/themes/color-rgb.css';
@import './styles/components/loading.css';
</style>
