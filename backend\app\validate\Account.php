<?php
declare(strict_types=1);

namespace app\validate;

use think\Validate;
use think\facade\Db;

/**
 * 账户相关验证器
 */
class Account extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'account' => 'require|max:255|checkUserAccountUnique',
        'apikey' => 'require|max:255',
        'secretkey' => 'require|max:255',
        'passphrase' => 'max:255',
        'page' => 'integer|egt:1',
        'limit' => 'integer|between:10,9999',
        'status' => 'integer|in:0,1,2',
        'checkUserAccount' => 'checkUserAccount'
    ];

    /**
     * 错误消息
     * @var array
     */
    protected $message = [
        'account.require' => '账户名称不能为空',
        'account.max' => '账户名称不能超过255个字符',
        'account.checkUserAccountUnique' => '该账号已存在账号，不能重复添加',
        'apikey.require' => 'API密钥不能为空',
        'apikey.max' => 'API密钥不能超过255个字符',
        'secretkey.require' => '密钥不能为空',
        'secretkey.max' => '密钥不能超过255个字符',
        'passphrase.max' => '口令不能超过255个字符',
        'page.integer' => '页码必须为整数',
        'page.egt' => '页码必须大于等于1',
        'limit.integer' => '每页数量必须为整数',
        'limit.between' => '每页数量必须在1-100之间',
        'status.integer' => '状态必须为整数',
        'status.in' => '状态值不合法',
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        'index' => ['page', 'limit', 'status'],
        'create' => ['account', 'apikey', 'secretkey', 'passphrase'],
        'update' => ['account', 'apikey', 'secretkey', 'passphrase'],
        'collectFunds' => [],
        'dashboard' => [''],
        'dailyProfitRecords' => ['page', 'limit', 'startDate', 'endDate'],
        'accountStatistics' => ['page', 'limit', 'expanded']
    ];
    /**
     * 验证用户是否已经有账号
     * @param $value
     * @param $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkUserAccountUnique($value, $rule, $data = [])
    {
        // 从请求中获取当前登录用户ID
        $userId = request()->user['id'];
        // 判断是否提交id
        if (isset($data['id'])) {
            // 如果提交了id，则需要排除当前记录
            $exists = Db::name('accounts')
                ->where('user_id', $userId)
                ->where('account', $value)
                ->where('id', '<>', $data['id'])
                ->find();
        }else{
            // 如果没有提交id，则不需要排除当前记录
            $exists = Db::name('accounts')
                ->where('user_id', $userId)
                ->where('account', $value)
                ->find();
        }
        if ($exists) {
            return false;
        }
        return true;
    }


}
