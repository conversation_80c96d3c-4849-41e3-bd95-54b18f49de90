{"AccountStatistics": {"title": "<PERSON><PERSON><PERSON><PERSON> kê tài k<PERSON>n", "subtitle": "<PERSON>em thông tin thống kê tài khoản và dữ liệu chi tiết của bạn", "noData": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dữ liệu tài k<PERSON>n", "account": "<PERSON><PERSON><PERSON>", "sumIncome": "<PERSON><PERSON><PERSON> lợ<PERSON> n<PERSON>(USDT)", "allAssets": "<PERSON><PERSON>t cả tài sản(USDT)", "mainAccountAssets": "<PERSON><PERSON><PERSON> sản tài k<PERSON>n ch<PERSON>(USDT)", "mainAccountCount": "Số tài k<PERSON> ch<PERSON>", "subAccountCount": "Số tài k<PERSON>n phụ", "totalAssets": "<PERSON>ổng tài sản(USDT)", "totalProfit": "<PERSON><PERSON><PERSON> lợ<PERSON> n<PERSON>(USDT)", "uncollectedAmount": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>a thu thập(USDT)", "subAccountTotalPrincipal": "Tổng vốn tài k<PERSON>n phụ(USDT)", "completedSubAccountCount": "Số tài khoản phụ đã hoàn thành", "taskStatus": "<PERSON>r<PERSON><PERSON> thái n<PERSON> vụ", "taskStatusNotStarted": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu", "taskStatusCompleted": "<PERSON><PERSON><PERSON> th<PERSON>", "taskStatusFailed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "taskStatusInProgress": "<PERSON><PERSON> ti<PERSON>n hành"}}