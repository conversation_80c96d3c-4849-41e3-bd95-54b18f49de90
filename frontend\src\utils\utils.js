import store from '@/store'

/**
 * 根据输入框ID聚焦对应的输入框元素
 * @param {string} inputId - 输入框的DOM元素ID
 */
export function focusInput(inputId) {
  const inputElement = document.getElementById(inputId)
  if (inputElement) {
    inputElement.focus()
  }
}

/**
 * 通用的消息通知方法，调用 store.dispatch 发送错误通知
 * @param {Function} t - 国际化翻译函数
 * @param {string} message - 要显示的消息内容
 */
export function showError(t, message) {
  store.dispatch('showNotification', {
    type: 'error',
    title: t('common.error'),
    message: message
  })
}

/**
 * 通用的消息通知方法，调用 store.dispatch 发送成功通知
 * @param {Function} t - 国际化翻译函数
 * @param {string} message - 要显示的消息内容
 */
export function showSuccess(t, message) {
  store.dispatch('showNotification', {
    type: 'success',
    title: t('common.success'),
    message: message
  })
}
