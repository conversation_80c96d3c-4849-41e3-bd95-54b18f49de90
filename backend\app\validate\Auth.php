<?php
declare(strict_types=1);

namespace app\validate;

use think\Validate;
use think\facade\Db;
use think\captcha\facade\Captcha;

/**
 * 认证相关验证器
 */
class Auth extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'username' => 'require|regex:^[a-zA-Z][a-zA-Z0-9_]{3,15}$|checkUsernameUnique',
        'password' => 'require|min:6',
        'email' => 'require|email|checkEmailUnique',
        'captcha' => 'require|checkCaptcha',
        'token' => 'require',
        'invitation' => 'require|alphaNum|checkInvitation',
        'remember' => 'boolean'
    ];

    /**
     * 错误消息
     * @var array
     */
    protected $message = [
        'username.require' => '用户名不能为空',
        'username.regex' => '用户名必须以字母开头，仅包含字母、数字和下划线，长度为4-16个字符',
        'username.checkUsernameUnique' => '用户名已被占用',
        'password.require' => '密码不能为空',
        'password.min' => '密码长度不能少于8个字符',
        'email.require' => '邮箱不能为空',
        'email.email' => '邮箱格式不正确',
        'email.checkEmailUnique' => '邮箱已被注册',
        'captcha.require' => '验证码不能为空',
        'captcha.checkCaptcha' => '验证码错误',
        'token.require' => '重置令牌不能为空',
        'invitation.require' => '邀请码不能为空',
        'invitation.alphaNum' => '邀请码只能包含字母和数字',
        'invitation.checkInvitation' => '邀请码无效或不存在',
        'remember.boolean' => '记住我参数格式不正确'
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        'login' => ['username', 'password', 'captcha', 'remember'],
        'register' => ['username', 'password', 'captcha', 'invitation'],
        'forgotPassword' => ['email', 'captcha'],
        'resetPassword' => ['token', 'password'],
        'checkCaptcha' => ['captcha'],
    ];

    /**
     * 自定义验证码验证规则
     * @param mixed $value 验证码值
     * @param mixed $rule 规则值
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkCaptcha($value, $rule, $data)
    {
        if (!Captcha::check($value)) {
            return '验证码错误';
        }
        return true;
    }

    /**
     * 检查用户名是否已被占用
     * @param mixed $value 用户名
     * @param mixed $rule 规则值
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkUsernameUnique($value, $rule, $data)
    {
        // 判断未登录
        if (request()->action() == 'login') {
            return true;
        }
        // 检查用户名是否已存在
        $exists = Db::name('users')->where('username', $value)->find();
        if ($exists) {
            return false;
        }
        return true;
    }
    
    /**
     * 检查邮箱是否已被注册
     * @param mixed $value 邮箱
     * @param mixed $rule 规则值
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkEmailUnique($value, $rule, $data)
    {
        // 检查邮箱是否已存在
        $exists = Db::name('users')->where('email', $value)->find();
        if ($exists) {
            return false;
        }
        return true;
    }
    
    /**
     * 检查邀请码是否有效
     * @param mixed $value 邀请码
     * @param mixed $rule 规则值
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkInvitation($value, $rule, $data)
    {
        // 检查邀请码是否存在
        $inviter = Db::name('users')->where('invitation', $value)->find();
        if (!$inviter) {
            return false;
        }
        return true;
    }
} 
