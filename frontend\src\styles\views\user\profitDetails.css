.date-range {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.date-separator {
  color: var(--text-secondary);
}

.item-label {
  color: var(--text-secondary);
}

.item-value {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.no-data-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-xl);
}


@media (max-width: 991px) {
  .profit-table-wrapper {
    display: none;
  }
  .profit-cards {
    display: grid;
  }
  
}