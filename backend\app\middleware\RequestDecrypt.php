<?php
declare(strict_types=1);

namespace app\middleware;

use app\utils\Crypto;
use think\facade\Config;

/**
 * 请求数据解密中间件
 */
class RequestDecrypt
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure $next
     * @return \think\Response
     */
    public function handle($request, \Closure $next)
    {
        // 获取当前请求路径
        $path = $request->pathinfo();
        dump($path);
        // 检查是否需要解密
        if (!$this->shouldDecrypt($path)) {
            return $next($request);
        }

        // 获取请求数据
        $param = $request->param();
        
        // 检查是否包含加密数据
        if (isset($param['data']) && !empty($param['data'])) {
            // 解密数据
            $decryptedData = Crypto::decrypt($param['data']);
            
            // 如果解密成功并且是数组，合并到请求参数中
            if (is_array($decryptedData)) {
                // 合并解密后的数据到请求参数
                $mergedParams = array_merge($param, $decryptedData);
                // 删除原始加密数据
                unset($mergedParams['data']);
                
                // 使用bind方法设置参数
                foreach ($mergedParams as $key => $value) {
                    $request->param[$key] = $value;
                }
            }
        }
        
        return $next($request);
    }
    
    /**
     * 判断是否需要解密请求
     *
     * @param string $path
     * @return bool
     */
    protected function shouldDecrypt(string $path): bool
    {
        // 获取配置中豁免解密的路径
        $cryptoConfig = Config::get('crypto', []);
        $exemptPrefixes = $cryptoConfig['exempt_prefixes'] ?? [];
        $exemptPaths = $cryptoConfig['exempt_paths'] ?? [];
        
        // 检查是否在豁免路径中
        if (in_array("/{$path}", $exemptPaths)) {
            return false;
        }
        
        // 检查是否以豁免前缀开头
        foreach ($exemptPrefixes as $prefix) {
            if (strpos("/{$path}", $prefix) === 0) {
                return false;
            }
        }
        
        return true;
    }
} 