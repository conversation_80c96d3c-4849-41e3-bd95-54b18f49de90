.messages-container {
  padding: var(--spacing-lg);
}

.messages-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.messages-header h1 {
  margin: 0;
  font-size: var(--font-size-xl);
  color: var(--text-primary);
}

.messages-filters {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: var(--bg-card);
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow-sm);
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

.filter-group label {
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.date-range {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.date-range span {
  color: var(--text-secondary);
}

.messages-list {
  margin-bottom: var(--spacing-lg);
}

.message-item {
  display: flex;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  background-color: var(--bg-card);
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.message-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.message-item.unread {
  border-left: 3px solid var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.message-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin-right: var(--spacing-md);
  background-color: rgba(var(--primary-color-rgb), 0.1);
  border-radius: var(--border-radius-circle);
  color: var(--primary-color);
}

.message-content {
  flex: 1;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xs);
}

.message-title {
  margin: 0;
  font-size: var(--font-size-md);
  color: var(--text-primary);
}

.message-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.message-time {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.message-type,
.message-urgency {
  padding: 2px 8px;
  font-size: var(--font-size-xs);
  border-radius: var(--border-radius-sm);
}

.message-type.notification,
.message-type.type0,
.message-urgency.urgency0 {
  background-color: rgba(var(--info-color-rgb), 0.1);
  color: var(--info-color);
}

.message-type.announcement,
.message-type.type1,
.message-urgency.urgency1 {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

.message-type.accountAlert,
.message-type.type2,
.message-urgency.urgency2 {
  background-color: rgba(var(--error-color-rgb), 0.1);
  color: var(--error-color);
}

.message-type.tradeAlert,
.message-type.type3 {
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
}

.message-body {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

.no-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  background-color: var(--bg-card);
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow-sm);
  color: var(--text-secondary);
}

.no-messages i {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

/* 消息详情模态框样式 */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal-backdrop.show {
  opacity: 1;
  visibility: visible;
}

.modal {
  width: 90%;
  max-width: 600px;
  background-color: var(--bg-card);
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow-lg);
  transform: translateY(20px);
  opacity: 0;
  transition: all var(--transition-normal);
}

.modal.show {
  transform: translateY(0);
  opacity: 1;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: var(--font-size-lg);
  transition: color var(--transition-normal);
}

.modal-close:hover {
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-lg);
}

.message-modal-header {
  display: flex;
  margin-bottom: var(--spacing-md);
}

.message-modal-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  margin-right: var(--spacing-md);
  background-color: rgba(var(--primary-color-rgb), 0.1);
  border-radius: var(--border-radius-circle);
  color: var(--primary-color);
  font-size: var(--font-size-lg);
}

.message-modal-info {
  flex: 1;
}

.message-modal-info h4 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
}

.message-modal-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.message-modal-time {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.message-modal-type,
.message-modal-urgency {
  padding: 2px 8px;
  font-size: var(--font-size-xs);
  border-radius: var(--border-radius-sm);
}

.message-modal-content {
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
  color: var(--text-primary);
}

.message-modal-action {
  display: flex;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .messages-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  .filter-group {
    width: 100%;
  }
  
  .message-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .message-meta {
    margin-top: var(--spacing-xs);
  }
}
