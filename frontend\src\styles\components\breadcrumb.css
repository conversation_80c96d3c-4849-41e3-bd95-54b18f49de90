.breadcrumb {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 0;
  margin-bottom: var(--spacing-sm);
  list-style: none;
  background-color: transparent;
  border-radius: var(--border-radius-md);
  font-family: var(--font-family);
}

.breadcrumb-container {
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  transition: background-color var(--transition-normal);
}

.breadcrumb-dark {
  background-color: var(--secondary-color);
  border-bottom-color: var(--secondary-color-light);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-link {
  color: var(--text-tertiary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}

.breadcrumb-link:hover {
  color: var(--primary-color);
}

.breadcrumb-dark .breadcrumb-link {
  color: var(--text-quaternary);
}

.breadcrumb-dark .breadcrumb-link:hover {
  color: var(--primary-color);
}

.breadcrumb-separator {
  display: flex;
  align-items: center;
  color: var(--text-tertiary);
  margin: 0 var(--spacing-xs);
  font-size: var(--font-size-sm);
  opacity: 0.7;
}

.breadcrumb-dark .breadcrumb-separator {
  color: var(--text-quaternary);
}

.breadcrumb-item:last-child .breadcrumb-link {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  pointer-events: none;
}

.breadcrumb-dark .breadcrumb-item:last-child .breadcrumb-link {
  color: var(--text-light);
}

.breadcrumb-item:last-child .breadcrumb-separator {
  display: none;
}

.breadcrumb-icon {
  margin-right: var(--spacing-xs);
  font-size: var(--font-size-md);
}

.breadcrumb-home {
  color: var(--text-tertiary);
  margin-right: var(--spacing-xs);
  transition: color var(--transition-fast);
}

.breadcrumb-home:hover {
  color: var(--primary-color);
}

.breadcrumb-dark .breadcrumb-home {
  color: var(--text-quaternary);
}

.breadcrumb-dark .breadcrumb-home:hover {
  color: var(--primary-color);
}

/* Tech variant specific styles */
.breadcrumb-tech {
  background: linear-gradient(90deg, rgba(var(--bg-secondary-rgb), 0.5) 0%, rgba(var(--bg-secondary-rgb), 0.3) 100%);
  padding: 12px var(--spacing-md);
  height: 48px;
  line-height: 24px;
  border-radius: var(--border-radius-md);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: var(--spacing-sm);
  border-left: 3px solid var(--primary-color);
}

.breadcrumb-tech .breadcrumb-link {
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  line-height: 24px;
  height: 24px;
  display: inline-block;
}

.breadcrumb-tech .breadcrumb-link:hover {
  color: var(--primary-color);
  transform: translateX(2px);
}

.breadcrumb-tech .breadcrumb-separator {
  color: var(--text-tertiary);
  opacity: 0.6;
  line-height: 24px;
  height: 24px;
  display: flex;
  align-items: center;
}

.breadcrumb-tech .breadcrumb-item.active .breadcrumb-link {
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
}

/* Separator variants */
.breadcrumb-with-arrows .breadcrumb-separator i {
  font-size: var(--font-size-xs);
}

.breadcrumb-with-dividers .breadcrumb-separator {
  margin: 0 var(--spacing-sm);
}

/* Responsive styles */
@media (max-width: 767px) {
  .breadcrumb {
    padding: 0;
    margin-bottom: var(--spacing-xs);
  }

  .breadcrumb-container {
    padding: var(--spacing-xs);
  }

  .breadcrumb-link {
    font-size: var(--font-size-xs);
  }

  .breadcrumb-separator {
    font-size: var(--font-size-xs);
  }
  
  .breadcrumb-tech {
    height: 44px;
    line-height: 20px;
    padding: 12px var(--spacing-sm);
  }
  
  .breadcrumb-tech .breadcrumb-link,
  .breadcrumb-tech .breadcrumb-separator {
    height: 20px;
    line-height: 20px;
  }
}
