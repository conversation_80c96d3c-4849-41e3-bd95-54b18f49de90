<?php
declare(strict_types=1);

namespace app\controller\api\v1;

use app\controller\api\BaseController;
use think\Response;
use think\facade\Db;
use app\validate\Account as AccountValidate;
use app\utils\ApiKeyChecker;

/**
 * 账户控制器
 */
class Account extends BaseController
{
    /**
     * 获取账户列表
     */
    public function index(): Response
    {
        // 验证请求参数
        $validate = new AccountValidate();
        if (!$validate->scene('index')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        // 从token中获取当前登录用户ID
        $userId = request()->user['id'];

        // 获取分页参数
        $page = input('page/d', 1);
        $limit = input('limit/d', 10);
        $status = input('status', '');

        // 构建查询条件
        $where = [
            'user_id' => $userId,
        ];

        if ($status !== '') {
            $where['status'] = $status;
        }

        // 查询账户列表
        $accounts = Db::name('accounts')
            ->field('id,user_id,account,total_wallet_balance,sum_income,collect_wallet,created_at,updated_at,task_status,status')
            ->where($where)
            ->page($page, $limit)
            ->select()
            ->toArray();

        $todayStart = date('Y-m-d 00:00:00');
        $todayEnd = date('Y-m-d 23:59:59');
        // 获取每个账号的子账号数量和相关信息
        foreach ($accounts as &$account) {
            // 今日收益
            $account['todayProfit'] = Db::name('funding_rate_logs')
                ->where('user_id', $userId)
                ->where('account_id', $account['id'])
                ->where('time', 'between', [$todayStart, $todayEnd])
                ->sum('usdt');
            // 获取子账号数量
            $account['subAccountCount'] = Db::name('account_sub')
                ->where('account_id', $account['id'])
                ->count();
            // 完成数量
            $account['completedCount'] = Db::name('account_sub')
                ->where('account_id', $account['id'])
                ->where('task_status', 1)
                ->count();
            // 运行数量
            $account['runningCount'] = Db::name('account_sub')
            ->where('account_id', $account['id'])
            ->where('task_status', 'not in', [0, 1])
            ->count();
            $account['created_at'] = empty($account['created_at'])?'':substr($account['created_at'], 0, 16);
        }
        // 获取总数
        $total = Db::name('accounts')
            ->where($where)
            ->count();

        // 获取母账号统计数据
        // 母账号数量
        $accountCount = $total;

        // 子账号数量
        $subAccountCount = Db::name('account_sub')
            ->where('user_id', $userId)
            ->count();

        // 总资产
        $totalAssets = Db::name('accounts')
            ->where('user_id', $userId)
            ->sum('total_wallet_balance');

        // 总收益
        $totalProfit = Db::name('accounts')
            ->where('user_id', $userId)
            ->sum('sum_income');

        // 未归集资金
        $uncollectedFunds = Db::name('accounts')
            ->where('user_id', $userId)
            ->sum('collect_wallet');

        return $this->success([
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'accounts' => $accounts,
            'accountCount' => $accountCount,
            'subAccountCount' => $subAccountCount,
            'totalAssets' => $totalAssets,
            'totalProfit' => $totalProfit,
            'uncollectedFunds' => $uncollectedFunds
        ], '获取账户列表成功');
    }

    /**
     * 获取账户详情
     */
    public function read($id): Response
    {
        // 从token中获取当前登录用户ID
        $userId = request()->user['id'];

        // 查询账户信息
        $account = Db::name('accounts')
            ->field('id,user_id,account,created_at,updated_at,status')
            ->where('id', $id)
            ->where('user_id', $userId)
            ->where('isdel', 0)
            ->find();

        if (empty($account)) {
            return $this->error('账户不存在');
        }

        return $this->success($account, '获取账户详情成功');
    }

    /**
     * 添加账户
     */
    public function create(): Response
    {
        // 验证请求参数
        $validate = new AccountValidate();
        if (!$validate->scene('create')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        // 从token中获取当前登录用户ID
        $userId = request()->user['id'];

        // 获取请求参数
        $account = input('account', '');
        $apikey = input('apikey', '');
        $secretkey = input('secretkey', '');
        $passphrase = input('passphrase', '');

        if (!empty($apikey) && !empty($secretkey)) {
            // 检查apikey和secretkey是否已存在
            $existingAccount = Db::name('accounts')
                ->where('apikey', $apikey)
                ->find();

            if ($existingAccount) {
                return $this->error('API Key已存在，请使用其他API Key');
            }

            $existingAccount = Db::name('accounts')
                ->where('secretkey', $secretkey)
                ->find();

            if ($existingAccount) {
                return $this->error('Secret Key已存在，请使用其他Secret Key');
            }

            // 验证API Key
            $checkResult = ApiKeyChecker::check($apikey, $secretkey);
            if (!$checkResult['success']) {
                return $this->error('API Key验证失败: '.$checkResult['message'] ?? 'API Key验证失败');
            }
        }

        // 创建账户
        $accountId = Db::name('accounts')->insertGetId([
            'user_id' => $userId,
            'account' => $account,
            'apikey' => $apikey,
            'secretkey' => $secretkey,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'status' => 1, // 待验证状态
            'task_status' => 0 // 未开始任务
        ]);

        if (!$accountId) {
            return $this->error('账户添加失败');
        }

        return $this->success([
            'account' => [
                'id' => $accountId,
                'account' => $account,
                'total_wallet_balance' => '0',
                'collect_wallet' => '0',
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ], '账户添加成功');
    }

    /**
     * 更新账户
     */
    public function update($id): Response
    {
        // 验证请求参数
        $validate = new AccountValidate();
        if (!$validate->scene('update')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        // 从token中获取当前登录用户ID
        $userId = request()->user['id'];

        // 查询账户是否存在
        $account = Db::name('accounts')
            ->where('id', $id)
            ->where('user_id', $userId)
            ->find();

        if (empty($account)) {
            return $this->error('账户不存在');
        }

        // 获取请求参数
        $account = input('account', '');
        $apikey = input('apikey', '');
        $secretkey = input('secretkey', '');
        $passphrase = input('passphrase', '');

        // 更新账户信息
        $updated_data = [
            'updated_at' => date('Y-m-d H:i:s'),
            'status' => 1 // 更新API信息后需要重新验证
        ];

        if (!empty($account)) {
            $updated_data['account'] = $account;
        }
        if (!empty($apikey) && !empty($secretkey)) {
            // 检查apikey和secretkey是否已存在（排除当前账户）
            $existingAccount = Db::name('accounts')
                ->where('apikey', $apikey)
                ->where('id', '<>', $id)
                ->find();

            if ($existingAccount) {
                return $this->error('API Key已存在，请使用其他API Key');
            }

            $existingAccount = Db::name('accounts')
                ->where('secretkey', $secretkey)
                ->where('id', '<>', $id)
                ->find();

            if ($existingAccount) {
                return $this->error('Secret Key已存在，请使用其他Secret Key');
            }

            // 验证API Key
            $checkResult = ApiKeyChecker::check($apikey, $secretkey);
            if (!$checkResult['success']) {
                return $this->error('API Key验证失败: '.$checkResult['message'] ?? 'API Key验证失败');
            }

            $updated_data['apikey'] = $apikey;
            $updated_data['secretkey'] = $secretkey;
        }


        $result = Db::name('accounts')
            ->where('id', $id)
            ->update($updated_data);

        if ($result === false) {
            return $this->error('账户更新失败');
        }
        return $this->success([
            'account' => [
                'id' => $id,
                'account' => $account ?: $account['account'],
                'status' => 1,
            ]
        ], '账户更新成功');
    }

    /**
     * 归集资金
     */
    public function collectFunds(): Response
    {
        // 验证请求参数
        $validate = new AccountValidate();
        if (!$validate->scene('collectFunds')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        // 从token中获取当前登录用户ID
        $userId = request()->user['id'];

        // 查询用户所有子账号的未归集资金总额
        $collectedAmount = Db::name('account_sub')
            ->where('user_id', $userId)
            ->sum('collect_wallet');

        // 创建归集记录
        $recordId = Db::name('settlement_record')->insertGetId([
            'user_id' => $userId,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'profit' => $collectedAmount,
            'amount' => 0,
            'status' => 0, // 未交收状态
            'ratio' => Db::name('users')->where('id', $userId)->value('settlement_ratio') ?: 0
        ]);

        if (!$recordId) {
            return $this->error('资金归集失败');
        }

        // 更新子账号的未归集资金为0
        Db::name('account_sub')
            ->where('user_id', $userId)
            ->update([
                'collect_wallet' => 0,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

        return $this->success([
            'collectedAmount' => $collectedAmount
        ], '资金归集成功');
    }

    /**
     * 获取仪表盘数据
     */
    public function dashboard(): Response
    {

        // 从token中获取当前登录用户ID
        $userId = request()->user['id'];
        $user = Db::name('users')->where('id', $userId)->find();
        // 总资产
        $totalAssets = Db::name('accounts')
            ->where('user_id', $userId)
            ->sum('total_wallet_balance');

        // 获取今日收益
        $todayStart = date('Y-m-d 00:00:00');
        $todayEnd = date('Y-m-d 23:59:59');

        $todayProfit = Db::name('funding_rate_logs')
            ->where('user_id', $userId)
            ->where('time', 'between', [$todayStart, $todayEnd])
            ->sum('usdt');

        // 获取昨日收益，用于计算趋势
        $yesterdayStart = date('Y-m-d 00:00:00', strtotime('-1 day'));
        $yesterdayEnd = date('Y-m-d 23:59:59', strtotime('-1 day'));

        $yesterdayProfit = Db::name('funding_rate_logs')
            ->where('user_id', $userId)
            ->where('time', 'between', [$yesterdayStart, $yesterdayEnd])
            ->sum('usdt');

        // 计算今日收益趋势
        $todayProfitTrend = 0;
        if ($yesterdayProfit > 0) {
            $todayProfitTrend = (($todayProfit - $yesterdayProfit) / $yesterdayProfit) * 100;
        }

        // 获取未归集金额
        $uncollectedAmount = Db::name('accounts')
            ->where('user_id', $userId)
            ->sum('collect_wallet');

        // 获取总收益
        $totalProfit = $user['sum_income'];

        // 获取交收比例
        $settlementRatio = $user['settlement_ratio'];

        // 获取账户数量
        $accountCount = Db::name('accounts')
            ->where('user_id', $userId)
            ->count();

        // 获取子账号数量
        $subAccountCount = Db::name('account_sub')
            ->where('user_id', $userId)
            ->count();

        // 获取已完成子账号数量
        $completedSubAccountCount = Db::name('account_sub')
            ->where('user_id', $userId)
            ->where('task_status', 1)
            ->count();

        // 获取运行中账户数量
        $runningSubAccountCount = Db::name('account_sub')
        ->where('user_id', $userId)
        ->where('task_status', 'not in', [0, 1])
        ->count();
        // 获取推荐人数
        $referralCount = Db::name('users')
            ->where('pid', $userId)
            ->count();
        return $this->success([
            'totalAssets' => $totalAssets,
            'todayProfit' => $todayProfit,
            'todayProfitTrend' => round($todayProfitTrend, 2),
            'uncollectedAmount' => $uncollectedAmount,
            'totalProfit' => $totalProfit,
            'settlementRatio' => $settlementRatio,
            'accountCount' => $accountCount,
            'subAccountCount' => $subAccountCount,
            'completedSubAccountCount' => $completedSubAccountCount,
            'runningSubAccountCount' => $runningSubAccountCount,
            'referralCount' => $referralCount,
            'invitation' => $user['invitation'],
        ], '获取仪表盘数据成功');
    }

    /**
     * 获取每日收益记录
     */
    public function dailyProfitRecords(): Response
    {
        // 验证请求参数
        $validate = new AccountValidate();
        if (!$validate->scene('dailyProfitRecords')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        // 从token中获取当前登录用户ID
        $userId = request()->user['id'];
        $page = input('page/d', 1);
        $limit = input('limit/d', 10);

        // 获取时间筛选参数
        $startDate = input('startDate/s', '');
        $endDate = input('endDate/s', '');

        // 构建基础查询
        $query = Db::name('funding_rate_logs')
            ->where('user_id', $userId)
            ->field('DATE(time) as date, SUM(usdt) as profit')
            ->group('DATE(time)')
            ->order('date', 'desc');

        // 添加时间筛选条件
        if (!empty($startDate)) {
            $query->where('time', '>=', $startDate . ' 00:00:00');
        }

        if (!empty($endDate)) {
            $query->where('time', '<=', $endDate . ' 23:59:59');
        }

        // 获取总数（用于分页）
        $totalQuery = clone $query;
        $total = $totalQuery->count();

        // 获取分页数据
        $records = $query->page($page, $limit)->select()->toArray();

        // 获取子账号总本金 - account_sub表user_id=当前登录用户id的principal字段总和
        $subAccountTotalPrincipal = Db::name('account_sub')
            ->where('user_id', $userId)
            ->sum('principal');

        // 计算趋势（与前一天对比）和日利率
        foreach ($records as &$record) {
            $record['profit'] = round(floatval($record['profit']), 8);

            // 计算日利率：日收益除以子账号总本金
            if ($subAccountTotalPrincipal > 0) {
                $record['daily_rate'] = round(($record['profit'] / $subAccountTotalPrincipal) * 100, 4);
            } else {
                $record['daily_rate'] = null;
            }

            // 获取前一天的收益
            $prevDate = date('Y-m-d', strtotime($record['date'] . ' -1 day'));
            $prevProfit = Db::name('funding_rate_logs')
                ->where('user_id', $userId)
                ->where('time', 'between', [$prevDate . ' 00:00:00', $prevDate . ' 23:59:59'])
                ->sum('usdt');

            if ($prevProfit > 0) {
                $record['trend'] = round((($record['profit'] - $prevProfit) / $prevProfit) * 100, 2);
            } else {
                $record['trend'] = null;
            }
        }



        return $this->success([
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'records' => $records
        ], '获取每日收益记录成功');
    }

    /**
     * 获取账户统计数据
     */
    public function accountStatistics(): Response
    {
        // 验证请求参数
        $validate = new AccountValidate();
        if (!$validate->scene('accountStatistics')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        // 从token中获取当前登录用户ID
        $userId = request()->user['id'];
        $page = input('page/d', 1);
        $limit = input('limit/d', 10);

        // 获取展开的母账号ID列表
        $expanded = input('expanded/a', []);
        // 转换为整数数组
        $expanded = array_map('intval', array_filter($expanded));

        // 构建基础查询 - 获取母账号
        $accountQuery = Db::name('accounts')
            ->where('user_id', $userId)
            ->field('id, account, total_wallet_balance, wallet_balance, collect_wallet, sum_income, task_status, created_at, updated_at')
            ->order('created_at', 'desc');

        // 获取总数
        $total = $accountQuery->count();

        // 获取分页的母账号数据
        $accounts = $accountQuery->page($page, $limit)->select()->toArray();

        $records = [];
        foreach ($accounts as $account) {
            // 检查是否有子账号
            $subAccountCount = Db::name('account_sub')
                ->where('account_id', $account['id'])
                ->count();

            $accountRecord = [
                'id' => 'account_' . $account['id'],
                'accountId' => intval($account['id']), // 添加真实的母账号ID
                'account' => $account['account'], // 使用account字段作为显示名称
                'total_wallet_balance' => round(floatval($account['total_wallet_balance']), 8),
                'wallet_balance' => round(floatval($account['wallet_balance']), 8), // 母账号资产
                'collect_wallet' => round(floatval($account['collect_wallet']), 8),
                'sum_income' => round(floatval($account['sum_income']), 8),
                'task_status' => intval($account['task_status']), // 任务状态
                'level' => 0,
                'hasChildren' => $subAccountCount > 0,
                'expanded' => in_array(intval($account['id']), $expanded) // 使用母账号ID判断展开状态
            ];

            $records[] = $accountRecord;

            // 如果该账户被展开，获取其子账号
            if ($accountRecord['expanded'] && $subAccountCount > 0) {
                $subAccounts = Db::name('account_sub')
                    ->where('account_id', $account['id']) // 使用account_id字段关联母账号
                    ->field('id, email as account, wallet_balance, collect_wallet, sum_income, task_status, created_at, account_id')
                    ->order('created_at', 'desc')
                    ->select()
                    ->toArray();

                foreach ($subAccounts as $subAccount) {
                    $records[] = [
                        'id' => 'sub_' . $subAccount['id'],
                        'accountId' => intval($subAccount['account_id']), // 子账号的母账号ID
                        'account' => $subAccount['account'],
                        'total_wallet_balance' => round(floatval($subAccount['wallet_balance']), 8),
                        'wallet_balance' => null, // 子账号不显示母账号资产，前端显示为-
                        'collect_wallet' => round(floatval($subAccount['collect_wallet']), 8),
                        'sum_income' => round(floatval($subAccount['sum_income']), 8),
                        'task_status' => intval($subAccount['task_status']), // 任务状态
                        'level' => 1,
                        'hasChildren' => false,
                        'expanded' => false
                    ];
                }
            }
        }

        // 计算统计摘要
        // 母账户数量
        $mainAccountCount = Db::name('accounts')->where('user_id', $userId)->count();

        // 子账户数量
        $subAccountCount = Db::name('account_sub')
            ->alias('sub')
            ->leftJoin('accounts acc', 'sub.account_id = acc.id')
            ->where('acc.user_id', $userId)
            ->count();

        // 子账号总本金 - account_sub表user_id=当前登录用户id的principal字段总和
        $subAccountTotalPrincipal = Db::name('account_sub')
            ->where('user_id', $userId)
            ->sum('principal');

        // 已完成子账号数 - account_sub表user_id=当前登录用户id的task_status字段等于1的数量
        $completedSubAccountCount = Db::name('account_sub')
            ->where('user_id', $userId)
            ->where('task_status', 1)
            ->count();

        // 资产统计 - 只统计母账号数据
        $totalAssets = Db::name('accounts')->where('user_id', $userId)->sum('total_wallet_balance');
        $totalProfit = Db::name('accounts')->where('user_id', $userId)->sum('sum_income');
        $uncollectedAmount = Db::name('accounts')->where('user_id', $userId)->sum('collect_wallet');

        $summary = [
            'mainAccountCount' => intval($mainAccountCount),
            'subAccountCount' => intval($subAccountCount),
            'totalAssets' => round(floatval($totalAssets), 8),
            'totalProfit' => round(floatval($totalProfit), 8),
            'uncollectedAmount' => round(floatval($uncollectedAmount), 8),
            'subAccountTotalPrincipal' => round(floatval($subAccountTotalPrincipal), 8),
            'completedSubAccountCount' => intval($completedSubAccountCount)
        ];

        return $this->success([
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'records' => $records,
            'summary' => $summary
        ], '获取账户统计成功');
    }
}
