<template>
  <div class="user-page">
    <div class="page-header">
      <h1 class="page-title">{{ $t('AccountStatistics.title') }}</h1>
      <div class="page-subtitle">{{ $t('AccountStatistics.subtitle') }}</div>
    </div>

    <div class="account-statistics-content">
      <!-- 统计卡片 -->
      <div class="stats-cards">
        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-credit-card"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ summary.mainAccountCount }}</div>
            <div class="stats-label">{{ $t('AccountStatistics.mainAccountCount') }}</div>
          </div>
        </div>

        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-users"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ summary.subAccountCount }}</div>
            <div class="stats-label">{{ $t('AccountStatistics.subAccountCount') }}</div>
          </div>
        </div>

        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-check-circle"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ summary.completedSubAccountCount }}</div>
            <div class="stats-label">{{ $t('AccountStatistics.completedSubAccountCount') }}</div>
          </div>
        </div>

        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-money-bill"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">≈{{ summary.totalAssets }}</div>
            <div class="stats-label">{{ $t('AccountStatistics.totalAssets') }}</div>
          </div>
        </div>

        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-dollar-sign"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">≈{{ summary.subAccountTotalPrincipal }}</div>
            <div class="stats-label">{{ $t('AccountStatistics.subAccountTotalPrincipal') }}</div>
          </div>
        </div>

        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-wallet"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">≈{{ summary.uncollectedAmount }}</div>
            <div class="stats-label">{{ $t('AccountStatistics.uncollectedAmount') }}</div>
          </div>
        </div>

        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-coins"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value" :class="summary.totalProfit > 0 ? 'profit-positive' : 'profit-negative'">
              ≈{{ summary.totalProfit }}
            </div>
            <div class="stats-label">{{ $t('AccountStatistics.totalProfit') }}</div>
          </div>
        </div>
      </div>



      <!-- 数据表格 -->
      <Table :columns="tableColumns" :data="records" :no-data-text="$t('AccountStatistics.noData')"
        class="desktop-only tree-table">
        <template #account="{ item }">
          <div class="account-cell" :class="{ 'sub-account': item.level > 0 }">
            <span
              v-if="item.hasChildren"
              @click="toggleExpanded(item)"
              class="expand-icon"
              :class="{ 'expanded': item.expanded }"
            >
              <i class="fa fa-chevron-right"></i>
            </span>
            <span class="account-name" :class="{ 'sub-account-name': item.level > 0 }">{{ item.account }}</span>
          </div>
        </template>
        <template #total_wallet_balance="{ item }">
          <span>{{ item.total_wallet_balance }}</span>
        </template>
        <template #wallet_balance="{ item }">
          <span>{{ item.level === 0 ? item.wallet_balance : '-' }}</span>
        </template>
        <template #collect_wallet="{ item }">
          <span>{{ item.collect_wallet }}</span>
        </template>
        <template #sum_income="{ item }">
          <span :class="item.sum_income > 0 ? 'profit-positive' : 'profit-negative'">
            {{ item.sum_income }}
          </span>
        </template>
        <template #task_status="{ item }">
          <span class="task-status-badge" :class="'task-status-' + item.task_status">
            {{ getTaskStatusText(item.task_status) }}
          </span>
        </template>
      </Table>

      <!-- 移动端卡片视图 -->
      <Cards :columns="tableColumns" :data="records" :no-data-text="$t('AccountStatistics.noData')">
        <template #account="{ item }">
          <div class="account-cell" :class="{ 'sub-account': item.level > 0 }">
            <span
              v-if="item.hasChildren"
              @click="toggleExpanded(item)"
              class="expand-icon"
              :class="{ 'expanded': item.expanded }"
            >
              <i class="fa fa-chevron-right"></i>
            </span>
            <span class="account-name" :class="{ 'sub-account-name': item.level > 0 }">{{ item.account }}</span>
          </div>
        </template>
        <template #total_wallet_balance="{ item }">
          <span>{{ item.total_wallet_balance }}</span>
        </template>
        <template #wallet_balance="{ item }">
          <span>{{ item.level === 0 ? item.wallet_balance : '-' }}</span>
        </template>
        <template #collect_wallet="{ item }">
          <span>{{ item.collect_wallet }}</span>
        </template>
        <template #sum_income="{ item }">
          <span :class="item.sum_income > 0 ? 'profit-positive' : 'profit-negative'">
            {{ item.sum_income }}
          </span>
        </template>
        <template #task_status="{ item }">
          <span class="task-status-badge" :class="'task-status-' + item.task_status">
            {{ getTaskStatusText(item.task_status) }}
          </span>
        </template>
      </Cards>

      <!-- 分页组件 -->
      <Pagination v-if="totalRecords > 0" :total="totalRecords" :page="currentPage" :limit="limit"
        @change="handlePageChange" use-store />
    </div>
  </div>
</template>

<script>
import { computed, onMounted, reactive, ref } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import Table from '@/components/common/Table.vue'
import Pagination from '@/components/common/Pagination.vue'
import Cards from '@/components/common/Cards.vue'
import { showError } from '@/utils/utils'

export default {
  name: 'AccountStatisticsPage',
  components: {
    Table,
    Pagination,
    Cards
  },
  setup() {
    const store = useStore()
    const { t } = useI18n()



    // 表格列配置
    const tableColumns = computed(() => [
      { key: 'account', title: t('AccountStatistics.account'), align:'left' },
      { key: 'total_wallet_balance', title: t('AccountStatistics.allAssets') },
      { key: 'wallet_balance', title: t('AccountStatistics.mainAccountAssets') },
      { key: 'collect_wallet', title: t('AccountStatistics.uncollectedAmount') },
      { key: 'sum_income', title: t('AccountStatistics.sumIncome') },
      { key: 'task_status', title: t('AccountStatistics.taskStatus') }
    ])

    // 统计摘要
    const summary = reactive({
      mainAccountCount: 0,
      subAccountCount: 0,
      totalAssets: 0,
      totalProfit: 0,
      uncollectedAmount: 0,
      subAccountTotalPrincipal: 0,
      completedSubAccountCount: 0
    })

    // 展开状态管理
    const expandedItems = ref(new Set())

    const records = computed(() => store.getters['account/accountStatistics'])
    const currentPage = computed(() => store.getters['pagination/pagination'].current)
    const limit = computed(() => store.getters['pagination/pagination'].limit)
    const totalRecords = computed(() => store.getters['pagination/pagination'].total)

    // 获取任务状态文本
    const getTaskStatusText = (status) => {
      const statusMap = {
        0: t('AccountStatistics.taskStatusNotStarted'),
        1: t('AccountStatistics.taskStatusCompleted'),
        2: t('AccountStatistics.taskStatusFailed')
      }
      return statusMap[status] || t('AccountStatistics.taskStatusInProgress')
    }



    // 切换展开状态
    const toggleExpanded = (item) => {
      if (!item.hasChildren) return

      // 使用母账号的真实ID（去掉前缀）
      const accountId = item.accountId || item.id.replace('account_', '')

      if (expandedItems.value.has(accountId)) {
        expandedItems.value.delete(accountId)
        item.expanded = false
      } else {
        expandedItems.value.add(accountId)
        item.expanded = true
      }

      // 重新获取数据以更新子项显示
      fetchAccountStatistics({
        page: currentPage.value,
        limit: limit.value
      })
    }



    // 获取账户统计数据
    const fetchAccountStatistics = (params = {}) => {
      // 合并参数
      const queryParams = {
        ...params,
        expanded: Array.from(expandedItems.value)
      }

      store.dispatch('account/fetchAccountStatistics', queryParams).then(response => {
        if (response.code === 200) {
          // 更新统计摘要
          summary.mainAccountCount = response.data.summary.mainAccountCount || 0
          summary.subAccountCount = response.data.summary.subAccountCount || 0
          summary.totalAssets = response.data.summary.totalAssets || 0
          summary.totalProfit = response.data.summary.totalProfit || 0
          summary.uncollectedAmount = response.data.summary.uncollectedAmount || 0
          summary.subAccountTotalPrincipal = response.data.summary.subAccountTotalPrincipal || 0
          summary.completedSubAccountCount = response.data.summary.completedSubAccountCount || 0


        }
      }).catch(error => {
        console.error('获取账户统计失败:', error)
        showError(t, error.message || '获取账户统计失败')
      })
    }

    // 处理分页变化
    const handlePageChange = ({ page, limit: size }) => {
      if (page && size) {
        fetchAccountStatistics({ page, limit: size })
      }
    }



    onMounted(() => {
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value || 10
      }, { root: true })

      fetchAccountStatistics({
        page: 1,
        limit: limit.value || 10
      })
    })

    return {
      tableColumns,
      records,
      currentPage,
      limit,
      totalRecords,
      summary,
      getTaskStatusText,
      toggleExpanded,
      handlePageChange
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/user/common.css';
@import '@/styles/views/user/overview/accountStatistics.css';
</style>
