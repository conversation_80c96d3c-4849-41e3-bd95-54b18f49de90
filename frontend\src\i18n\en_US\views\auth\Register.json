{"Register": {"title": "Register", "subtitle": "Create your account", "username": "Username", "usernamePlaceholder": "Enter your username", "password": "Password", "passwordPlaceholder": "Enter your password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Confirm your password", "email": "Email", "emailPlaceholder": "Enter your email", "phone": "Phone", "phonePlaceholder": "Enter your phone number", "captcha": "<PERSON><PERSON>", "captchaPlaceholder": "Enter cap<PERSON>a", "invitation": "Invitation Code", "invitationPlaceholder": "Enter invitation code", "invitationRequired": "Invitation code is required", "invitationInvalid": "Invalid or non-existent invitation code", "agreement": "I have read and agree to the", "terms": "Terms of Service", "privacy": "Privacy Policy", "registerButton": "Register", "hasAccount": "Already have an account?", "loginNow": "Login now", "registerFailed": "Registration failed, please try again later", "registerSuccess": "Registration successful, please login", "usernameRequired": "Username is required", "usernameLength": "Username must be at least 3 characters", "passwordRequired": "Password is required", "passwordLength": "Password must be at least 6 characters", "confirmPasswordRequired": "Please confirm your password", "passwordNotMatch": "Passwords do not match", "passwordsNotMatch": "Passwords do not match", "emailRequired": "Email is required", "emailInvalid": "Invalid email format", "phoneRequired": "Phone number is required", "phoneInvalid": "Invalid phone number format", "captchaRequired": "Captcha is required", "captchaInvalid": "Invalid cap<PERSON>a", "captchaError": "Captcha error, please try again", "agreementRequired": "Please read and agree to the Terms of Service and Privacy Policy", "agreeTerms": "I have read and agree to the", "termsLink": "Terms of Service", "termsTitle": "Terms of Service", "termsRequired": "Please read and agree to the Terms of Service", "haveAccount": "Already have an account?"}}