{"AccountManagement": {"account": "Master Account", "accountAdded": "Account has been added", "accountManagement": "Master Account Management", "accountRemark": "Master Account <PERSON><PERSON>", "accountRemarkHint": "Used to distinguish different trading accounts", "accountUpdated": "Account information has been updated", "addAccount": "Add Master Account", "addTime": "Add Time", "agreeTerms": "I have read and agree to the", "agreeTermsError": "Please read and agree to the API Key Terms of Use", "apiTerms": "API Key Terms of Use", "cancel": "Cancel", "completedCount": "Completed Count", "edit": "Edit", "editAccount": "Edit Master Account", "noAccountData": "No Master Account Data", "operation": "Operation", "profitDetails": "Profit Details", "runningCount": "Running Count", "save": "Save", "saveAccountError": "Failed to save account", "secretkey": "API Secret", "status": "Status", "statusDisabled": "Disabled", "statusEnabled": "Enabled", "statusExpired": "Expired", "statusInactive": "Inactive", "subAccountCount": "Sub-account Count", "subtitle": "Manage your trading master accounts", "taskStatusCompleted": "Completed", "taskStatusNotRunning": "Not Running", "taskStatusRunning": "Running", "task_status": "Task Status", "totalAssets": "Total Assets", "totalProfit": "Total Profit", "todayProfit": "Today's Profit", "uncollectedFunds": "Uncollected Funds", "statistics": "Master Account Statistics", "accountCount": "Master Account Count", "totalAccountCount": "Total Master Accounts", "accountRequired": "Please enter the master account", "apikeyRequired": "Please enter the API KEY", "secretkeyRequired": "Please enter the API Secret"}}