<template>
  <nav aria-label="breadcrumb" :class="breadcrumbClass">
    <ol class="breadcrumb">
      <li 
        v-for="(item, index) in items" 
        :key="index" 
        class="breadcrumb-item"
        :class="{ 'active': isLast(index) }"
      >
        <router-link 
          v-if="!isLast(index)" 
          :to="item.to" 
          class="breadcrumb-link"
        >
          <i v-if="item.icon" :class="'fa fa-' + item.icon + ' breadcrumb-icon'"></i>
          {{ item.text }}
        </router-link>
        <span v-else class="breadcrumb-link">
          <i v-if="item.icon" :class="'fa fa-' + item.icon + ' breadcrumb-icon'"></i>
          {{ item.text }}
        </span>
        <span v-if="!isLast(index)" class="breadcrumb-separator">
          <i :class="separatorIcon"></i>
        </span>
      </li>
    </ol>
  </nav>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'Breadcrumb',
  props: {
    items: {
      type: Array,
      required: true,
      // 验证每个面包屑项必须包含text属性和有效的to属性
      validator: (items) => {
        return items.every(item => item.text && (item.to !== undefined))
      }
    },
    variant: {
      type: String,
      default: 'default',
      // 检查传入的variant值是否合法
      validator: (value) => {
        return ['default', 'with-bg', 'with-dividers', 'with-arrows', 'tech', 'help-center'].includes(value)
      }
    },
    withIcons: {
      type: Boolean,
      default: false
    },
    separatorType: {
      type: String,
      default: 'slash',
      // 确保分隔符类型是允许的值之一
      validator: (value) => {
        return ['slash', 'chevron', 'dot', 'arrow', 'none'].includes(value)
      }
    }
  },
  setup(props) {
    // 判断是否为面包屑导航的最后一项
    const isLast = (index) => {
      return index === props.items.length - 1
    }
    
    // 根据props计算面包屑的CSS类
    const breadcrumbClass = computed(() => {
      const classes = []
      
      if (props.variant === 'with-bg') {
        classes.push('breadcrumb-with-bg')
      } else if (props.variant === 'with-dividers') {
        classes.push('breadcrumb-with-dividers')
      } else if (props.variant === 'with-arrows') {
        classes.push('breadcrumb-with-arrows')
      } else if (props.variant === 'tech') {
        classes.push('breadcrumb-tech')
      } else if (props.variant === 'help-center') {
        classes.push('breadcrumb-help-center')
      }
      
      if (props.withIcons) {
        classes.push('breadcrumb-with-icons')
      }
      
      return classes.join(' ')
    })
    
    // 根据分隔符类型计算图标类名
    const separatorIcon = computed(() => {
      switch (props.separatorType) {
        case 'chevron':
          return 'fa fa-chevron-right'
        case 'arrow':
          return 'fa fa-angle-right'
        case 'dot':
          return 'fa fa-circle'
        case 'none':
          return ''
        case 'slash':
        default:
          return 'fa fa-slash'
      }
    })
    
    return {
      isLast,
      breadcrumbClass,
      separatorIcon
    }
  }
}
</script>

<style scoped>
@import '@/styles/components/breadcrumb.css';
</style>
