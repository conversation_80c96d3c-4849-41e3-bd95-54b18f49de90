<?php

return [
    // 密码加密算法
    'password_algo' => PASSWORD_DEFAULT,
    
    // 密码最小长度
    'password_min_length' => 8,
    
    // 密码复杂度要求（0-3，0=无要求，1=需要数字，2=需要大小写字母，3=需要特殊字符）
    'password_complexity' => 2,
    
    // 用户名最小长度
    'username_min_length' => 5,
    
    // 用户名正则（字母开头，只能包含字母、数字和下划线）
    'username_regex' => '/^[a-zA-Z][a-zA-Z0-9_]{4,}$/',
    
    // 邮箱是否必须验证
    'email_verification_required' => false,
    
    // 手机是否必须验证
    'phone_verification_required' => false,
    
    // 验证码长度
    'captcha_length' => 6,
    
    // 验证码有效期（秒）
    'captcha_expire' => 300,
    
    // 开放的API路径前缀（不需要验证token）
    'open_api_prefixes' => [
        'api/v1/auth',          // 认证相关
        'api/v1/funding-rate',  // 资金费率接口
        'api/v1/test/public',   // 测试公开接口
    ],
    
    // 完全开放的API路径（精确匹配，不需要验证token）
    'open_api_paths' => [
        'api/v1/user/register',  // 用户注册
        'api/v1/home/<USER>',    // 首页横幅
        'api/v1/home/<USER>',  // 首页特点
        'api/v1/agent/level-list',    // 代理等级列表
    ],
    
    // 登录尝试次数限制
    'login_attempts_limit' => 5,
    
    // 登录锁定时间（秒）
    'login_lock_time' => 1800,
]; 