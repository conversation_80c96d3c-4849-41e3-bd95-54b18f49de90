<template>
  <div class="about-page">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">关于我们</h1>
        <div class="page-subtitle">专业的量化交易团队</div>
      </div>
      
      <div class="about-content">
        <div class="row">
          <div class="col-md-12">

            <div id="company" class="about-section">
              <h2 class="section-title">关于我们的公司</h2>
              <p class="section-text">
                我们的公司致力于为全球投资者提供专业的量化交易服务，帮助他们在波动的金融市场中实现稳定回报。
              </p>
              <p class="section-text">
                我们拥有一支经验丰富的金融专家和技术专业团队，持续开发和优化交易策略，以适应不断变化的市场条件。
              </p>
            </div>
            
            <div id="values" class="about-section">
              <h2 class="section-title">我们的核心价值观</h2>
              <div class="values-list">
                <div class="value-item">
                  <div class="value-icon">
                    <i class="fa fa-shield-alt"></i>
                  </div>
                  <div class="value-content">
                    <h3 class="value-title">安全</h3>
                    <p class="value-text">我们优先考虑用户资金和数据的安全，实施多层保护措施。</p>
                  </div>
                </div>
                
                <div class="value-item">
                  <div class="value-icon">
                    <i class="fa fa-lightbulb"></i>
                  </div>
                  <div class="value-content">
                    <h3 class="value-title">创新</h3>
                    <p class="value-text">我们不断探索新技术和策略，以提高我们的交易表现。</p>
                  </div>
                </div>
                
                <div class="value-item">
                  <div class="value-icon">
                    <i class="fa fa-handshake"></i>
                  </div>
                  <div class="value-content">
                    <h3 class="value-title">诚信</h3>
                    <p class="value-text">我们以透明和诚实的方式运营，通过可靠的服务与用户建立信任。</p>
                  </div>
                </div>
                
                <div class="value-item">
                  <div class="value-icon">
                    <i class="fa fa-users"></i>
                  </div>
                  <div class="value-content">
                    <h3 class="value-title">以用户为中心</h3>
                    <p class="value-text">我们在设计平台和服务时以用户为中心，关注易用性和客户满意度。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AboutPage',
}
</script>

<style scoped>
@import '@/styles/views/about.css';
</style>
