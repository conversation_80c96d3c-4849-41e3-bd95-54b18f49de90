import { createRouter, createWebHistory } from 'vue-router'
import i18n from '../i18n'

// 静态导入布局组件
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import UserCenterLayout from '@/layouts/UserCenterLayout.vue'
import HelpLayout from '@/layouts/HelpLayout.vue'

// 路由配置
const routes = [
  {
    path: '/',
    component: DefaultLayout,
    children: [
      {
        path: '',
        name: 'Home',
        component: () => import('@/views/Home.vue'),
        meta: { title: i18n.global.t('Home.title') }
      },
      {
        path: 'introduction',
        name: 'Introduction',
        component: () => import('@/views/Introduction.vue'),
        meta: { title: i18n.global.t('Introduction.title') }
      },
      {
        path: 'about',
        name: 'About',
        component: () => import('@/views/About.vue'),
        meta: { title: i18n.global.t('About.title') }
      },
      {
        path: 'zjfl',
        name: 'FundingRateHistory',
        component: () => import('@/views/FundingRateHistory.vue'),
        meta: { title: i18n.global.t('FundingRateHistory.title') }
      }
    ]
  },
  {
    path: '/help',
    component: HelpLayout,
    children: [
      {
        path: '',
        name: 'Help',
        component: () => import('@/views/help/Index.vue'),
        meta: { title: i18n.global.t('Help.title') }
      },
      {
        path: 'fund-security',
        name: 'FundSecurity',
        component: () => import('@/views/help/FundSecurity.vue'),
        meta: { title: i18n.global.t('FundSecurity.title') }
      },
      {
        path: 'profit-model',
        name: 'ProfitModel',
        component: () => import('@/views/help/ProfitModel.vue'),
        meta: { title: i18n.global.t('ProfitModel.title') }
      },
      {
        path: 'operation-process',
        name: 'OperationProcess',
        component: () => import('@/views/help/OperationProcess.vue'),
        meta: { title: i18n.global.t('OperationProcess.title') }
      },
      {
        path: 'agent-rules',
        name: 'AgentRules',
        component: () => import('@/views/help/AgentRules.vue'),
        meta: { title: i18n.global.t('AgentRules.title') }
      }
    ]
  },
  {
    path: '/user',
    component: UserCenterLayout,
    meta: { requiresAuth: true }, // 启用身份验证检查
    children: [
      // {
      //   path: '',
      //   name: 'UserCenterLayout',
      //   redirect: { name: 'Overview' }
      // },
      {
        path: '',
        name: 'UserCenterLayout',
        component: () => import('@/views/user/overview/Index.vue'),
        meta: { title: i18n.global.t('UserCenterLayout.userCenter') }
      },
      {
        path: 'daily-profit',
        name: 'DailyProfit',
        component: () => import('@/views/user/overview/DailyProfit.vue'),
        meta: { title: i18n.global.t('DailyProfit.title') }
      },
      {
        path: 'account-statistics',
        name: 'AccountStatistics',
        component: () => import('@/views/user/overview/AccountStatistics.vue'),
        meta: { title: i18n.global.t('AccountStatistics.title') }
      },
      {
        path: 'personal-info',
        name: 'PersonalInfo',
        component: () => import('@/views/user/PersonalInfo.vue'),
        meta: { title: i18n.global.t('PersonalInfo.personalInfo') }
      },
      {
        path: 'account-management',
        name: 'AccountManagement',
        component: () => import('@/views/user/AccountManagement.vue'),
        meta: { title: i18n.global.t('AccountManagement.accountManagement') }
      },
      {
        path: 'sub-account-management',
        name: 'SubAccountManagement',
        component: () => import('@/views/user/SubAccountManagement.vue'),
        meta: { title: i18n.global.t('SubAccountManagement.title') }
      },
      {
        path: 'collection-records',
        name: 'CollectionRecords',
        component: () => import('@/views/user/CollectionRecords.vue'),
        meta: { title: i18n.global.t('CollectionRecords.title') }
      },
      {
        path: 'agent-center',
        name: 'AgentCenter',
        component: () => import('@/views/user/agent/Index.vue'),
        meta: { title: i18n.global.t('AgentCenter.title') }
      },
      {
        path: 'profit-details',
        name: 'ProfitDetails',
        component: () => import('@/views/user/ProfitDetails.vue'),
        meta: { title: i18n.global.t('ProfitDetails.title') }
      },
      {
        path: 'agent-profit',
        name: 'AgentProfit',
        component: () => import('@/views/user/agent/AgentProfit.vue'),
        meta: { title: i18n.global.t('AgentProfit.agentProfit') }
      },
      {
        path: 'withdrawal-records',
        name: 'WithdrawalRecords',
        component: () => import('@/views/user/agent/WithdrawalRecords.vue'),
        meta: { title: i18n.global.t('WithdrawalRecords.title') }
      },
      {
        path: 'commission-records',
        name: 'CommissionRecords',
        component: () => import('@/views/user/agent/CommissionRecords.vue'),
        meta: { title: i18n.global.t('CommissionRecords.title') }
      }
    ]
  },
  {
    path: '/auth',
    component: DefaultLayout,
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/views/auth/Login.vue'),
        meta: { title: i18n.global.t('Login.title'), guest: true }
      },
      {
        path: 'register',
        name: 'Register',
        component: () => import('@/views/auth/Register.vue'),
        meta: { title: i18n.global.t('Register.title'), guest: true }
      },
      {
        path: 'forgot-password',
        name: 'ForgotPassword',
        component: () => import('@/views/auth/ForgotPassword.vue'),
        meta: { title: i18n.global.t('ForgotPassword.title'), guest: true }
      }
    ]
  },
  {
    path: '/messages',
    component: DefaultLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Messages',
        component: () => import('@/views/Messages.vue'),
        meta: { title: i18n.global.t('Messages.title') }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: { title: i18n.global.t('NotFound.title') }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 导航守卫
router.beforeEach((to, from, next) => {
  // 设置文档标题
  const defaultTitle = i18n.global.t('common.webname')
  const title = to.meta.title
    ? defaultTitle + ' - ' + to.meta.title
    : defaultTitle
  document.title = title

  // 检查路由是否需要身份验证
  const isAuthenticated = localStorage.getItem('token')

  if (to.matched.some(record => record.meta.requiresAuth) && !isAuthenticated) {
    // 路由需要身份验证但用户未登录
    next({ name: 'Login', query: { redirect: to.fullPath } })
  } else {
    // 正常进行
    next()
  }
})

export default router
