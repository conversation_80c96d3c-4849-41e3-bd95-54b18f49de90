<template>
  <div class="user-page">
    <div class="page-header">
      <h1 class="page-title">{{ $t('CollectionRecords.collectionRecords') }}</h1>
      <div class="page-subtitle">{{ $t('CollectionRecords.subtitle') }}</div>
    </div>
    
    <div class="collection-content">
      <FilterComponent 
        :filters="filterConfig" 
        v-model="filters" 
        @search="applyFilters" 
        @reset="resetFilters" 
      />
      
      <div class="collection-table-wrapper">
        <table class="collection-table">
          <thead>
            <tr>
              <th>{{ $t('CollectionRecords.transactionId') }}</th>
              <th>{{ $t('CollectionRecords.account') }}</th>
              <th>{{ $t('CollectionRecords.subAccount') }}</th>
              <th>{{ $t('CollectionRecords.amount') }}</th>
              <th>{{ $t('CollectionRecords.fee') }}</th>
              <th>{{ $t('CollectionRecords.actualAmount') }}</th>
              <th>{{ $t('CollectionRecords.status') }}</th>
              <th>{{ $t('CollectionRecords.createdTime') }}</th>
              <th>{{ $t('CollectionRecords.completedTime') }}</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="record in records" :key="record.id">
              <td>{{ record.transactionId }}</td>
              <td>{{ getAccountName(record.accountId) }}</td>
              <td>{{ record.subAccountName || '-' }}</td>
              <td>{{ formatCurrency(record.amount) }}</td>
              <td>{{ formatCurrency(record.fee) }}</td>
              <td>{{ formatCurrency(record.actualAmount) }}</td>
              <td>
                <span class="status-badge" :class="getStatusClass(record.status)">
                  {{ getStatusText(record.status) }}
                </span>
              </td>
              <td>{{ formatDateTime(record.created_at) }}</td>
              <td>{{ record.completedAt ? formatDateTime(record.completedAt) : '-' }}</td>
            </tr>
            <tr v-if="records.length === 0">
              <td colspan="9" class="no-data">
                <div class="no-data-content">
                  <i class="fa fa-info-circle"></i>
                  <p>{{ $t('CollectionRecords.noData') }}</p>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div class="collection-cards">
        <div v-for="record in records" :key="record.id" class="collection-card">
          <div class="card-header">
            <div class="card-title">{{ $t('CollectionRecords.transactionId') }}: {{ record.transactionId }}</div>
            <div class="card-status">
              <span class="status-badge" :class="getStatusClass(record.status)">
                {{ getStatusText(record.status) }}
              </span>
            </div>
          </div>
          <div class="card-body">
            <div class="card-item">
              <div class="item-label">{{ $t('CollectionRecords.account') }}</div>
              <div class="item-value">{{ getAccountName(record.accountId) }}</div>
            </div>
            <div class="card-item">
              <div class="item-label">{{ $t('CollectionRecords.subAccount') }}</div>
              <div class="item-value">{{ record.subAccountName || '-' }}</div>
            </div>
            <div class="card-item">
              <div class="item-label">{{ $t('CollectionRecords.amount') }}</div>
              <div class="item-value">{{ formatCurrency(record.amount) }}</div>
            </div>
            <div class="card-item">
              <div class="item-label">{{ $t('CollectionRecords.fee') }}</div>
              <div class="item-value">{{ formatCurrency(record.fee) }}</div>
            </div>
            <div class="card-item">
              <div class="item-label">{{ $t('CollectionRecords.actualAmount') }}</div>
              <div class="item-value">{{ formatCurrency(record.actualAmount) }}</div>
            </div>
            <div class="card-item">
              <div class="item-label">{{ $t('CollectionRecords.createdTime') }}</div>
              <div class="item-value">{{ formatDateTime(record.created_at) }}</div>
            </div>
            <div class="card-item">
              <div class="item-label">{{ $t('CollectionRecords.completedTime') }}</div>
              <div class="item-value">{{ record.completedAt ? formatDateTime(record.completedAt) : '-' }}</div>
            </div>
          </div>
        </div>
        <div v-if="records.length === 0" class="no-data-card">
          <div class="no-data-content">
            <i class="fa fa-info-circle"></i>
            <p>{{ $t('CollectionRecords.noData') }}</p>
          </div>
        </div>
      </div>
      
      <div class="pagination" v-if="totalPages > 1">
        <button class="pagination-btn" :disabled="currentPage === 1" @click="changePage(currentPage - 1)">
          <i class="fa fa-chevron-left"></i>
        </button>
        <div class="pagination-pages">
          <button 
            v-for="page in displayedPages" 
            :key="page" 
            class="pagination-page" 
            :class="{ active: currentPage === page }"
            @click="changePage(page)"
          >
            {{ page }}
          </button>
        </div>
        <button class="pagination-btn" :disabled="currentPage === totalPages" @click="changePage(currentPage + 1)">
          <i class="fa fa-chevron-right"></i>
        </button>
      </div>
      
      <div class="summary-section">
        <div class="summary-card">
          <div class="summary-title">{{ $t('CollectionRecords.collectionStatistics') }}</div>
          <div class="summary-content">
            <div class="summary-item">
              <div class="summary-label">{{ $t('CollectionRecords.totalCount') }}</div>
              <div class="summary-value">{{ summary.totalCount }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">{{ $t('CollectionRecords.totalAmount') }}</div>
              <div class="summary-value">{{ formatCurrency(summary.totalAmount) }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">{{ $t('CollectionRecords.totalFee') }}</div>
              <div class="summary-value">{{ formatCurrency(summary.totalFee) }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">{{ $t('CollectionRecords.totalActualAmount') }}</div>
              <div class="summary-value">{{ formatCurrency(summary.totalActualAmount) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import FilterComponent from '@/components/common/FilterComponent.vue'

export default {
  name: 'CollectionRecordsPage',
  components: {
    FilterComponent
  },
  setup() {
    const store = useStore()
    const { t } = useI18n()
    
    // 账号列表
    const accounts = ref([
      {
        id: 1,
        name: '币安主账号'
      },
      {
        id: 2,
        name: 'OKX账号'
      }
    ])
    
    // 归集记录
    const records = ref([
      {
        id: 1,
        transactionId: 'TX123456789',
        accountId: 1,
        subAccountName: '子账号1',
        amount: 500.25,
        fee: 5.00,
        actualAmount: 495.25,
        status: 'completed',
        created_at: '2023-05-15T10:30:00Z',
        completedAt: '2023-05-15T10:35:00Z'
      },
      {
        id: 2,
        transactionId: 'TX123456790',
        accountId: 1,
        subAccountName: '子账号2',
        amount: 300.50,
        fee: 3.00,
        actualAmount: 297.50,
        status: 'completed',
        created_at: '2023-05-14T14:20:00Z',
        completedAt: '2023-05-14T14:25:00Z'
      },
      {
        id: 3,
        transactionId: 'TX123456791',
        accountId: 2,
        subAccountName: '子账号4',
        amount: 200.75,
        fee: 2.00,
        actualAmount: 198.75,
        status: 'processing',
        created_at: '2023-05-16T09:15:00Z',
        completedAt: null
      },
      {
        id: 4,
        transactionId: 'TX123456792',
        accountId: 1,
        subAccountName: null,
        amount: 1000.00,
        fee: 10.00,
        actualAmount: 990.00,
        status: 'completed',
        created_at: '2023-05-10T16:45:00Z',
        completedAt: '2023-05-10T16:50:00Z'
      },
      {
        id: 5,
        transactionId: 'TX123456793',
        accountId: 2,
        subAccountName: '子账号5',
        amount: 150.30,
        fee: 1.50,
        actualAmount: 148.80,
        status: 'failed',
        created_at: '2023-05-12T11:10:00Z',
        completedAt: '2023-05-12T11:15:00Z'
      }
    ])
    
    // 筛选条件
    const filters = reactive({
      startDate: '',
      endDate: '',
      status: '',
      accountId: ''
    })
    
    // 筛选配置
    const filterConfig = computed(() => [
      {
        type: 'dateRange',
        id: 'dateRange',
        label: t('CollectionRecords.dateRange'),
        startKey: 'startDate',
        endKey: 'endDate'
      },
      {
        type: 'select',
        id: 'statusSelect',
        label: t('CollectionRecords.status'),
        key: 'status',
        placeholder: t('CollectionRecords.allStatus'),
        options: [
          { value: 'completed', label: t('CollectionRecords.completed') },
          { value: 'processing', label: t('CollectionRecords.processing') },
          { value: 'failed', label: t('CollectionRecords.failed') }
        ]
      },
      {
        type: 'select',
        id: 'accountSelect',
        label: t('CollectionRecords.account'),
        key: 'accountId',
        placeholder: t('CollectionRecords.allAccounts'),
        options: accounts.value.map(account => ({
          value: account.id,
          text: account.name
        }))
      }
    ])
    
    // 分页
    const currentPage = ref(1)
    const limit = ref(10)
    const totalRecords = computed(() => records.value.length)
    const totalPages = computed(() => Math.ceil(totalRecords.value / limit.value))
    
    // 显示的页码
    const displayedPages = computed(() => {
      const pages = []
      const maxPages = 5
      
      if (totalPages.value <= maxPages) {
        for (let i = 1; i <= totalPages.value; i++) {
          pages.push(i)
        }
      } else {
        let startPage = Math.max(1, currentPage.value - Math.floor(maxPages / 2))
        let endPage = startPage + maxPages - 1
        
        if (endPage > totalPages.value) {
          endPage = totalPages.value
          startPage = Math.max(1, endPage - maxPages + 1)
        }
        
        for (let i = startPage; i <= endPage; i++) {
          pages.push(i)
        }
      }
      
      return pages
    })
    
    // 统计数据
    const summary = computed(() => {
      const totalCount = records.value.length
      const totalAmount = records.value.reduce((sum, record) => sum + record.amount, 0)
      const totalFee = records.value.reduce((sum, record) => sum + record.fee, 0)
      const totalActualAmount = records.value.reduce((sum, record) => sum + record.actualAmount, 0)
      
      return {
        totalCount,
        totalAmount,
        totalFee,
        totalActualAmount
      }
    })
    
    // 格式化货币
    const formatCurrency = (value) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2
      }).format(value)
    }
    
    // 格式化日期时间
    const formatDateTime = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    // 获取账号名称
    const getAccountName = (accountId) => {
      const account = accounts.value.find(a => a.id === accountId)
      return account ? account.name : '-'
    }
    
    // 获取状态样式类
    const getStatusClass = (status) => {
      switch (status) {
        case 'completed':
          return 'status-completed'
        case 'processing':
          return 'status-processing'
        case 'failed':
          return 'status-failed'
        default:
          return ''
      }
    }
    
    // 获取状态文本
    const getStatusText = (status) => {
      switch (status) {
        case 'completed':
          return t('CollectionRecords.statusCompleted')
        case 'processing':
          return t('CollectionRecords.statusProcessing')
        case 'failed':
          return t('CollectionRecords.statusFailed')
        default:
          return '-'
      }
    }
    
    // 切换页码
    const changePage = (page) => {
      currentPage.value = page
      // 这里应该调用API获取对应页码的数据
    }
    
    // 应用筛选
    const applyFilters = () => {
      // 构建请求参数
      const params = {
        page: currentPage.value,
        limit: limit.value
      }
      
      // 只添加有值的筛选条件
      if (filters.startDate) {
        params.startDate = filters.startDate
      }
      
      if (filters.endDate) {
        params.endDate = filters.endDate
      }
      
      if (filters.status) {
        params.status = filters.status
      }
      
      if (filters.accountId) {
        params.accountId = filters.accountId
      }
      
      // 这里应该调用API获取筛选后的数据
      console.log('Apply filters:', params)
      
      // 模拟API调用
      // fetchCollectionRecords(params)
    }
    
    // 重置筛选
    const resetFilters = () => {
      filters.startDate = ''
      filters.endDate = ''
      filters.status = ''
      filters.accountId = ''
      
      // 这里应该调用API获取重置后的数据
    }
    
    onMounted(() => {
      // 这里应该调用API获取账号列表和归集记录
    })
    
    return {
      accounts,
      records,
      filters,
      filterConfig,
      currentPage,
      totalPages,
      displayedPages,
      summary,
      formatCurrency,
      formatDateTime,
      getAccountName,
      getStatusClass,
      getStatusText,
      changePage,
      applyFilters,
      resetFilters
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/user/collectionRecords.css';
</style>
