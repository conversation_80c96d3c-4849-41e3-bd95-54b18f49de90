/**
 * 语言检测和设置工具
 */

// 支持的语言列表
export const SUPPORTED_LOCALES = {
  'zh_CN': 'zh_CN',
  'zh_TW': 'zh_TW',
  'zh_HK': 'zh_HK',
  'en_US': 'en_US',
  'vi_VN': 'vi_VN',
  'ko_KR': 'ko_KR',
  'th_TH': 'th_TH',
  'ms_MY': 'ms_MY'
}

// 默认语言
export const DEFAULT_LOCALE = 'en_US'

// 浏览器语言到应用语言的映射
const BROWSER_LOCALE_MAP = {
  // 中文相关
  'zh': 'zh_CN',
  'zh-CN': 'zh_CN',
  'zh-Hans': 'zh_CN',
  'zh-Hans-CN': 'zh_CN',
  'zh-TW': 'zh_TW',
  'zh-Hant': 'zh_TW',
  'zh-Hant-TW': 'zh_TW',
  'zh-HK': 'zh_HK',
  'zh-Hant-HK': 'zh_HK',
  'zh-MO': 'zh_HK',
  'zh-SG': 'zh_CN',

  // 英文相关
  'en': 'en_US',
  'en-US': 'en_US',
  'en-GB': 'en_US',
  'en-AU': 'en_US',
  'en-CA': 'en_US',
  'en-NZ': 'en_US',
  'en-ZA': 'en_US',
  'en-IE': 'en_US',
  'en-IN': 'en_US',

  // 越南语
  'vi': 'vi_VN',
  'vi-VN': 'vi_VN',

  // 韩语
  'ko': 'ko_KR',
  'ko-KR': 'ko_KR',

  // 泰语
  'th': 'th_TH',
  'th-TH': 'th_TH',

  // 马来语
  'ms': 'ms_MY',
  'ms-MY': 'ms_MY'
}

/**
 * 检测浏览器语言偏好
 * @returns {string} 匹配的应用语言代码
 */
export function detectBrowserLocale() {
  // 获取浏览器语言列表
  const browserLanguages = navigator.languages || [navigator.language || navigator.userLanguage]
  
  console.log('浏览器语言列表:', browserLanguages)
  
  // 遍历浏览器语言列表，寻找匹配的语言
  for (const browserLang of browserLanguages) {
    // 直接匹配
    if (BROWSER_LOCALE_MAP[browserLang]) {
      console.log(`直接匹配浏览器语言: ${browserLang} -> ${BROWSER_LOCALE_MAP[browserLang]}`)
      return BROWSER_LOCALE_MAP[browserLang]
    }
    
    // 尝试匹配语言代码的前缀（如 zh-CN -> zh）
    const langPrefix = browserLang.split('-')[0]
    if (BROWSER_LOCALE_MAP[langPrefix]) {
      console.log(`前缀匹配浏览器语言: ${browserLang} (${langPrefix}) -> ${BROWSER_LOCALE_MAP[langPrefix]}`)
      return BROWSER_LOCALE_MAP[langPrefix]
    }
  }
  
  console.log(`未匹配到浏览器语言，使用默认语言: ${DEFAULT_LOCALE}`)
  return DEFAULT_LOCALE
}

/**
 * 获取应用语言设置
 * 优先级：localStorage > 浏览器语言 > 默认语言
 * @returns {string} 应用语言代码
 */
export function getAppLocale() {
  // 1. 优先使用localStorage中保存的语言设置
  const savedLocale = localStorage.getItem('locale')
  if (savedLocale && SUPPORTED_LOCALES[savedLocale]) {
    console.log(`使用已保存的语言设置: ${savedLocale}`)
    return savedLocale
  }
  
  // 2. 如果没有保存的设置，检测浏览器语言
  const browserLocale = detectBrowserLocale()
  
  // 3. 保存检测到的语言设置
  localStorage.setItem('locale', browserLocale)
  console.log(`保存检测到的语言设置: ${browserLocale}`)
  
  return browserLocale
}

/**
 * 设置应用语言
 * @param {string} locale 语言代码
 */
export function setAppLocale(locale) {
  if (!SUPPORTED_LOCALES[locale]) {
    console.warn(`不支持的语言: ${locale}，使用默认语言: ${DEFAULT_LOCALE}`)
    locale = DEFAULT_LOCALE
  }
  
  localStorage.setItem('locale', locale)
  console.log(`设置应用语言: ${locale}`)
  
  return locale
}

/**
 * 验证语言代码是否有效
 * @param {string} locale 语言代码
 * @returns {boolean} 是否有效
 */
export function isValidLocale(locale) {
  return !!SUPPORTED_LOCALES[locale]
}

/**
 * 获取语言显示名称
 * @param {string} locale 语言代码
 * @returns {string} 语言显示名称
 */
export function getLocaleDisplayName(locale) {
  const displayNames = {
    'zh_CN': '简体中文',
    'zh_TW': '繁體中文(台灣)',
    'zh_HK': '繁體中文(香港)',
    'en_US': 'English',
    'vi_VN': 'Tiếng Việt',
    'ko_KR': '한국어',
    'th_TH': 'ไทย',
    'ms_MY': 'Bahasa Melayu'
  }

  return displayNames[locale] || locale
}
