.banner-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.banner-image {
    max-width: 100%;
    height: auto;
    z-index: 2;
}

.tech-circle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.1;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-light) 100%);
}

.tech-circle-1 {
    width: 200px;
    height: 200px;
    top: -50px;
    right: -50px;
    animation: pulse 4s infinite alternate;
}

.tech-circle-2 {
    width: 150px;
    height: 150px;
    bottom: -30px;
    left: 20%;
    animation: pulse 6s infinite alternate;
}

.tech-circle-3 {
    width: 100px;
    height: 100px;
    top: 30%;
    left: -20px;
    animation: pulse 5s infinite alternate;
}

.tech-line {
    position: absolute;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    height: 1px;
    opacity: 0.2;
}

.tech-line-1 {
    width: 80%;
    top: 40%;
    left: 10%;
    animation: slide 8s infinite alternate;
}

.tech-line-2 {
    width: 60%;
    bottom: 30%;
    left: 20%;
    animation: slide 6s infinite alternate-reverse;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.1;
    }

    100% {
        transform: scale(1.1);
        opacity: 0.2;
    }
}

@keyframes slide {
    0% {
        transform: translateX(-10%);
    }

    100% {
        transform: translateX(10%);
    }
}

@media (max-width: 767px) {
    .tech-circle-1 {
        width: 150px;
        height: 150px;
    }

    .tech-circle-2 {
        width: 100px;
        height: 100px;
    }

    .tech-circle-3 {
        width: 80px;
        height: 80px;
    }
}