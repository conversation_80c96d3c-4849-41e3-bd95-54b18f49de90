import api from './index'

export default {
  // Authentication
  login(credentials) {
    return api.post('/auth/login', credentials)
  },
  
  register(userData) {
    return api.post('/auth/register', userData)
  },
  
  forgotPassword(email) {
    return api.post('/auth/forgot-password', { email })
  },
  
  resetPassword(resetData) {
    return api.post('/auth/reset-password', resetData)
  },
  
  // User profile
  getProfile() {
    return api.get('/user/profile')
  },
  
  updateProfile(profileData) {
    return api.post('/user/profile', profileData)
  },
  
  changePassword(passwordData) {
    return api.post('/user/change-password', passwordData)
  },
  
  // Email and phone verification
  bindEmail(emailData) {
    return api.post('/user/bind-email', emailData)
  },
  
  sendEmailVerificationCode(email) {
    return api.post('/user/send-email-code', { email })
  },
  
  bindPhone(phoneData) {
    return api.post('/user/bind-phone', phoneData)
  },
  
  sendPhoneVerificationCode(phone) {
    return api.post('/user/send-phone-code', { phone })
  },
  
  // Captcha
  getCaptcha() {
    return api.get('/auth/captcha'+'?t='+Date.now())
  },
  
  verifyCaptcha(captchaData) {
    return api.post('/auth/verify-captcha', captchaData)
  }
}
