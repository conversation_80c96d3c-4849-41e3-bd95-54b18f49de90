export default {
  namespaced: true,
  
  state: {
    // 合约列表
    symbols: [],
    
    // 历史资金费率（表格数据）
    historyList: [],
    
    // 历史资金费率（图表数据）
    chartData: [],
    
    // 当前选中的币种ID
    currentSymbolId: null
  },
  
  getters: {
    symbols: state => state.symbols,
    historyList: state => state.historyList,
    chartData: state => state.chartData,
    currentSymbolId: state => state.currentSymbolId
  },
  
  mutations: {
    SET_SYMBOLS(state, symbols) {
      state.symbols = symbols
    },
    
    SET_HISTORY_LIST(state, historyList) {
      state.historyList = historyList
    },
    
    SET_CHART_DATA(state, chartData) {
      state.chartData = chartData
    },
    
    SET_CURRENT_SYMBOL_ID(state, symbolId) {
      state.currentSymbolId = symbolId
    }
  },
  
  actions: {
    async fetchSymbols({ commit, state }) {
      try {
        commit('SET_LOADING', true, { root: true })
        // 使用API模块
        const fundingRateApi = await import('@/api/fundingRate').then(m => m.default)
        const data = await fundingRateApi.getSymbolList()
        
        if (data.code !== 200) {
          throw new Error(data.message || '获取合约列表失败')
        }
        
        commit('SET_SYMBOLS', data.data.symbols)
        
        // 如果有合约数据且当前未选择币种，则默认选择第一个
        if (data.data.symbols && data.data.symbols.length > 0 && !state.currentSymbolId) {
          commit('SET_CURRENT_SYMBOL_ID', data.data.symbols[0].id)
        }
        
        return data
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },
    
    async fetchHistoryList({ commit, state }, params = {}) {
      try {
        commit('SET_LOADING', true, { root: true })
        
        // 确保有选中的币种ID
        if (!params.symbol_id && state.currentSymbolId) {
          params.symbol_id = state.currentSymbolId
        }
        
        // 使用API模块
        const fundingRateApi = await import('@/api/fundingRate').then(m => m.default)
        const data = await fundingRateApi.getHistoryList(params)
        
        if (data.code !== 200) {
          throw new Error(data.message || '获取历史资金费率失败')
        }
        
        commit('SET_HISTORY_LIST', data.data.history)
        
        // 使用pagination模块处理分页
        commit('pagination/SET_PAGINATION', {
          current: data.data.page,
          limit: data.data.limit,
          total: data.data.total
        }, { root: true })
        
        return data
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },
    
    async fetchChartData({ commit, state }, params = {}) {
      try {
        commit('SET_LOADING', true, { root: true })
        
        // 确保有选中的币种ID
        if (!params.symbol_id && state.currentSymbolId) {
          params.symbol_id = state.currentSymbolId
        }
        
        // 使用API模块，不传递分页参数
        const fundingRateApi = await import('@/api/fundingRate').then(m => m.default)
        const data = await fundingRateApi.getHistoryList(params)
        
        if (data.code !== 200) {
          throw new Error(data.message || '获取历史资金费率图表数据失败')
        }
        
        commit('SET_CHART_DATA', data.data.history)
        
        return data
      } catch (error) {
        throw error
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },
    
    setCurrentSymbolId({ commit }, symbolId) {
      commit('SET_CURRENT_SYMBOL_ID', symbolId)
    }
  }
}
