<template>
  <div class="user-page">
    <div class="page-header">
      <h1 class="page-title">{{ $t('CommissionRecords.title') }}</h1>
      <div class="page-subtitle">{{ $t('CommissionRecords.subtitle') }}</div>
    </div>

    <div class="commission-records-content">
      <!-- 筛选组件 -->
      <FilterComponent
        :filters="filterConfig"
        :initial-values="filterValues"
        @search="handleFilterSearch"
        @reset="handleFilterReset"
      />

      <Table :columns="tableColumns" :data="records" :no-data-text="$t('CommissionRecords.noData')"
        class="desktop-only">
        <template #username="{ item }">
          {{ item.username }}
        </template>
        <template #revenue_amount="{ item }">
          {{ item.revenue_amount }}
        </template>
        <template #agent_ratio="{ item }">
          {{ item.agent_ratio }}%
        </template>
        <template #amount="{ item }">
          {{ item.amount }}
        </template>
        <template #original_balance="{ item }">
          {{ item.original_balance }}
        </template>
        <template #remaining_balance="{ item }">
          {{ item.remaining_balance }}
        </template>
        <template #status="{ item }">
          <span :class="getStatusClass(item.status)">
            {{ getStatusText(item.status) }}
          </span>
        </template>
        <template #created_at="{ item }">
          {{ item.created_at }}
        </template>
        <template #operation="{ item }">
          <div class="action-buttons">
            <button class="btn btn-sm btn-info" @click="onViewClick(item)">
              <i class="fa fa-eye"></i> {{ $t('CommissionRecords.view') }}
            </button>
          </div>
        </template>
      </Table>

      <Cards :columns="tableColumns" :data="records" :no-data-text="$t('CommissionRecords.noData')">
        <template #username="{ item }">
          {{ item.username }}
        </template>
        <template #revenue_amount="{ item }">
          {{ item.revenue_amount }}
        </template>
        <template #agent_ratio="{ item }">
          {{ item.agent_ratio }}%
        </template>
        <template #amount="{ item }">
          {{ item.amount }}
        </template>
        <template #original_balance="{ item }">
          {{ item.original_balance }}
        </template>
        <template #remaining_balance="{ item }">
          {{ item.remaining_balance }}
        </template>
        <template #status="{ item }">
          <span :class="getStatusClass(item.status)">
            {{ getStatusText(item.status) }}
          </span>
        </template>
        <template #created_at="{ item }">
          {{ item.created_at }}
        </template>
        <template #operation="{ item }">
          <div class="action-buttons">
            <button class="btn btn-sm btn-info" @click="onViewClick(item)">
              <i class="fa fa-eye"></i> {{ $t('CommissionRecords.view') }}
            </button>
          </div>
        </template>
      </Cards>

      <Pagination v-if="totalRecords > 0" :total="totalRecords" :page="currentPage" :limit="limit"
        @change="handlePageChange" use-store />
    </div>

    <Modal v-model="showDetailModal" :title="$t('CommissionRecords.detail')"
      @update:modelValue="val => { if (!val) closeDetailModal() }">
      <div v-if="currentRecord">
        <p><strong>{{ $t('CommissionRecords.username') }}:</strong> {{ currentRecord.username }}</p>
        <p><strong>{{ $t('CommissionRecords.revenueAmount') }}:</strong> {{ currentRecord.revenue_amount }}</p>
        <p><strong>{{ $t('CommissionRecords.agentRatio') }}:</strong> {{ currentRecord.agent_ratio }}%</p>
        <p><strong>{{ $t('CommissionRecords.amount') }}:</strong> {{ currentRecord.amount }}</p>
        <p><strong>{{ $t('CommissionRecords.originalBalance') }}:</strong> {{ currentRecord.original_balance }}</p>
        <p><strong>{{ $t('CommissionRecords.remainingBalance') }}:</strong> {{ currentRecord.remaining_balance }}</p>
        <p><strong>{{ $t('CommissionRecords.status') }}:</strong> {{ getStatusText(currentRecord.status) }}</p>
        <p><strong>{{ $t('CommissionRecords.createdAt') }}:</strong> {{ currentRecord.created_at }}</p>
        <p v-if="currentRecord.settlement_record_id"><strong>{{ $t('CommissionRecords.settlementRecordId') }}:</strong> {{ currentRecord.settlement_record_id }}</p>
      </div>
    </Modal>
  </div>
</template>

<script>
import { ref, computed, onMounted, reactive } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import Table from '@/components/common/Table.vue'
import Pagination from '@/components/common/Pagination.vue'
import Cards from '@/components/common/Cards.vue'
import Modal from '@/components/common/Modal.vue'
import FilterComponent from '@/components/common/FilterComponent.vue'
import { showError } from '@/utils/utils'

export default {
  name: 'CommissionRecordsPage',
  components: {
    Table,
    Pagination,
    Cards,
    Modal,
    FilterComponent
  },
  setup() {
    const store = useStore()
    const { t } = useI18n()

    // 筛选配置
    const filterConfig = computed(() => [
      {
        type: 'select',
        id: 'status',
        label: 'CommissionRecords.status',
        key: 'status',
        placeholder: t('FilterComponent.all'),
        options: [
          { value: '0', label: t('CommissionRecords.pending') },
          { value: '1', label: t('CommissionRecords.completed') }
        ]
      },
      {
        type: 'dateRange',
        id: 'commissionDateRange',
        label: 'FilterComponent.dateRange',
        startKey: 'startDate',
        endKey: 'endDate'
      }
    ])

    // 筛选值
    const filterValues = reactive({
      status: '',
      startDate: '',
      endDate: ''
    })

    const tableColumns = computed(() => [
      { key: 'username', title: t('CommissionRecords.username') },
      { key: 'revenue_amount', title: t('CommissionRecords.revenueAmount') },
      { key: 'agent_ratio', title: t('CommissionRecords.agentRatio') },
      { key: 'amount', title: t('CommissionRecords.amount') },
      { key: 'original_balance', title: t('CommissionRecords.originalBalance') },
      { key: 'remaining_balance', title: t('CommissionRecords.remainingBalance') },
      { key: 'status', title: t('CommissionRecords.status') },
      { key: 'created_at', title: t('CommissionRecords.createdAt') },
      { key: 'operation', title: t('CommissionRecords.operation') }
    ])

    const records = computed(() => store.getters['agent/commissionRecords'])
    const currentPage = computed(() => store.getters['pagination/pagination'].current)
    const limit = computed(() => store.getters['pagination/pagination'].limit)
    const totalRecords = computed(() => store.getters['pagination/pagination'].total)

    // 查看详情弹窗显示状态
    const showDetailModal = ref(false)
    // 当前选中记录
    const currentRecord = ref(null)

    // 获取状态文本
    const getStatusText = (status) => {
      switch (parseInt(status)) {
        case 0:
          return t('CommissionRecords.pending')
        case 1:
          return t('CommissionRecords.completed')
        default:
          return '-'
      }
    }

    // 获取状态样式类
    const getStatusClass = (status) => {
      switch (parseInt(status)) {
        case 0:
          return 'status-pending'
        case 1:
          return 'status-completed'
        default:
          return ''
      }
    }

    const handlePageChange = ({ page, limit: size }) => {
      if (page && size) {
        fetchCommissionRecords({ page, limit: size })
      }
    }

    const fetchCommissionRecords = (params = {}) => {
      // 合并筛选参数
      const queryParams = {
        ...params,
        status: filterValues.status || '',
        startDate: filterValues.startDate || '',
        endDate: filterValues.endDate || ''
      }

      store.dispatch('agent/fetchCommissionRecords', queryParams).catch(error => {
        console.error('获取代理佣金记录失败:', error)
        showError(t, error.message || '获取代理佣金记录失败')
      })
    }

    // 处理筛选搜索
    const handleFilterSearch = (values) => {
      // 重置分页到第一页
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value || 10
      }, { root: true })

      // 更新筛选值
      Object.keys(values).forEach(key => {
        filterValues[key] = values[key]
      })

      // 获取数据
      fetchCommissionRecords({
        page: 1,
        limit: limit.value || 10
      })
    }

    // 处理筛选重置
    const handleFilterReset = () => {
      // 重置分页到第一页
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value || 10
      }, { root: true })

      // 重置筛选值
      filterValues.status = ''
      filterValues.startDate = ''
      filterValues.endDate = ''

      // 获取数据
      fetchCommissionRecords({
        page: 1,
        limit: limit.value || 10
      })
    }

    // 关闭查看详情弹窗
    const closeDetailModal = () => {
      showDetailModal.value = false
      currentRecord.value = null
    }

    // 点击查看按钮
    const onViewClick = (record) => {
      currentRecord.value = record
      showDetailModal.value = true
    }

    onMounted(() => {
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value || 10
      }, { root: true })

      fetchCommissionRecords({
        page: 1,
        limit: limit.value || 10
      })
    })

    return {
      tableColumns,
      records,
      currentPage,
      limit,
      totalRecords,
      handlePageChange,
      getStatusText,
      getStatusClass,
      showDetailModal,
      currentRecord,
      closeDetailModal,
      onViewClick,
      // 筛选相关
      filterConfig,
      filterValues,
      handleFilterSearch,
      handleFilterReset
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/user/common.css';
@import '@/styles/views/user/agent/CommissionRecords.css';
</style>
