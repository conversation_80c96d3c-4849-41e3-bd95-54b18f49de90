import { createStore } from 'vuex'
import app from './modules/app'
import user from './modules/user'
import account from './modules/account'
import agent from './modules/agent'
import pagination from './modules/pagination'
import fundingRate from './modules/fundingRate'
import { getAppLocale } from '@/utils/locale'

export default createStore({
  state: {
    // 全局加载状态
    loading: false,
    // 应用主题
    darkMode: localStorage.getItem('darkMode') !== null ? localStorage.getItem('darkMode') === 'true' : true,
    // 应用语言 - 使用智能语言检测
    locale: getAppLocale(),
    // 通知系统
    notification: null,
  },
  getters: {
    // 全局加载状态
    isLoading: state => state.loading,
    // 主题相关
    isDarkMode: state => state.darkMode,
    // 语言相关
    currentLocale: state => state.locale,
    notification: state => state.notification,
  },
  mutations: {
    // 全局加载状态
    SET_LOADING(state, isLoading) {
      state.loading = isLoading
    },

    // 主题相关
    SET_DARK_MODE(state, isDarkMode) {
      state.darkMode = isDarkMode
      localStorage.setItem('darkMode', isDarkMode)

      // Apply dark mode to document
      if (isDarkMode) {
        document.documentElement.classList.add('dark-mode')
      } else {
        document.documentElement.classList.remove('dark-mode')
      }
    },

    // 语言相关
    SET_LOCALE(state, locale) {
      state.locale = locale
      localStorage.setItem('locale', locale)
    },
    SET_NOTIFICATION(state, notification) {
      state.notification = notification
    },

    CLEAR_NOTIFICATION(state) {
      state.notification = null
    }
  },
  actions: {
    // 全局加载状态
    setLoading({ commit }, isLoading) {
      commit('SET_LOADING', isLoading)
    },

    // 主题相关
    toggleDarkMode({ commit, state }) {
      commit('SET_DARK_MODE', !state.darkMode)
    },

    // 语言相关
    setLocale({ commit }, locale) {
      commit('SET_LOCALE', locale)
    },
    // 显示通知
    showNotification({ commit }, notification) {
      commit('SET_NOTIFICATION', notification)

      // 5秒后自动隐藏通知
      setTimeout(() => {
        commit('CLEAR_NOTIFICATION')
      }, 5000)
    },

    // 初始化全局状态
    initGlobalState({ state, dispatch }) {
      // 如果需要，应用深色模式
      if (state.darkMode) {
        document.documentElement.classList.add('dark-mode')
      } else {
        document.documentElement.classList.remove('dark-mode')
      }
    }
  },
  modules: {
    app,
    user,
    account,
    agent,
    pagination,
    fundingRate
  }
})
