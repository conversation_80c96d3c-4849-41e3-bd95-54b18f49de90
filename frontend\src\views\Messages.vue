<template>
  <div class="messages-container">
    <div class="messages-header">
      <h1>{{ $t('Messages.title') }}</h1>
      <div class="messages-actions">
        <button class="btn btn-primary" @click="markAllAsRead">
          {{ $t('Messages.markAllAsRead') }}
        </button>
      </div>
    </div>
    
    <FilterComponent 
      :filters="filterConfig" 
      v-model="filters" 
      :initialValues="filters"
      @search="applyFilters" 
      @reset="resetFilters" 
    />
    
    <div class="messages-list">
      <div 
        v-for="message in messages" 
        :key="message.id" 
        class="message-item"
        :class="{ 'unread': message.status === 0}"
        @click="openMessageDetail(message)"
      >
        <div class="message-icon">
          <i :class="getMessageIcon(message)"></i>
        </div>
        <div class="message-content">
          <div class="message-header">
            <h3 class="message-title">{{ message.title }}</h3>
            <div class="message-meta">
              <span class="message-time">{{ formatTime(message.created_at) }}</span>
              <span 
                class="message-type"
                :class="`type${message.type}`"
              >
                {{ $t(`Messages.type${message.type}`) }}
              </span>
              <span 
                class="message-urgency"
                :class="`urgency${message.urgency}`"
              >
                {{ $t(`Messages.urgency${message.urgency}`) }}
              </span>
            </div>
          </div>
          <div class="message-body">
            {{ message.content }}
          </div>
        </div>
      </div>
      
      <div v-if="totalMessages.length === 0" class="no-messages">
        <i class="fa fa-inbox"></i>
        <p>{{ $t('Messages.empty') }}</p>
      </div>
    </div>
    
    <!-- 分页控件 -->
    <Pagination 
      v-if="totalMessages > 0" 
      :total="totalMessages" 
      :page="currentPage" 
      :page-size="limit"
      @change="handlePageChange" 
    />
    
    <!-- 消息详情模态框 -->
    <div class="modal-backdrop" :class="{ 'show': showMessageModal }" @click="closeMessageModal">
      <div class="modal" :class="{ 'show': showMessageModal }" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">{{ $t('Messages.viewDetails') }}</h3>
          <button class="modal-close" @click="closeMessageModal">
            <i class="fa fa-times"></i>
          </button>
        </div>
        <div class="modal-body" v-if="selectedMessage">
          <div class="message-modal-header">
            <div class="message-modal-icon">
              <i :class="getMessageIcon(selectedMessage)"></i>
            </div>
            <div class="message-modal-info">
              <h4>{{ selectedMessage.title }}</h4>
              <div class="message-modal-meta">
                <div class="message-modal-time">{{ formatTime(selectedMessage.created_at) }}</div>
                <div 
                  class="message-type"
                  :class="`type${selectedMessage.type}`"
                >
                  {{ $t(`Messages.type${selectedMessage.type}`) }}
                </div>
                <div 
                  class="message-urgency"
                  :class="`urgency${selectedMessage.urgency}`"
                >
                  {{ $t(`Messages.urgency${selectedMessage.urgency}`) }}
                </div>
              </div>
            </div>
          </div>
          <div class="message-modal-content">
            {{ selectedMessage.content }}
          </div>
          <div class="message-modal-action" v-if="selectedMessage.action_name">
            <button 
              class="btn btn-primary"
              @click="handleMessageAction"
            >
              {{ selectedMessage.action_name }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import Pagination from '@/components/common/Pagination.vue'
import FilterComponent from '@/components/common/FilterComponent.vue'

export default {
  name: 'Messages',
  components: {
    Pagination,
    FilterComponent
  },
  setup() {
    const router = useRouter()
    const { t } = useI18n()
    const store = useStore()
    
    // 消息列表
    const messages = computed(() => store.getters['app/messages'])
    const loading = ref(false)
    
    // 筛选条件
    const filters = reactive({
      type: '',
      status: '',
      startDate: '',
      endDate: ''
    })
    
    // 筛选配置
    const filterConfig = computed(() => [
      {
        type: 'select',
        id: 'messageType',
        label: t('Messages.type'),
        key: 'type',
        placeholder: t('Messages.all'),
        options: [
          { value: 0, text: t('Messages.type0') },
          { value: 1, text: t('Messages.type1') },
          { value: 2, text: t('Messages.type2') },
          { value: 3, text: t('Messages.type3') }
        ]
      },
      {
        type: 'select',
        id: 'messageStatus',
        label: t('Messages.status'),
        key: 'status',
        placeholder: t('Messages.all'),
        options: [
          { value: 1, text: t('Messages.read') },
          { value: 0, text: t('Messages.unread') }
        ]
      },
      {
        type: 'dateRange',
        id: 'messageDateRange',
        label: t('Messages.dateRange'),
        startKey: 'startDate',
        endKey: 'endDate'
      }
    ])
    
    // 分页
    const currentPage = computed(() => store.getters['pagination/pagination'].current)
    const limit = computed(() => store.getters['pagination/pagination'].limit)
    const totalMessages = computed(() => store.getters['pagination/pagination'].total)
    
    // 计算总页数
    const totalPages = computed(() => {
      return Math.ceil(totalMessages.value / limit.value)
    })
    
    
    // 获取消息列表
    const fetchMessages = async (params = {}) => {
      try {
        loading.value = true
        
        // 构建请求参数
        const requestParams = {
          page: params.page || currentPage.value,
          limit: params.limit || limit.value,
          ...filters
        }
        

        
        // 从message模块获取消息列表
        await store.dispatch('app/fetchMessages', requestParams)
      } catch (error) {
        console.error('Failed to fetch messages:', error)
      } finally {
        loading.value = false
      }
    }
    
    // 应用筛选
    const applyFilters = (filterParams) => {
      // 更新本地筛选条件
      Object.keys(filterParams).forEach(key => {
        if (filterParams[key] !== undefined) {
          filters[key] = filterParams[key]
        }
      })
      
      // 重新加载消息列表，使用当前页码
      fetchMessages({
        page: 1, // 筛选时重置为第一页
        limit: limit.value
      })
    }
    
    // 重置筛选
    const resetFilters = () => {
      // 重置所有筛选条件
      filters.type = ''
      filters.status = ''
      filters.startDate = ''
      filters.endDate = ''
      
      // 重新加载消息列表
      fetchMessages({
        page: 1,
        limit: limit.value
      })
    }
    
    // 处理分页变化
    const handlePageChange = ({ page, limit: size }) => {
      if (page && size) {
        store.dispatch('app/fetchMessages', {
          page,
          limit: size
        })
      }
    }
    
    // 将所有消息标记为已读
    const markAllAsRead = async () => {
      try {
        await store.dispatch('app/markAllMessagesAsRead')
      } catch (error) {
        store.dispatch('showNotification', {
          type: 'error',
          message: t('Messages.markAllAsRead') + t('common.error')
        })
      }
    }
    
    // 消息详情模态框
    const showMessageModal = ref(false)
    const selectedMessage = ref(null)
    
    // 打开消息详情
    const openMessageDetail = async (message) => {
      selectedMessage.value = message
      showMessageModal.value = true
      
      // 如果消息未读，标记为已读
      if (message.status === 0) {
        await store.dispatch('app/markMessageAsRead', message.id)
        // 重新获取消息列表
        fetchMessages()
      }
    }
    
    // 关闭消息详情
    const closeMessageModal = () => {
      showMessageModal.value = false
      selectedMessage.value = null
    }
    
    // 处理消息操作
    const handleMessageAction = () => {
      if (selectedMessage.value && selectedMessage.value.action_url) {
        closeMessageModal()
        router.push(selectedMessage.value.action_url)
      }
    }
    
    // 获取消息图标
    const getMessageIcon = (message) => {
      switch (message.type) {
        case 0:
          return 'fa fa-bell'
        case 1:
          return 'fa fa-bullhorn'
        case 2:
          return 'fa fa-exclamation-circle'
        case 3:
          return 'fa fa-chart-line'
        default:
          return 'fa fa-envelope'
      }
    }
    
    // 格式化时间
    const formatTime = (time) => {
      const date = new Date(time)
      return date.toLocaleString()
    }
    
    // 初始化
    onMounted(() => {
      
      // 从 store 获取消息列表，使用重置后的分页参数
      fetchMessages({
        page: 1,
        limit: limit.value
      })
    })
    
    return {
      messages,
      loading,
      filters,
      filterConfig,
      currentPage,
      limit,
      totalMessages,
      totalPages,
      handlePageChange,
      markAllAsRead,
      showMessageModal,
      selectedMessage,
      openMessageDetail,
      closeMessageModal,
      handleMessageAction,
      getMessageIcon,
      formatTime,
      applyFilters,
      resetFilters
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/messages.css';
</style>
