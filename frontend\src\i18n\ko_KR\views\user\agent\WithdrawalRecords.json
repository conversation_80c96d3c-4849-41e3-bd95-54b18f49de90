﻿{
  "WithdrawalRecords": {
    "title": "출금 기록",
    "subtitle": "출금 내역과 상태를 확인하세요",
    "noData": "출금 기록을 찾을 수 없습니다",
    "network": "토큰 네트워크",
    "address": "출금 주소",
    "amount": "출금 금액",
    "fee": "수수료",
    "amountReceived": "입금 금액",
    "originalBalance": "원래 잔액",
    "remainingBalance": "남은 잔액",
    "status": "상태",
    "createdAt": "신청 시간",
    "pending": "확인 대기",
    "processing": "처리 중",
    "completed": "완료됨",
    "failed": "실패",
    "applyWithdrawal": "출금 신청",
    "applySuccess": "출금 신청 제출 성공",
    "applyFailed": "출금 신청 제출 실패",
    "confirm": "확인",
    "view": "보기",
    "confirmWithdrawal": "출금 확인",
    "detail": "상세",
    "operation": "작업",
    "confirmSuccess": "출금 확인 성공",
    "confirmFailed": "출금 확인 실패",
    "withdrawalCheckNotice": "출금 신청 성공 후, 출금 주소를 다시 확인하고 출금을 확인하여 출금 주소가 정확한지 확인해야 합니다.",
    "currentWalletBalance": "현재 지갑 잔액",
    "cancelled": "취소됨",
    "addressRequired": "출금 주소는 필수입니다",
    "amountInvalid": "출금 금액은 1000 이상이어야 합니다",
    "amountExceedsBalance": "출금 금액이 현재 잔액을 초과할 수 없습니다",
    "withdrawalSubmitted": "출금 신청 제출 성공",
    "withdrawalError": "출금 신청 제출 실패",
    "cancelSuccess": "출금 취소 성공",
    "cancelFailed": "출금 취소 실패",
    "fetchRecordsFailed": "출금 기록 가져오기 실패",
    "fetchBalanceFailed": "잔액 가져오기 실패"
  }
}
