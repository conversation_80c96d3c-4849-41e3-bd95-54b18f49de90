<?php
declare(strict_types=1);

namespace app\utils;

use think\Response;
use think\facade\Config;

/**
 * 响应处理工具类
 * 提供统一的数据返回方法
 */
class ResponseHelper
{
    /**
     * 返回成功响应
     * 
     * @param mixed $data 响应数据
     * @param string $msg 响应消息
     * @param int $code 响应状态码
     * @return Response
     */
    public static function success($data = [], string $msg = '操作成功', int $code = 200): Response
    {
        return self::response($code, $msg, $data);
    }
    
    /**
     * 返回错误响应
     * 
     * @param string $msg 错误消息
     * @param int $code 错误状态码
     * @param array $errors 错误详情
     * @return Response
     */
    public static function error(string $msg = '操作失败', int $code = 400, array $errors = []): Response
    {
        $data = empty($errors) ? new \stdClass() : ['errors' => $errors];
        return self::response($code, $msg, $data);
    }
    
    /**
     * 构建响应结果
     * 
     * @param int $code 状态码
     * @param string $msg 消息
     * @param mixed $data 数据
     * @return Response
     */
    private static function response(int $code, string $msg, $data): Response
    {
        $result = [
            'code' => $code,
            'message' => $msg,
            'data' => $data,
        ];
        
        // 判断是否需要加密响应数据
        $cryptoConfig = Config::get('crypto', []);
        $encryptResponse = $cryptoConfig['encrypt_response'] ?? false;
        
        if ($encryptResponse) {
            $path = request()->pathinfo();
            $exemptPrefixes = $cryptoConfig['exempt_prefixes'] ?? [];
            $exemptPaths = $cryptoConfig['exempt_paths'] ?? [];
            
            // 检查是否在豁免路径中
            $isExempt = in_array("/{$path}", $exemptPaths);
            if (!$isExempt) {
                foreach ($exemptPrefixes as $prefix) {
                    if (strpos("/{$path}", $prefix) === 0) {
                        $isExempt = true;
                        break;
                    }
                }
            }
            
            // 不在豁免路径中，需要加密data部分
            if (!$isExempt && $code === 200 && !empty($data) && $data !== new \stdClass()) {
                $result['data'] = Crypto::encrypt($data);
            }
        }
        
        return json($result);
    }
} 