<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-header">
        <h2 class="login-title">{{ $t('Login.title') }}</h2>
        <p class="login-subtitle">{{ $t('Login.subtitle') }}</p>
      </div>
      
      <form class="login-form" @submit.prevent="handleLogin">
        <div class="form-group">
          <label for="username">{{ $t('Login.username') }}</label>
          <div class="input-wrapper">
            <i class="fa fa-user"></i>
            <input 
              type="text" 
              id="username" 
              v-model="form.username" 
              :placeholder="$t('Login.usernamePlaceholder')"
              required
            />
          </div>
          <div class="error-message" v-if="errors.username">{{ errors.username }}</div>
        </div>
        
        <div class="form-group">
          <label for="password">{{ $t('Login.password') }}</label>
          <div class="input-wrapper">
            <i class="fa fa-lock"></i>
            <input 
              :type="showPassword ? 'text' : 'password'" 
              id="password" 
              v-model="form.password" 
              :placeholder="$t('Login.passwordPlaceholder')"
              required
            />
            <button 
              type="button" 
              class="toggle-password" 
              @click="showPassword = !showPassword"
              tabindex="-1"
            >
              <i :class="showPassword ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
            </button>
          </div>
          <div class="error-message" v-if="errors.password">{{ errors.password }}</div>
        </div>
        
        <div class="form-group captcha-group">
          <label for="captcha">{{ $t('Login.captcha') }}</label>
          <div class="captcha-wrapper">
            <div class="input-wrapper">
              <i class="fa fa-shield-alt"></i>
              <input 
                type="text" 
                id="captcha" 
                v-model="form.captcha" 
                :placeholder="$t('Login.captchaPlaceholder')"
                required
              />
            </div>
            <div class="captcha-image" @click="refreshCaptcha">
              <div v-if="!captchaUrl" style="background-color: white; width: 100%; height: 100%;"></div>
              <img v-else :src="captchaUrl" alt="Captcha" />
            </div>
          </div>
          <div class="error-message" v-if="errors.captcha">{{ errors.captcha }}</div>
        </div>
        
        <div class="form-options">
          <div class="remember-me">
            <input type="checkbox" id="remember" v-model="form.remember" />
            <label for="remember">{{ $t('Login.rememberMe') }}</label>
          </div>
          <router-link to="/auth/forgot-password" class="forgot-password">
            {{ $t('Login.forgotPassword') }}
          </router-link>
        </div>
        
        <button type="submit" class="btn-login" :disabled="loading">
          <span v-if="!loading">{{ $t('Login.loginButton') }}</span>
          <span v-else class="loading-spinner"></span>
        </button>
        
        <div class="login-footer">
          <p>{{ $t('Login.noAccount') }} <router-link to="/auth/register">{{ $t('Login.registerNow') }}</router-link></p>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

export default {
  name: 'LoginPage',
  setup() {
    const store = useStore()
    const router = useRouter()
    const { t } = useI18n()
    
    const form = reactive({
      username: '',
      password: '',
      captcha: '',
      remember: false
    })
    
    const errors = reactive({
      username: '',
      password: '',
      captcha: ''
    })
    
    const loading = ref(false)
    const showPassword = ref(false)
    const captchaUrl = ref('')
    
    const refreshCaptcha = async () => {
      try {
        // 通过store获取验证码
        const result = await store.dispatch('user/getCaptcha')
        // 后端直接返回base64图片字符串，直接使用
        captchaUrl.value = result
      } catch (error) {
        store.dispatch('showNotification', {
          type: 'error',
          title: t('common.error'),
          message: '获取验证码失败'
        })
      }
    }
    
    const validateForm = () => {
      let isValid = true
      
      // 重置错误信息
      errors.username = ''
      errors.password = ''
      errors.captcha = ''
      
      // 验证用户名
      if (!form.username) {
        errors.username = t('Login.usernameRequired')
        isValid = false
      }
      
      // 验证密码
      if (!form.password) {
        errors.password = t('Login.passwordRequired')
        isValid = false
      } else if (form.password.length < 6) {
        errors.password = t('Login.passwordLength')
        isValid = false
      }
      
      // 验证验证码
      if (!form.captcha) {
        errors.captcha = t('Login.captchaRequired')
        isValid = false
      }
      
      return isValid
    }
    
    const handleLogin = async () => {
      if (!validateForm()) {
        return
      }
      
      loading.value = true
      
      try {
        // 调用store的login action
        const result = await store.dispatch('user/login', {
          username: form.username,
          password: form.password,
          captcha: form.captcha,
          remember: form.remember
        })
        
        // 登录成功，显示成功提示并跳转到用户中心
        if (result && result.code === 200 && result.data.token) {
          store.dispatch('showNotification', {
            type: 'success',
            title: t('common.success'),
            message: t('Login.loginSuccess')
          })
          router.push('/user')
        }
      } catch (error) {
        // 获取错误消息
        const errorMsg = error.message || '';
        
        // 根据错误类型显示不同的错误信息
        if (errorMsg.includes('验证码') || errorMsg.includes('captcha')) {
          errors.captcha = t('Login.captchaError')
          store.dispatch('showNotification', {
            type: 'error',
            title: t('common.error'),
            message: t('Login.captchaError')
          })
        } else {
          // 通用错误信息
          errors.username = t('Login.loginFailed')
          store.dispatch('showNotification', {
            type: 'error',
            title: t('common.error'),
            message: t('Login.loginFailed')
          })
        }
        // 刷新验证码
        refreshCaptcha()
      } finally {
        loading.value = false
      }
    }
    
    onMounted(() => {
      refreshCaptcha()
    })
    
    return {
      form,
      errors,
      loading,
      showPassword,
      captchaUrl,
      refreshCaptcha,
      handleLogin
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/auth/login.css';
</style>
