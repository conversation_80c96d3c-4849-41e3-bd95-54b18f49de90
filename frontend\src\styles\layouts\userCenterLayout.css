.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.user-center-container {
  display: flex;
  min-height: calc(100vh - 64px - 300px);
  /* Adjust based on navbar and footer heights */
}

/* 用户中心侧边栏样式 - 科技感设计 */
.user-sidebar {
  background: linear-gradient(145deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-right: 1px solid rgba(var(--border-color-rgb), 0.1);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
  position: sticky;
  top: 70px; /* 导航栏的高度 */
  height: calc(100vh - 70px); /* 视口高度减去导航栏高度 */
  overflow-y: auto;
  overflow-x: hidden;
  transition: width var(--transition-normal);
  z-index: 1; /* 确保左侧导航栏的 z-index 低于顶部导航栏 */
}

/* 添加科技感背景元素 */
.user-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 10% 20%, rgba(var(--primary-color-rgb), 0.03) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(var(--primary-color-rgb), 0.03) 0%, transparent 20%),
    linear-gradient(120deg, rgba(var(--primary-color-rgb), 0.02) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

.user-sidebar-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid rgba(var(--border-color-rgb), 0.1);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.user-sidebar-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  position: relative;
  padding-left: var(--spacing-md);
}

.user-sidebar-header h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 18px;
  background: linear-gradient(to bottom, var(--primary-color), var(--primary-color-light));
  border-radius: 2px;
}

.user-sidebar-menu {
  list-style: none;
  padding: var(--spacing-sm) 0;
  margin: 0;
  position: relative;
  z-index: 1;
}

.user-sidebar-item {
  margin: var(--spacing-xs) 0;
  position: relative;
  transition: all var(--transition-normal);
}

.user-sidebar-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-normal);
  border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  white-space: nowrap;
}

/* 悬停效果 */
.user-sidebar-link:hover {
  color: var(--text-primary);
  background: rgba(var(--bg-secondary-rgb), 0.5);
  transform: translateX(4px);
}

/* 悬停时的光效 */
.user-sidebar-link::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.user-sidebar-link:hover::after {
  left: 100%;
}

/* 活动项目样式 */
.user-sidebar-item.active .user-sidebar-link {
  background: linear-gradient(
    90deg,
    rgba(var(--primary-color-rgb), 0.1) 0%,
    rgba(var(--primary-color-rgb), 0.05) 70%,
    transparent 100%
  );
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  border-left: 3px solid var(--primary-color);
  padding-left: calc(var(--spacing-lg) - 3px);
  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.1);
}

/* 图标样式 */
.user-sidebar-link i {
  margin-right: var(--spacing-md);
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  border-radius: var(--border-radius-circle);
  background: rgba(var(--bg-tertiary-rgb), 0.5);
  color: var(--text-secondary);
  transition: all var(--transition-normal);
  flex-shrink: 0;
}

.user-sidebar-item.active .user-sidebar-link i,
.user-sidebar-link:hover i {
  background: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  transform: scale(1.1);
}

/* 带子菜单的项目 */
.user-sidebar-item.has-children .user-sidebar-arrow {
  margin-left: auto;
  transition: transform var(--transition-normal);
}

.user-sidebar-item.open .user-sidebar-arrow i {
  transform: rotate(90deg);
}

/* 子菜单样式 */
.user-sidebar-submenu {
  list-style: none;
  padding: 0;
  margin: 0;
  background: rgba(var(--bg-secondary-rgb), 0.3);
  border-left: 2px solid rgba(var(--primary-color-rgb), 0.1);
  margin-left: var(--spacing-lg);
}

.user-sidebar-submenu-item {
  margin: 0;
}

.user-sidebar-submenu-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-lg);
  color: var(--text-secondary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: all var(--transition-normal);
  cursor: pointer;
}

/* 确保子菜单图标宽度一致 */
.user-sidebar-submenu-link i {
  width: 20px;
  text-align: center;
  margin-right: var(--spacing-sm);
  font-size: 12px;
}

.user-sidebar-submenu-link:hover {
  color: var(--text-primary);
  background: rgba(var(--bg-tertiary-rgb), 0.3);
}

.user-sidebar-submenu-item.active .user-sidebar-submenu-link {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

/* 徽章样式 */
.user-sidebar-badge {
  margin-left: var(--spacing-sm);
  padding: 2px 6px;
  font-size: 10px;
  border-radius: var(--border-radius-sm);
  background-color: var(--primary-color);
  color: var(--text-on-primary);
}

/* 主内容区域 */
.user-content {
  flex: 1;
  padding: var(--spacing-sm);
  overflow-x: hidden;
}

.user-content-body {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-lg);
  /* margin-top: var(--spacing-md); */
}

/* 侧边栏切换按钮样式 */
.sidebar-toggle-btn {
  position: absolute;
  top: 50%;
  right: -0px;
  width: 24px;
  height: 24px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.3);
  transition: all var(--transition-normal);
  transform: translateY(-50%);
}

.sidebar-toggle-btn:hover {
  background: var(--primary-color-dark);
  transform: translateY(-50%) scale(1.1);
}

.sidebar-toggle-btn i {
  color: white;
  font-size: 12px;
  transition: transform var(--transition-normal);
}

.sidebar-collapsed .sidebar-toggle-btn i {
  transform: rotate(180deg);
}

/* 收起状态下的侧边栏样式 */
.sidebar-collapsed .user-sidebar {
  width: 60px;
  overflow: visible;
}

.sidebar-collapsed .user-sidebar-header h3 {
  opacity: 0;
  visibility: hidden;
}

.sidebar-collapsed .user-sidebar-link span,
.sidebar-collapsed .user-sidebar-badge,
.sidebar-collapsed .user-sidebar-arrow {
  opacity: 0;
  visibility: hidden;
  width: 0;
}

/* 修复侧边栏收起时图标对齐问题 */
.sidebar-collapsed .user-sidebar-link {
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md); /* 调整填充以适应收起状态 */
}

.sidebar-collapsed .user-sidebar-link i {
  margin-right: var(--spacing-xs); /* 减小边距以对齐 */
}

/* 特别处理代理中心的图标对齐 */
.sidebar-collapsed .user-sidebar-item.has-children .user-sidebar-link i {
  margin-right: 0; /* 移除右边距，确保图标居中对齐 */
}

/* 收起状态下的二级菜单悬浮显示 */
.sidebar-collapsed .user-sidebar-item.has-children:hover .user-sidebar-submenu {
  display: block;
  position: absolute;
  left: 60px;
  top: 0;
  min-width: 180px;
  background: var(--bg-card);
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
  box-shadow: var(--box-shadow);
  border-left: none;
  margin-left: 0;
  z-index: 100;
}

.sidebar-collapsed .user-sidebar-submenu {
  display: none;
}

.sidebar-collapsed .user-sidebar-submenu-link {
  padding: var(--spacing-sm) var(--spacing-md);
}

/* 下拉菜单动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.3s ease;
  max-height: 300px;
  overflow: hidden;
}

.dropdown-enter-from,
.dropdown-leave-to {
  max-height: 0;
  opacity: 0;
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式样式 */
@media (max-width: 992px) {
  .user-center-container {
    flex-direction: column;
  }

  .user-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(90deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: sticky;
    top: 70px;
    height: auto;
    /* 降低侧边栏的z-index，确保不会遮盖导航栏的下拉菜单 */
    z-index: calc(var(--z-index-dropdown) - 10);
  }

  .user-sidebar-menu {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .user-sidebar-menu::-webkit-scrollbar {
    display: none;
  }

  .user-sidebar-item {
    border-bottom: none;
    border-right: none;
    flex-shrink: 0;
    margin: var(--spacing-xs);
    width: auto;
  }

  .user-sidebar-link {
    padding: var(--spacing-sm) var(--spacing-md);
    flex-direction: column;
    text-align: center;
    border-radius: var(--border-radius-md);
    margin-right: 0;
    min-width: 80px;
    max-width: 120px;
  }

  .user-sidebar-item-title {
    white-space: normal;
    word-break: break-word;
    font-size: var(--font-size-sm);
    line-height: 1.2;
    margin-top: var(--spacing-xs);
  }

  .user-sidebar-item.active .user-sidebar-link {
    border-left: none;
    border-bottom: 2px solid var(--primary-color);
    padding-left: var(--spacing-md);
    padding-bottom: calc(var(--spacing-sm) - 2px);
  }

  .user-sidebar-link i {
    margin-right: 0;
    margin-bottom: var(--spacing-xs);
  }

  .user-sidebar-submenu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    /* 提高子菜单的z-index，但保持低于导航栏下拉菜单 */
    z-index: calc(var(--z-index-dropdown) - 5);
    margin-left: 0;
    border-left: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  /* 子菜单箭头在移动端的位置调整 */
  .user-sidebar-item.has-children .user-sidebar-arrow {
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    margin-left: 0;
  }
  
  .user-sidebar-item.open .user-sidebar-arrow i {
    transform: rotate(180deg);
  }
  
  /* 移动端侧边栏切换按钮隐藏 */
  .sidebar-toggle-btn {
    display: none;
  }
  
  .sidebar-collapsed .user-sidebar {
    width: 100%;
  }
  
  /* 保持收起状态下的文本隐藏 */
  .sidebar-collapsed .user-sidebar-header h3 {
    opacity: 0;
    visibility: hidden;
  }
  
  /* 移动端收起状态下的菜单项样式 */
  .sidebar-collapsed .user-sidebar-link {
    justify-content: center;
    padding: var(--spacing-sm);
  }
  
  .sidebar-collapsed .user-sidebar-link i {
    margin-right: 0;
    margin-bottom: 0;
  }
  
  /* 移动端收起状态下隐藏文本和其他元素 */
  .sidebar-collapsed .user-sidebar-item-title,
  .sidebar-collapsed .user-sidebar-badge,
  .sidebar-collapsed .user-sidebar-arrow {
    display: none;
  }
  
  /* 调整移动端收起状态下的菜单项宽度 */
  .sidebar-collapsed .user-sidebar-item {
    min-width: auto;
    max-width: 60px;
  }
}
