{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.4.2", "@vuepic/vue-datepicker": "^11.0.2", "axios": "^1.4.0", "chart.js": "^3.9.1", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "echarts": "^5.4.2", "js-cookie": "^3.0.5", "qrcode": "^1.5.4", "vue": "^3.3.4", "vue-i18n": "^9.2.2", "vue-router": "^4.2.2", "vuex": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "eslint": "^8.42.0", "eslint-plugin-vue": "^9.14.1", "vite": "^6.2.2"}}