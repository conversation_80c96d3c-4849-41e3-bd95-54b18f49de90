<?php
declare(strict_types=1);

namespace app\utils;

use Firebase\JWT\JWT as FirebaseJWT;
use Firebase\JWT\Key;
use think\facade\Config;

/**
 * JWT工具类
 */
class Jwt
{
    /**
     * 生成JWT令牌
     * 
     * @param array $payload 令牌负载数据
     * @param int $expire 过期时间(秒)
     * @return string
     */
    public static function generate(array $payload, int $expire = 86400): string
    {
        $config = Config::get('jwt', []);
        $key = $config['secret_key'] ?? 'lianghua_secret_key';
        $algorithm = $config['algorithm'] ?? 'HS256';
        
        $time = time();
        $data = [
            'iat' => $time,           // 签发时间
            'exp' => $time + $expire, // 过期时间
            'data' => $payload        // 自定义数据
        ];
        
        return FirebaseJWT::encode($data, $key, $algorithm);
    }
    
    /**
     * 验证JWT令牌
     * 
     * @param string $token JWT令牌
     * @return array|false 验证成功返回负载数据，失败返回false
     */
    public static function verify(string $token)
    {
        $config = Config::get('jwt', []);
        $key = $config['secret_key'] ?? 'lianghua_secret_key';
        $algorithm = $config['algorithm'] ?? 'HS256';
        
        try {
            $decoded = FirebaseJWT::decode($token, new Key($key, $algorithm));
            return (array)$decoded->data;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 从请求头中获取token
     * 
     * @param string $header 请求头
     * @return string|false
     */
    public static function getToken(string $header = null)
    {
        if (is_null($header)) {
            $header = request()->header('Authorization');
        }
        
        if ($header && preg_match('/Bearer\s+(.*)$/i', $header, $matches)) {
            return $matches[1];
        }
        
        return false;
    }
} 