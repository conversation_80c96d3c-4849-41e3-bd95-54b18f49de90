<?php
declare(strict_types=1);

namespace app\controller\api\v1;

use app\controller\api\BaseController;
use think\Response;
use think\facade\Db;
use app\validate\Agent as AgentValidate;

/**
 * 代理控制器
 */
class Agent extends BaseController
{
    /**
     * 获取代理概览
     */
    public function overview(): Response
    {
        // 验证请求参数
        $validate = new AgentValidate();
        if (!$validate->scene('overview')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        // 获取当前登录用户ID
        $userId = request()->user['id'];

        // 一次性获取用户等级、邀请码和余额
        $userInfo = Db::name('users')
            ->where('id', $userId)
            ->field('level, invitation, balance')
            ->find();

        if (!$userInfo) {
            return $this->error('用户不存在');
        }

        $level = $userInfo['level'] ?? 0;
        $invitation = $userInfo['invitation'] ?? '';
        $balance = $userInfo['balance'] ?? 0.00; // 获取余额

        // 获取直接下级用户ID（pid为当前用户ID的用户）
        $directSubUserIds = Db::name('users')
            ->where('pid', $userId)
            ->column('id');

        // 获取邀请人数（直接下级数量）
        $totalReferrals = count($directSubUserIds);

        // 获取有效人数（直接下级用户中sum_income>0的用户数量）
        $invitedUsers = 0;
        if (!empty($directSubUserIds)) {
            $invitedUsers = Db::name('users')
                ->whereIn('id', $directSubUserIds)
                ->where('sum_income', '>', 0)
                ->count();
        }





        // 获取代理总收益（agent_commission_records表中agent_id=当前用户id的amount总和）
        $totalCommission = Db::name('agent_commission_records')
            ->where('agent_id', $userId)
            ->sum('amount');

        // 获取已交收代理收益（agent_commission_records表中agent_id=当前用户id且status=1的amount总和）
        $settledCommission = Db::name('agent_commission_records')
            ->where('agent_id', $userId)
            ->where('status', 1)
            ->sum('amount');

        // 获取未交收代理收益（agent_commission_records表中agent_id=当前用户id且status=0的amount总和）
        $unsettledCommission = Db::name('agent_commission_records')
            ->where('agent_id', $userId)
            ->where('status', 0)
            ->sum('amount');

        // 获取已提现金额（agent_withdrawal_records表中agent_id=当前用户id且status=2的amount总和）
        $withdrawnAmount = Db::name('agent_withdrawal_records')
            ->where('agent_id', $userId)
            ->where('status', 2)
            ->sum('amount');

        // 获取提现中金额（agent_withdrawal_records表中agent_id=当前用户id且status=0或者1的amount总和）
        $withdrawingAmount = Db::name('agent_withdrawal_records')
            ->where('agent_id', $userId)
            ->whereIn('status', [0, 1])
            ->sum('amount');

        return $this->success([
            'level' => $level,                 // 代理等级
            'invitedUsers' => $invitedUsers,           // 有效人数
            'totalReferrals' => $totalReferrals,         // 邀请人数
            'invitation' => $invitation,              // 邀请链接
            'balance' => $balance,                   // 钱包余额
            'totalCommission' => $totalCommission,         // 代理总收益
            'settledCommission' => $settledCommission,      // 已交收代理收益
            'unsettledCommission' => $unsettledCommission,    // 未交收代理收益
            'withdrawnAmount' => $withdrawnAmount,         // 已提现金额
            'withdrawingAmount' => $withdrawingAmount      // 提现中金额
        ], '获取代理概览成功');
    }


    /**
     * 获取代理统计数据
     */
    public function profit(): Response
    {
        // 验证请求参数
        $validate = new AgentValidate();
        if (!$validate->scene('profit')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        // 获取当前登录用户ID
        $userId = request()->user['id'];

        // 获取分页参数
        $page = input('page/d', 1);
        $limit = input('limit/d', 10);
        $parentId = input('parentId/d', 0); // 父级ID，0表示顶级

        // 获取时间筛选参数
        $startDate = input('startDate/s', '');
        $endDate = input('endDate/s', '');

        // 获取当前用户的level
        $userLevel = Db::name('users')->where('id', $userId)->value('level');
        $userLevel = $userLevel ?: 0; // 如果为null，设为0

        // 检查用户等级是否允许查看下级的下级
        if ($userLevel < 2 && $parentId > 0) {
            return $this->error('代理星级不够，无法查看下级的下级用户');
        }

        // 如果是获取子用户数据
        if ($parentId > 0) {
            // 检查父级用户是否存在
            $parentUser = Db::name('users')->where('id', $parentId)->find();
            if (!$parentUser) {
                return $this->error('父级用户不存在');
            }

            // 检查父级用户是否是当前用户的下级
            if ($parentUser['pid'] != $userId && $parentId != $userId) {
                // 递归检查是否是下级
                $isSubUser = false;
                $tempPid = $parentUser['pid'];
                while ($tempPid > 0) {
                    $tempUser = Db::name('users')->where('id', $tempPid)->find();
                    if (!$tempUser) {
                        break;
                    }
                    if ($tempUser['id'] == $userId) {
                        $isSubUser = true;
                        break;
                    }
                    $tempPid = $tempUser['pid'];
                }

                if (!$isSubUser) {
                    return $this->error('无权查看该用户的下级数据');
                }
            }

            // 计算当前查询的层级深度
            $depth = 1; // 默认为1级（直接下级）
            $tempId = $parentId;
            while ($tempId != $userId && $tempId > 0) {
                $depth++;
                $tempUser = Db::name('users')->where('id', $tempId)->find();
                if (!$tempUser) {
                    break;
                }
                $tempId = $tempUser['pid'];
            }

            // 检查用户等级是否允许查看该层级深度
            if ($depth > $userLevel) {
                return $this->error('代理星级不够，无法查看该层级的下级用户');
            }

            // 查询pid为parentId的直接下级用户，当获取子数据时不使用分页或使用更大的分页限制
            $query = Db::name('users')
                ->where('pid', $parentId)
                ->field('id, username, level, pid');

            // 获取总数
            $total = $query->count();

            // 如果前端传入了较大的limit值，则使用前端传入的值，否则不使用分页
            if ($limit > 100) {
                // 使用较大的limit值，实际上是获取全部数据
                $directUsers = $query->select()->toArray();
            } else {
                // 使用默认分页
                $directUsers = $query->page($page, $limit)->select()->toArray();
            }
        } else {
            // 查询pid为当前userid的直接下级用户
            $directUsers = Db::name('users')
                ->where('pid', $userId)
                ->field('id, username, level, pid')
                ->page($page, $limit)
                ->select()
                ->toArray();

            // 获取总数
            $total = Db::name('users')
                ->where('pid', $userId)
                ->count();
        }

        // 如果没有直接下级用户，返回空数组
        if (empty($directUsers)) {
            return $this->success([
                'total' => 0,
                'page' => $page,
                'limit' => $limit,
                'profit' => []
            ], '获取代理统计数据成功');
        }

        // 准备返回数据
        $profitData = [];

        // 不再需要日期范围

        // 获取代理等级配置
        $agentLevelConfigs = Db::name('agent_level_config')
            ->field('level, radio')
            ->order('level asc')
            ->select()
            ->toArray();

        foreach ($directUsers as $user) {
            $subUserId = $user['id'];

            // 不再需要获取所有下级用户

            // 获取下级用户数量（直接下级数量）
            $inviteCount = Db::name('users')
                ->where('pid', $subUserId)
                ->count();

            // 获取直接下级用户ID
            $directSubUserIds = Db::name('users')
                ->where('pid', $subUserId)
                ->column('id');

            // 获取有效人数（直接下级用户中sum_income>0的用户数量）
            $activeUsers = Db::name('users')
                ->whereIn('id', $directSubUserIds)
                ->where('sum_income', '>', 0)
                ->count();

            // 获取用户自身收益（funding_rate_logs表中user_id=自己的usdt总和）
            $userProfitQuery = Db::name('funding_rate_logs')
                ->where('user_id', $subUserId);

            // 添加时间筛选条件
            if (!empty($startDate)) {
                $userProfitQuery->where('time', '>=', $startDate . ' 00:00:00');
            }

            if (!empty($endDate)) {
                $userProfitQuery->where('time', '<=', $endDate . ' 23:59:59');
            }

            $userProfit = $userProfitQuery->sum('usdt');

            // 计算分成比例
            $commissionRatio = 0;

            // 计算当前层级
            $level = 0;
            if ($parentId > 0) {
                // 如果是子级数据，计算相对于顶级的层级
                $level = 1; // 默认为1级
                $tempId = $user['pid'];
                while ($tempId > 0 && $tempId != $userId) {
                    $level++;
                    $tempUser = Db::name('users')->where('id', $tempId)->find();
                    if (!$tempUser) {
                        break;
                    }
                    $tempId = $tempUser['pid'];
                }
            }

            // 获取用户层级
            $userSubLevel = $level == 0 ? 1 : $level;

            // 根据登录用户等级和下级用户层级计算分成比例
            if ($userLevel == 0) {
                // 当登录用户代理等级level为0时，只有1级用户有分成比例
                if ($userSubLevel == 1) {
                    // 分成比例为agent_level_config表中level=0的radio
                    foreach ($agentLevelConfigs as $config) {
                        if ($config['level'] == '0') {
                            $commissionRatio = floatval($config['radio']);
                            break;
                        }
                    }
                }
            } else if ($userLevel == 1) {
                // 当登录用户代理等级为level1时，也只有1级用户有分成比例
                if ($userSubLevel == 1) {
                    // 分成比例为agent_level_config表中level=0的radio + level=1的radio
                    foreach ($agentLevelConfigs as $config) {
                        if ($config['level'] == '0' || $config['level'] == '1') {
                            $commissionRatio += floatval($config['radio']);
                        }
                    }
                }
            } else {
                // 当登录用户代理等级为level2及以上时
                if ($userSubLevel == 1) {
                    // 对于1级用户，分成比例为agent_level_config表中level=0的radio + level=1的radio
                    foreach ($agentLevelConfigs as $config) {
                        if ($config['level'] == '0' || $config['level'] == '1') {
                            $commissionRatio += floatval($config['radio']);
                        }
                    }
                } else {
                    // 对于2级及以上用户，分成比例为agent_level_config表中对应level的radio
                    foreach ($agentLevelConfigs as $config) {
                        if ($config['level'] == (string)$userSubLevel) {
                            $commissionRatio = floatval($config['radio']);
                            break;
                        }
                    }
                }
            }

            // 计算预计代理收益（用户收益 * 分成比例）
            $expectedAgentCommission = round($userProfit * $commissionRatio / 100, 8);

            // 获取未交收收益（settlement_record表中status=0和2的）
            $unpaidProfitQuery = Db::name('settlement_record')
                ->where('user_id', $subUserId)
                ->whereIn('status', [0, 2]);

            // 添加时间筛选条件
            if (!empty($startDate)) {
                $unpaidProfitQuery->where('created_at', '>=', $startDate . ' 00:00:00');
            }

            if (!empty($endDate)) {
                $unpaidProfitQuery->where('created_at', '<=', $endDate . ' 23:59:59');
            }

            $unpaidProfit = $unpaidProfitQuery->sum('profit');

            // 获取已交收收益（settlement_record表中status=1的）
            $settledProfitQuery = Db::name('settlement_record')
                ->where('user_id', $subUserId)
                ->where('status', 1);

            // 添加时间筛选条件
            if (!empty($startDate)) {
                $settledProfitQuery->where('created_at', '>=', $startDate . ' 00:00:00');
            }

            if (!empty($endDate)) {
                $settledProfitQuery->where('created_at', '<=', $endDate . ' 23:59:59');
            }

            $settledProfit = $settledProfitQuery->sum('profit');

            // 计算到账代理收益（已交收收益 * 分成比例）
            $actualAgentCommission = round($settledProfit * $commissionRatio / 100, 8);

            // 检查是否有下级用户
            $hasChildren = Db::name('users')
                ->where('pid', $subUserId)
                ->count() > 0;

            // 根据用户level和当前用户level判断是否可以展开
            $canExpand = false;
            if ($userLevel >= 2) { // 当前用户level>=2才能展开
                // 计算可展开的最大层级
                $maxExpandLevel = $userLevel - 1; // level=2可展开到1级，level=3可展开到2级...

                // 如果当前层级小于或等于最大可展开层级，并且有子用户，则可以展开
                $canExpand = ($level <= $maxExpandLevel) && $hasChildren;
            }

            // 构建数据
            $profitData[] = [
                'id' => $subUserId,
                'userAccount' => $user['username'],
                'inviteCount' => $inviteCount,
                'activeUsers' => $activeUsers,
                'userProfit' => round($userProfit, 8),
                'commissionRatio' => round($commissionRatio, 2), // 分成比例保持2位小数
                'expectedAgentCommission' => $expectedAgentCommission, // 预计代理收益
                'unpaidProfit' => round($unpaidProfit, 8), // 未交收收益
                'settledProfit' => round($settledProfit, 8), // 已交收收益
                'actualAgentCommission' => $actualAgentCommission, // 到账代理收益
                'hasChildren' => $hasChildren, // 是否有子项（不考虑level限制）
                'canExpand' => $canExpand, // 是否可以展开（考虑level限制）
                'level' => $level, // 层级
                'userLevel' => $level==0 ? 1 : $level, // 用户层级（前端显示用）
                'pid' => $user['pid'] // 父ID
            ];
        }

        return $this->success([
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'profit' => $profitData,
        ], '获取代理统计数据成功');
    }

    /**
     * 获取代理等级列表
     */
    public function levelList(): Response
    {
        // 查询代理等级配置表
        $list = Db::name('agent_level_config')
            ->field('level, radio, invited_users')
            ->order('level asc')
            ->select()
            ->toArray();

        return $this->success($list, '获取代理等级列表成功');
    }

    /**
     * 提现申请接口
     */
    public function withdrawApply(): Response
    {
        $validate = new AgentValidate();
        if (!$validate->scene('withdrawApply')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        $userId = request()->user['id'];
        $amount = input('amount/f', 0);
        // $network = input('network/s', 'Tron(TRC20)'); // 暂时不使用
        $address = input('address/s', '');

        if ($amount <= 0) {
            return $this->error('提现金额必须大于0');
        }

        if (empty($address)) {
            return $this->error('提现地址不能为空');
        }

        // 查询当前用户余额，假设余额字段为 users.balance
        $originalBalance = Db::name('users')->where('id', $userId)->value('balance');
        if ($originalBalance === null) {
            return $this->error('用户余额查询失败');
        }

        if ($amount > $originalBalance) {
            return $this->error('提现金额不能大于当前余额');
        }

        // 计算手续费，假设手续费为1
        $fee = 1;
        $amountReceived = round($amount - $fee, 2);

        // 计算剩余余额
        $remainingBalance = round($originalBalance - $amount, 2);

        // 插入提现记录，状态初始为0处理中
        $data = [
            'agent_id' => $userId,
            'address' => $address,
            'amount' => $amount,
            'fee' => $fee,
            'amount_received' => $amountReceived,
            'status' => 0,
            'remaining_balance' => $remainingBalance,
            'original_balance' => $originalBalance,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ];
        // 事务
        Db::startTrans();
        try {
            // 更新用户余额
            Db::name('users')->where('id', $userId)->update(['balance' => $remainingBalance]);
            // 插入提现记录
            $result = Db::name('agent_withdrawal_records')->insert($data);
            // 提交事务
            Db::commit();
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->error('提现申请提交失败: ' . $e->getMessage());
        }

        if ($result) {
            return $this->success([], '提现申请提交成功');
        } else {
            return $this->error('提现申请提交失败');
        }
    }

    /**
     * 提现记录列表接口
     */
    public function withdrawalRecords(): Response
    {
        $validate = new AgentValidate();
        if (!$validate->scene('withdrawalRecords')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        $userId = request()->user['id'];
        $page = input('page/d', 1);
        $limit = input('limit/d', 10);

        // 获取状态筛选参数
        $status = input('status/s', '');

        // 获取时间筛选参数
        $startDate = input('startDate/s', '');
        $endDate = input('endDate/s', '');

        $query = Db::name('agent_withdrawal_records')
            ->where('agent_id', $userId);

        // 添加状态筛选条件
        if ($status !== '') {
            $query->where('status', $status);
        }

        // 添加时间筛选条件
        if (!empty($startDate)) {
            $query->where('created_at', '>=', $startDate . ' 00:00:00');
        }

        if (!empty($endDate)) {
            $query->where('created_at', '<=', $endDate . ' 23:59:59');
        }

        // 添加排序
        $query->order('created_at', 'desc');

        // 获取总数
        $total = $query->count();

        // 获取分页数据
        $records = $query->page($page, $limit)
            ->select()
            ->toArray();

        return $this->success([
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'records' => $records,
        ], '获取提现记录列表成功');
    }

    /**
     * 确认提现接口
     */
    public function withdrawConfirm(): Response
    {
        $validate = new AgentValidate();
        if (!$validate->scene('withdrawConfirm')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        $userId = request()->user['id'];
        $withdrawalId = input('withdrawal_id/d');

        // 查询提现记录
        $record = Db::name('agent_withdrawal_records')
            ->where('id', $withdrawalId)
            ->where('agent_id', $userId)
            ->find();

        if (!$record) {
            return $this->error('提现记录不存在');
        }

        // 只有状态为0（处理中）的提现记录可以确认
        if ($record['status'] != 0) {
            return $this->error('该提现记录无法确认');
        }

        // 更新提现记录状态为1（已确认）
        $result = Db::name('agent_withdrawal_records')
            ->where('id', $withdrawalId)
            ->update([
                'status' => 1,
                'updated_at' => date('Y-m-d H:i:s'),
            ]);

        if ($result) {
            return $this->success([], '提现确认成功');
        } else {
            return $this->error('提现确认失败');
        }
    }

    /**
     * @notes 取消提现申请
     * @return Response
     */
    public function cancelWithdrawal(): Response
    {
        $validate = new AgentValidate();
        if (!$validate->scene('cancelWithdrawal')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        $userId = request()->user['id'];
        $withdrawalId = input('withdrawal_id/d');

        // 启动数据库事务
        Db::startTrans();
        try {
            // 查询提现记录并锁定行记录，防止并发问题
            $record = Db::name('agent_withdrawal_records')
                ->where('id', $withdrawalId)
                ->where('agent_id', $userId)
                ->lock(true) // 添加行锁
                ->find();

            if (!$record) {
                Db::rollback();
                return $this->error('提现记录不存在');
            }

            // 只有状态为0（处理中）的提现记录可以取消
            if ($record['status'] != 0) {
                Db::rollback();
                return $this->error('该提现记录无法取消');
            }

            // 更新提现记录状态为4（已取消）
            $updateResult = Db::name('agent_withdrawal_records')
                ->where('id', $withdrawalId)
                ->update([
                    'status' => 4, // 4代表已取消
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);

            if (!$updateResult) {
                Db::rollback();
                return $this->error('取消提现失败');
            }

            // 返还用户余额
            $refundAmount = $record['amount']; // 获取提现金额
            $userUpdateResult = Db::name('users')
                ->where('id', $userId)
                ->inc('balance', (float)$refundAmount) // 增加用户余额，确保类型为 float
                ->update();

            if (!$userUpdateResult) {
                Db::rollback();
                return $this->error('返还余额失败');
            }

            // 提交事务
            Db::commit();
            return $this->success([], '取消提现成功');

        } catch (\Exception $exception) {
            // 回滚事务
            Db::rollback();

            return $this->error('取消提现时发生错误: ' . $exception->getMessage());
        }
    }

    /**
     * 代理佣金记录列表接口
     */
    public function commissionRecords(): Response
    {
        $validate = new AgentValidate();
        if (!$validate->scene('commissionRecords')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        $userId = request()->user['id'];
        $page = input('page/d', 1);
        $limit = input('limit/d', 10);

        // 获取状态筛选参数
        $status = input('status/s', '');

        // 获取时间筛选参数
        $startDate = input('startDate/s', '');
        $endDate = input('endDate/s', '');

        $query = Db::name('agent_commission_records')
            ->alias('acr')
            ->leftJoin('users u', 'acr.user_id = u.id')
            ->where('acr.agent_id', $userId)
            ->field('acr.*, u.username');

        // 添加状态筛选条件
        if ($status !== '') {
            $query->where('acr.status', $status);
        }

        // 添加时间筛选条件
        if (!empty($startDate)) {
            $query->where('acr.created_at', '>=', $startDate . ' 00:00:00');
        }

        if (!empty($endDate)) {
            $query->where('acr.created_at', '<=', $endDate . ' 23:59:59');
        }

        // 添加排序
        $query->order('acr.created_at', 'desc');
        $query->order('acr.id', 'asc');

        // 获取总数
        $total = $query->count();

        // 获取分页数据
        $records = $query->page($page, $limit)
            ->select()
            ->toArray();

        return $this->success([
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'records' => $records,
        ], '获取代理佣金记录列表成功');
    }
}
