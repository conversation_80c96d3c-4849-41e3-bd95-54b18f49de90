.info-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-xl);
}

.info-section {
  margin-bottom: var(--spacing-lg);
}

.section-title {
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  position: relative;
  padding-bottom: var(--spacing-xs);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  border-radius: 1.5px;
}

.info-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: center;
}

.info-avatar {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: var(--spacing-lg);
  flex-shrink: 0;
}



.avatar-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color) 100%);
  color: var(--text-light);
  border-radius: 50%;
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}



.info-details {
  flex: 1;
}

.info-item {
  margin-bottom: var(--spacing-md);
}

.info-item:last-child {
  margin-bottom: 0;
}

.item-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.item-value {
  font-size: 1.1rem;
  color: var(--text-primary);
}

.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
}

.level-1 {
  background-color: var(--info-color-light);
  color: var(--info-color);
}

.level-2 {
  background-color: var(--success-color-light);
  color: var(--success-color);
}

.level-3 {
  background-color: var(--warning-color-light);
  color: var(--warning-color);
}

.contact-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.contact-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: flex-start;
}

.contact-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  color: var(--text-light);
  font-size: 1.25rem;
  flex-shrink: 0;
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}

.contact-content {
  flex: 1;
}

.contact-type {
  font-size: 1.1rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.contact-value {
  font-size: 1rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.contact-status {
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-md);
}

.status-verified {
  color: var(--success-color);
}

.status-unverified {
  color: var(--warning-color);
}

.contact-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.security-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.security-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: flex-start;
}

.security-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  color: var(--text-light);
  font-size: 1.25rem;
  flex-shrink: 0;
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}

.security-content {
  flex: 1;
}

.security-type {
  font-size: 1.1rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.security-value {
  font-size: 1rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.security-actions {
  display: flex;
  gap: var(--spacing-sm);
}



.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background-color: var(--bg-input);
  color: var(--text-primary);
  font-size: 1rem;
}

.form-text {
  font-size: 0.85rem;
  color: var(--text-tertiary);
  margin-top: var(--spacing-xs);
}



.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}



@media (max-width: 991px) {
  .contact-cards,
  .security-cards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 767px) {
  .info-card {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-avatar {
    margin-right: 0;
    margin-bottom: var(--spacing-md);
  }
  
  .contact-actions,
  .security-actions {
    flex-direction: column;
  }

}
