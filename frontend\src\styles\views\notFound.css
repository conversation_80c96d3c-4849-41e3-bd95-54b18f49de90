.not-found-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-primary);
  padding: var(--spacing-lg);
}

.not-found-container {
  width: 100%;
  max-width: 1000px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-xl);
}

.not-found-content {
  flex: 1;
}

.error-code {
  font-size: 8rem;
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  line-height: 1;
  margin-bottom: var(--spacing-md);
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 10px 20px rgba(var(--primary-color-rgb), 0.2);
}

.error-title {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.error-message {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  max-width: 500px;
}

.error-actions {
  display: flex;
  gap: var(--spacing-md);
}

.btn-home {
  padding: var(--spacing-md) var(--spacing-lg);
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  color: var(--text-light);
  border: none;
  border-radius: var(--border-radius-md);
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background 0.3s, transform 0.1s;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.btn-home:hover {
  background: linear-gradient(90deg, var(--primary-color-dark), var(--primary-color));
}

.btn-home:active {
  transform: translateY(1px);
}

.btn-back {
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background 0.3s, transform 0.1s;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.btn-back:hover {
  background-color: var(--bg-hover);
}

.btn-back:active {
  transform: translateY(1px);
}

.not-found-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.not-found-image img {
  max-width: 100%;
  height: auto;
  max-height: 400px;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

@media (max-width: 991px) {
  .not-found-container {
    flex-direction: column-reverse;
    text-align: center;
  }
  
  .error-message {
    margin-left: auto;
    margin-right: auto;
  }
  
  .error-actions {
    justify-content: center;
  }
  
  .not-found-image img {
    max-height: 300px;
  }
}

@media (max-width: 480px) {
  .error-code {
    font-size: 6rem;
  }
  
  .error-title {
    font-size: 2rem;
  }
  
  .error-actions {
    flex-direction: column;
  }
  
  .btn-home, .btn-back {
    width: 100%;
    justify-content: center;
  }
  
  .not-found-image img {
    max-height: 200px;
  }
}
