import axios from 'axios'
import store from '@/store'
// import { encrypt, decrypt } from '@/utils/crypto'

// 创建axios实例
const baseURL = import.meta.env.VITE_API_BASE_URL || '/'
const api = axios.create({
  baseURL,
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json'
  },
  // 确保跨域请求发送凭证
  withCredentials: true
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 如果有token，添加到请求头
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    
    /* 加密请求数据（data部分）
    if (config.data && shouldEncrypt(config.url)) {
      config.data = {
        data: encrypt(config.data)
      }
    }
    */
    
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    const { data } = response
    
    /* 解密响应数据（data部分）
    if (data && data.code === 200 && data.data && typeof data.data === 'string' && shouldDecrypt(response.config.url)) {
      try {
        data.data = decrypt(data.data)
      } catch (error) {
        console.error('解密响应数据失败:', error)
      }
    }
    */
    
    // 检查响应中的code字段，当code为401时执行登录信息清理并跳转到登录页面
    if (data && data.code === 401) {
      // 清除登录信息
      store.dispatch('user/logout')
      
      // 显示提示信息
      store.dispatch('showNotification', {
        type: 'error',
        title: '错误',
        message: data.message || '登录已过期，请重新登录'
      })
      
      // 检查当前路径是否已经在登录页面，如果不是才跳转
      const currentPath = window.location.pathname;
      if (!currentPath.includes('/auth/login')) {
        // 跳转到登录页面
        setTimeout(() => {
          window.location.href = '/auth/login'
        }, 1500)
      }
      
      return Promise.reject({ message: data.message || '登录已过期，请重新登录' })
    }
    
    return data
  },
  error => {
    // 处理错误
    if (error.response) {
      // 服务器返回了非2xx的状态码
      const { status, data } = error.response
      
      // 处理401未授权 - Token过期或无效
      if (status === 401) {
        // 清除用户数据并重定向到登录页面
        store.dispatch('user/logout')
        
        // 显示提示信息
        store.dispatch('showNotification', {
          type: 'error',
          title: '错误',
          message: '登录已过期，请重新登录'
        })
        
        // 检查当前路径是否已经在登录页面，如果不是才跳转
        const currentPath = window.location.pathname;
        if (!currentPath.includes('/auth/login')) {
          // 延迟跳转，让用户有时间看到提示信息
          setTimeout(() => {
            window.location.href = '/auth/login'
          }, 1500)
        }
      }
      
      // 处理后端返回的错误信息
      if (data && typeof data === 'object') {
        // 如果后端返回了message字段，使用它作为错误信息
        if (data.message) {
          return Promise.reject({ message: data.message })
        }
        // 如果后端返回了其他格式的错误信息
        if (data.code && data.code !== 200) {
          return Promise.reject({ message: data.message || '请求失败' })
        }
      }
      
      return Promise.reject(data)
    } else if (error.request) {
      // 请求已发送但未收到响应
      return Promise.reject({ message: '网络错误，请检查您的连接。' })
    } else {
      // 设置请求时发生了错误
      return Promise.reject({ message: error.message })
    }
  }
)

/**
 * 判断是否需要加密请求
 * @param {string} url 请求URL
 * @returns {boolean}
 */
/*
function shouldEncrypt(url) {
  // 不需要加密的URL列表
  const exemptUrls = [
    'auth/captcha',
    'public/',
    'uploads/'
  ]
  
  // 检查是否在豁免列表中
  for (const exemptUrl of exemptUrls) {
    if (url.includes(exemptUrl)) {
      return false
    }
  }
  
  return true
}
*/

/**
 * 判断是否需要解密响应
 * @param {string} url 请求URL
 * @returns {boolean}
 */
/*
function shouldDecrypt(url) {
  // 与加密逻辑相同
  return shouldEncrypt(url)
}
*/

export default api
