.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.stats-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: center;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  color: var(--text-light);
  font-size: 1.5rem;
  flex-shrink: 0;
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.profit-positive {
  color: var(--success-color);
}

.profit-negative {
  color: var(--error-color);
}

.stats-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: var(--spacing-xs);
}

.stats-trend {
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.trend-up {
  color: var(--success-color);
}

.trend-down {
  color: var(--error-color);
}

.stats-info {
  font-size: 0.85rem;
  color: var(--text-tertiary);
}

.collect-btn {
  margin-top: var(--spacing-xs);
}

.overview-sections {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
}

.overview-section {
  margin-bottom: var(--spacing-xl);
}

.section-title {
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  position: relative;
  padding-bottom: var(--spacing-xs);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  border-radius: 1.5px;
}

.info-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.info-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md);
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: center;
}

.info-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  color: var(--text-light);
  font-size: 1rem;
  flex-shrink: 0;
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}

.info-content {
  flex: 1;
}

.info-value {
  font-size: 1.25rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.info-label {
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.promotion-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
}

.promotion-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.promotion-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  color: var(--text-light);
  font-size: 1.25rem;
  flex-shrink: 0;
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}

.promotion-title h3 {
  font-size: 1.1rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.promotion-value {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.promotion-link {
  margin-bottom: var(--spacing-md);
}

.link-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.link-value {
  display: flex;
  gap: var(--spacing-sm);
}

.link-value input {
  flex: 1;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background-color: var(--bg-input);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.promotion-actions {
  display: flex;
  gap: var(--spacing-md);
}

.chart-container {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  height: 300px;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-dialog {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.modal-content {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.modal-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.btn-close {
  background: none;
  border: none;
  color: var(--text-tertiary);
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0;
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-md);
}

@media (max-width: 1199px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 991px) {
  .overview-sections {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 767px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }

  .info-cards {
    grid-template-columns: 1fr;
  }

  .promotion-actions {
    flex-direction: column;
  }
}

/* 可点击卡片样式 */
.clickable-card {
  cursor: pointer;
  position: relative;
  transition: all var(--transition-normal);
}

.clickable-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.card-action-hint {
  position: absolute;
  top: 10px;
  right: 10px;
  color: var(--text-secondary);
  font-size: 12px;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.clickable-card:hover .card-action-hint {
  opacity: 1;
}
