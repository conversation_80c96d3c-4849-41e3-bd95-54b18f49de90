import asyncio
import aiohttp
import pymysql
from dotenv import load_dotenv
import os
from datetime import datetime
import logging
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv('d:\\web\\lianghua_int\\backend\\.env')

def get_db_connection():
    """
    获取数据库连接

    返回:
        pymysql.connections.Connection: 数据库连接对象
    """
    try:
        return pymysql.connect(
            host=os.getenv('DB_HOST'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASS'),
            database=os.getenv('DB_NAME'),
            port=int(os.getenv('DB_PORT')),
            charset=os.getenv('DB_CHARSET')
        )
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise

def update_accounts_sum_income():
    """
    将子账号表(account_sub)的sum_income统计到账号表(accounts)的sum_income

    步骤:
    1. 查询每个账号下所有子账号的sum_income总和
    2. 更新accounts表中对应账号的sum_income字段
    """
    logger.info("开始统计子账号sum_income并更新到accounts表...")

    try:
        # 获取数据库连接
        conn = get_db_connection()
        cursor = conn.cursor()

        # 查询所有账号ID
        cursor.execute("SELECT id FROM accounts")
        account_ids = [row[0] for row in cursor.fetchall()]

        # 统计更新的账号数量
        updated_count = 0

        # 遍历每个账号
        for account_id in account_ids:
            try:
                # 查询该账号下所有子账号的sum_income总和
                cursor.execute(
                    "SELECT COALESCE(SUM(sum_income), 0) FROM account_sub WHERE account_id = %s",
                    (account_id,)
                )
                total_sum_income = cursor.fetchone()[0]

                # 更新accounts表中对应账号的sum_income字段
                cursor.execute(
                    "UPDATE accounts SET sum_income = %s, updated_at = %s WHERE id = %s",
                    (total_sum_income, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), account_id)
                )

                # 提交事务
                conn.commit()
                updated_count += 1

                # 每更新10个账号输出一次日志
                if updated_count % 10 == 0:
                    logger.info(f"已更新 {updated_count}/{len(account_ids)} 个账号的sum_income")

            except Exception as e:
                # 回滚事务
                conn.rollback()
                logger.error(f"更新账号ID {account_id} 的sum_income失败: {e}")

        logger.info(f"统计完成，共更新 {updated_count}/{len(account_ids)} 个账号的sum_income")

    except Exception as e:
        logger.error(f"统计子账号sum_income失败: {e}")
    finally:
        # 关闭数据库连接
        if 'conn' in locals() and conn:
            conn.close()

def update_users_sum_income():
    """
    将账号表(accounts)的sum_income统计到用户表(users)的sum_income

    步骤:
    1. 查询每个用户下所有账号的sum_income总和
    2. 更新users表中对应用户的sum_income字段
    """
    logger.info("开始统计账号sum_income并更新到users表...")

    try:
        # 获取数据库连接
        conn = get_db_connection()
        cursor = conn.cursor()

        # 查询所有用户ID
        cursor.execute("SELECT id FROM users WHERE isdel = 0")
        user_ids = [row[0] for row in cursor.fetchall()]

        # 统计更新的用户数量
        updated_count = 0

        # 遍历每个用户
        for user_id in user_ids:
            try:
                # 查询该用户下所有账号的sum_income总和
                cursor.execute(
                    "SELECT COALESCE(SUM(sum_income), 0) FROM accounts WHERE user_id = %s",
                    (user_id,)
                )
                total_sum_income = cursor.fetchone()[0]

                # 更新users表中对应用户的sum_income字段
                cursor.execute(
                    "UPDATE users SET sum_income = %s, updated_at = %s WHERE id = %s",
                    (total_sum_income, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), user_id)
                )

                # 提交事务
                conn.commit()
                updated_count += 1

                # 每更新10个用户输出一次日志
                if updated_count % 10 == 0:
                    logger.info(f"已更新 {updated_count}/{len(user_ids)} 个用户的sum_income")

            except Exception as e:
                # 回滚事务
                conn.rollback()
                logger.error(f"更新用户ID {user_id} 的sum_income失败: {e}")

        logger.info(f"统计完成，共更新 {updated_count}/{len(user_ids)} 个用户的sum_income")

    except Exception as e:
        logger.error(f"统计账号sum_income失败: {e}")
    finally:
        # 关闭数据库连接
        if 'conn' in locals() and conn:
            conn.close()

if __name__ == "__main__":
    start_time = time.time()
    logger.info("开始执行脚本...")

    try:
        # 执行统计更新操作
        update_users_sum_income()

        # 计算执行时间
        execution_time = time.time() - start_time
        logger.info(f"脚本执行完成，耗时: {execution_time:.2f} 秒")
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        # 计算执行时间
        execution_time = time.time() - start_time
        logger.info(f"脚本执行失败，耗时: {execution_time:.2f} 秒")
