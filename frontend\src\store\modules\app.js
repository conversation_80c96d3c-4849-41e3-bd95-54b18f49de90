export default {
  namespaced: true,
  
  state: {
    // 侧边栏状态
    sidebarCollapsed: localStorage.getItem('sidebarCollapsed') === 'true',
    // 消息
    unreadMessagesCount: 0,
    messages: [],
    navbarMessages: [],
  },
  
  getters: {
    isSidebarCollapsed: state => state.sidebarCollapsed,
    unreadMessagesCount: state => state.unreadMessagesCount,
    messages: state => state.messages,
    navbarMessages: state => state.navbarMessages
  },
  
  mutations: {
    SET_SIDEBAR_COLLAPSED(state, isCollapsed) {
      state.sidebarCollapsed = isCollapsed
      localStorage.setItem('sidebarCollapsed', isCollapsed)
    },
    
    
    SET_UNREAD_MESSAGES_COUNT(state, count) {
      state.unreadMessagesCount = count
    },
    
    SET_MESSAGES(state, messages) {
      state.messages = messages
    },
    SET_NAVBAR_MESSAGES(state, navbarMessages) {
      state.navbarMessages = navbarMessages
    },
    
    UPDATE_MESSAGE(state, updatedMessage) {
      const index = state.messages.findIndex(message => message.id === updatedMessage.id)
      if (index !== -1) {
        // 合并原有消息和更新的消息数据，只更新传入的字段
        const mergedMessage = { ...state.messages[index], ...updatedMessage }
        state.messages.splice(index, 1, mergedMessage)
        
        // 如果消息被标记为已读，更新未读消息计数
        if (state.messages[index].status === 0 && updatedMessage.status === 1) {
          state.unreadMessagesCount = Math.max(0, state.unreadMessagesCount - 1)
        }
      }
    },
  },
  
  actions: {
    // 显示通知
    showNotification({ commit }, notification) {
      commit('SET_NOTIFICATION', notification)
      
      // 5秒后自动隐藏通知
      setTimeout(() => {
        commit('CLEAR_NOTIFICATION')
      }, 5000)
    },
    
    toggleSidebar({ commit, state }) {
      commit('SET_SIDEBAR_COLLAPSED', !state.sidebarCollapsed)
    },
    
    
    async fetchMessages({ commit, rootState, getters, dispatch }, params = {
      page: 1,
      limit: getters.pagination.limit
    }) {
      try {
        commit('SET_LOADING', true, { root: true })
        // 使用API模块
        const messageApi = await import('@/api/message').then(m => m.default)
        const data = await messageApi.getMessages(params)
        
        if (data && data.code === 200) {
          commit('SET_MESSAGES', data.data.messages)
          commit('SET_UNREAD_MESSAGES_COUNT', data.data.unread_count)
          commit('pagination/SET_PAGINATION', {
            current: data.data.page,
            total: data.data.total
          }, { root: true })
          
          return data
        }
      } catch (error) {
      } finally {
        commit('SET_LOADING', false, { root: true })
      }
    },
    async fetchNavbarMessages({ commit }) {
      try {
        // 使用API模块
        const messageApi = await import('@/api/message').then(m => m.default)
        const data = await messageApi.getMessages({
          page: 1,
          limit: 4
        })
        
        if (data && data.code === 200) {
          commit('SET_NAVBAR_MESSAGES', data.data.messages)
          commit('SET_UNREAD_MESSAGES_COUNT', data.data.unread_count)
          return data
        }
      } catch (error) {
      }
    },
    
    async markMessageAsRead({ commit, state, dispatch }, messageId) {
      try {
        // 使用API模块
        const messageApi = await import('@/api/message').then(m => m.default)
        const data = await messageApi.markMessageAsRead(messageId)
        
        // 更新本地消息状态
        if (data && data.code === 200) {
          // 查找消息并更新状态
          const message = state.messages.find(msg => msg.id === messageId)
          if (message) {
            commit('UPDATE_MESSAGE', { 
              id: messageId, 
              status: 1  // 标记为已读
            })
          } else {
            // 如果在本地找不到消息，则刷新消息列表
            dispatch('fetchMessages')
          }
        }
      } catch (error) {
      }
    },
    
    async markAllMessagesAsRead({ commit, dispatch }) {
      commit('SET_LOADING', true, { root: true })
      try {
        // 使用API模块
        const messageApi = await import('@/api/message').then(m => m.default)
        const data = await messageApi.markAllMessagesAsRead()
        
        // 刷新消息
        if (data && data.code === 200) {
          dispatch('fetchMessages')
        }
      } catch (error) {
      }
      commit('SET_LOADING', false, { root: true })
    }
  }
}
