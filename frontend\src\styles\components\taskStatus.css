.task-status {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  border-radius: var(--border-radius-sm);
}

.task-status-not-running {
  color: var(--text-secondary);
  background-color: var(--bg-secondary);
}

.task-status-completed {
  color: var(--success-color);
  background-color: rgba(2, 192, 118, 0.1);
}

.task-status-running {
  color: var(--primary-color);
  background-color: rgba(240, 185, 11, 0.1);
}

.task-status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: var(--border-radius-circle);
  margin-right: 0.5rem;
}

.task-status-dot-not-running {
  background-color: var(--text-secondary);
}

.task-status-dot-completed {
  background-color: var(--success-color);
}

.task-status-dot-running {
  background-color: var(--primary-color);
}

.table-dark .task-status-not-running {
  color: var(--text-quaternary);
  background-color: var(--secondary-color);
}

.table-dark .task-status-dot-not-running {
  background-color: var(--text-quaternary);
}
