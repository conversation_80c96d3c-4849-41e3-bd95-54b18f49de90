<template>
  <div class="user-page">
    <div class="page-header">
      <h1 class="page-title">{{ $t('Overview.overview') }}</h1>
      <div class="page-subtitle">{{ $t('Overview.subtitle') }}</div>
    </div>

    <div class="overview-content">
      <div class="stats-cards">
        <div class="stats-card clickable-card" @click="goToDailyProfit">
          <div class="stats-icon">
            <i class="fa fa-chart-line"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value" :class="stats.todayProfit > 0 ? 'profit-positive' : 'profit-negative'">≈{{ stats.todayProfit }}</div>
            <div class="stats-label">{{ $t('Overview.todayProfit') }}</div>
            <div class="stats-trend" :class="stats.todayProfitTrend > 0 ? 'trend-up' : 'trend-down'">
              {{ $t('Overview.comparedToYesterday') }} <i :class="stats.todayProfitTrend > 0 ? 'fa fa-arrow-up' : 'fa fa-arrow-down'"></i>
              {{ Math.abs(stats.todayProfitTrend) }}%
            </div>
          </div>
          <div class="card-action-hint">
            <i class="fa fa-external-link-alt"></i>
          </div>
        </div>

        <div class="stats-card clickable-card" @click="goToAccountStatistics">
          <div class="stats-icon">
            <i class="fa fa-money-bill"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">≈{{ stats.totalAssets }}</div>
            <div class="stats-label">{{ $t('Overview.totalAssets') }}</div>
          </div>
          <div class="card-action-hint">
            <i class="fa fa-external-link-alt"></i>
          </div>
        </div>

        <div class="stats-card clickable-card" @click="goToAccountStatistics">
          <div class="stats-icon">
            <i class="fa fa-wallet"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">≈{{ stats.uncollectedAmount }}</div>
            <div class="stats-label">{{ $t('Overview.uncollectedAmount') }}</div>
            <!-- <button class="btn btn-primary btn-sm collect-btn" @click="collectFunds">
              {{ $t('Overview.collectFunds') }}
            </button> -->
          </div>
          <div class="card-action-hint">
            <i class="fa fa-external-link-alt"></i>
          </div>
        </div>

        <div class="stats-card clickable-card" @click="goToAccountStatistics">
          <div class="stats-icon">
            <i class="fa fa-coins"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value" :class="stats.totalProfit > 0 ? 'profit-positive' : 'profit-negative'">≈{{ stats.totalProfit }}</div>
            <div class="stats-label">{{ $t('Overview.totalProfit') }}</div>
          </div>
          <div class="card-action-hint">
            <i class="fa fa-external-link-alt"></i>
          </div>
        </div>

        <div class="stats-card">
          <div class="stats-icon">
            <i class="fa fa-percentage"></i>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.settlementRatio }}%</div>
            <div class="stats-label">{{ $t('Overview.settlementRatio') }}</div>
            <div class="stats-info">{{ $t('Overview.platformFee', { percentage: stats.settlementRatio }) }}</div>
          </div>
        </div>
      </div>

      <div class="overview-sections">
        <div class="overview-section">
          <h2 class="section-title">{{ $t('Overview.accountInfo') }}</h2>
          <div class="info-cards">
            <div class="info-card clickable-card" @click="goToAccountManagement">
              <div class="info-icon">
                <i class="fa fa-user-circle"></i>
              </div>
              <div class="info-content">
                <div class="info-value">{{ stats.accountCount }}</div>
                <div class="info-label">{{ $t('Overview.accountCount') }}</div>
              </div>
              <div class="card-action-hint">
                <i class="fa fa-external-link-alt"></i>
              </div>
            </div>

            <div class="info-card clickable-card" @click="goToSubAccountManagement">
              <div class="info-icon">
                <i class="fa fa-users"></i>
              </div>
              <div class="info-content">
                <div class="info-value">{{ stats.subAccountCount }}</div>
                <div class="info-label">{{ $t('Overview.subAccountCount') }}</div>
              </div>
              <div class="card-action-hint">
                <i class="fa fa-external-link-alt"></i>
              </div>
            </div>

            <div class="info-card clickable-card" @click="goToSubAccountManagement">
              <div class="info-icon">
                <i class="fa fa-check-circle"></i>
              </div>
              <div class="info-content">
                <div class="info-value">{{ stats.completedSubAccountCount }}</div>
                <div class="info-label">{{ $t('Overview.completedSubAccountCount') }}</div>
              </div>
              <div class="card-action-hint">
                <i class="fa fa-external-link-alt"></i>
              </div>
            </div>

            <div class="info-card clickable-card" @click="goToSubAccountManagement">
              <div class="info-icon">
                <i class="fa fa-sync"></i>
              </div>
              <div class="info-content">
                <div class="info-value">{{ stats.runningSubAccountCount }}</div>
                <div class="info-label">{{ $t('Overview.runningSubAccountCount') }}</div>
              </div>
              <div class="card-action-hint">
                <i class="fa fa-external-link-alt"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="overview-section">
          <h2 class="section-title">{{ $t('Overview.promotionInfo') }}</h2>
          <div class="promotion-card">
            <div class="promotion-header">
              <div class="promotion-icon">
                <i class="fa fa-user-plus"></i>
              </div>
              <div class="promotion-title">
                <h3>{{ $t('Overview.referralCount') }}</h3>
                <div class="promotion-value">{{ stats.referralCount }}</div>
              </div>
            </div>
            <div class="promotion-content">
              <div class="promotion-link">
                <div class="link-label">{{ $t('Overview.referralLink') }}</div>
                <div class="link-value">
                  <input type="text" readonly :value="stats.referralLink" class="form-control" />
                  <button class="btn btn-primary btn-sm" @click="copyReferralLink">
                    <i class="fa fa-copy"></i> {{ $t('Overview.copy') }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- <div class="overview-section">
          <h2 class="section-title">{{ $t('Overview.profitTrend') }}</h2>
          <div class="chart-container">
            <canvas ref="profitChart"></canvas>
          </div>
        </div> -->
      </div>
    </div>

    <!-- 归集资金弹窗 -->
    <!-- <div class="modal" v-if="showCollectModal">
      <div class="modal-backdrop" @click="showCollectModal = false"></div>
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{{ $t('Overview.collectFundsModalTitle') }}</h5>
            <button type="button" class="btn-close" @click="showCollectModal = false">
              <i class="fa fa-times"></i>
            </button>
          </div>
          <div class="modal-body">
            <p>{{ $t('Overview.collectFundsModalConfirmText') }}</p>
            <p>{{ $t('Overview.collectFundsModalUncollectedAmount', { amount: stats.uncollectedAmount }) }}</p>
            <p>{{ $t('Overview.collectFundsModalDescription') }}</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="showCollectModal = false">
              {{ $t('common.cancel') }}
            </button>
            <button type="button" class="btn btn-primary" @click="confirmCollect">
              {{ $t('Overview.collectFundsModalConfirm') }}
            </button>
          </div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import { ref, onMounted, reactive } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import Chart from 'chart.js/auto'

export default {
  name: 'OverviewPage',
  setup() {
    const store = useStore()
    const { t } = useI18n()
    const router = useRouter()
    const profitChart = ref(null)
    const showCollectModal = ref(false)


    // 数据对象
    const stats = reactive({
      todayProfit: 0,
      todayProfitTrend: 0,
      uncollectedAmount: 0,
      totalProfit: 0,
      totalAssets: 0,
      settlementRatio: 0,
      accountCount: 0,
      subAccountCount: 0,
      completedSubAccountCount: 0,
      runningSubAccountCount: 0,
      referralCount: 0,
      referralLink: ''
    })

    // 获取仪表盘数据
    const fetchDashboardData = async () => {
      try {
        const response = await store.dispatch('account/getDashboardData')
        if (response.code === 200) {
          stats.todayProfit = response.data.todayProfit || 0
          stats.todayProfitTrend = response.data.todayProfitTrend || 0
          stats.uncollectedAmount = response.data.uncollectedAmount || 0
          stats.totalProfit = response.data.totalProfit || 0
          stats.totalAssets = response.data.totalAssets || 0
          stats.settlementRatio = response.data.settlementRatio || 0
          stats.accountCount = response.data.accountCount || 0
          stats.subAccountCount = response.data.subAccountCount || 0
          stats.completedSubAccountCount = response.data.completedSubAccountCount || 0
          stats.runningSubAccountCount = response.data.runningSubAccountCount || 0
          stats.referralCount = response.data.referralCount || 0
          stats.referralLink = window.location.origin + '/auth/register?invitation=' + (response.data.invitation || '')


        }
      } catch (error) {
        console.error('获取仪表盘数据失败:', error)
        store.dispatch('showNotification', {
          type: 'error',
          title: '获取数据失败',
          message: '无法获取仪表盘数据，请稍后再试'
        })
      }
    }


    // 跳转到每日收益页面
    const goToDailyProfit = () => {
      router.push('/user/daily-profit')
    }

    // 跳转到账户统计页面
    const goToAccountStatistics = () => {
      router.push('/user/account-statistics')
    }

    // 跳转到账户管理页面
    const goToAccountManagement = () => {
      router.push('/user/account-management')
    }

    // 跳转到子账户管理页面
    const goToSubAccountManagement = () => {
      router.push('/user/sub-account-management')
    }

    // 复制推广链接
    const copyReferralLink = () => {
      navigator.clipboard.writeText(stats.referralLink).then(() => {
        store.dispatch('showNotification', {
          type: 'success',
          title: '复制成功',
          message: '推广链接已复制到剪贴板'
        })
      }).catch(err => {
        store.dispatch('showNotification', {
          type: 'error',
          title: '复制失败',
          message: '无法复制到剪贴板，请手动复制'
        })
      })
    }

    // 归集资金
    const collectFunds = () => {
      showCollectModal.value = true
    }

    // 确认归集
    const confirmCollect = async () => {
      try {
        const response = await store.dispatch('account/collectFunds')
        if (response.code === 200) {
          showCollectModal.value = false
          stats.uncollectedAmount = 0
          store.dispatch('showNotification', {
            type: 'success',
            title: '归集成功',
            message: '资金已成功归集到您的主账户'
          })

          // 重新获取仪表盘数据
          fetchDashboardData()
        }
      } catch (error) {
        console.error('归集资金失败:', error)
        store.dispatch('showNotification', {
          type: 'error',
          title: '归集失败',
          message: error.message || '归集资金失败，请稍后再试'
        })
      }
    }

    // 初始化图表
    const initChart = async () => {
      try {
        // 获取收益趋势数据
        const response = await store.dispatch('account/getProfitTrend')

        const ctx = profitChart.value.getContext('2d')

        // 使用API返回的数据
        const labels = response.data.labels || []
        const profitData = response.data.data || []

        const data = {
          labels: labels,
          datasets: [
            {
              label: '月收益',
              data: profitData,
              borderColor: 'rgb(75, 192, 192)',
              backgroundColor: 'rgba(75, 192, 192, 0.2)',
              tension: 0.4,
              fill: true
            }
          ]
        }

        new Chart(ctx, {
          type: 'line',
          data: data,
          options: {
            responsive: true,
            plugins: {
              legend: {
                position: 'top',
              },
              title: {
                display: true,
                text: '月度收益趋势'
              }
            }
          }
        })
      } catch (error) {
        console.error('初始化图表失败:', error)
        store.dispatch('showNotification', {
          type: 'error',
          title: '图表加载失败',
          message: '无法加载收益趋势图表，请稍后再试'
        })
      }
    }

    onMounted(() => {
      fetchDashboardData()
      // initChart()
    })

    return {
      stats,
      profitChart,
      showCollectModal,
      goToDailyProfit,
      goToAccountStatistics,
      goToAccountManagement,
      goToSubAccountManagement,
      copyReferralLink,
      collectFunds,
      confirmCollect
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/user/common.css';
@import '@/styles/views/user/overview.css';
</style>
