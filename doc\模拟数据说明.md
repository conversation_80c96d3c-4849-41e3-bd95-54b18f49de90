1. 生成用户信息, 表users, 密码使用$2y$10$k6MnV0pr4AVMWuW8hkVCge.VOcAsntd7uoCr9Y2ChuOmtEDO3.bWS,数值相关字段默认0, 其他根据需求随机生成, 生成时间为2023年1月1日00:00:00.
创建一个用户名为user7,pid为0;
创建一个用户名为user6,pid为user7的id;
创建一个用户名为user5,pid为user6的id;
创建一个用户名为user4,pid为user5的id;
创建一个用户名为user3,pid为user4的id;
创建一个用户名为user2,pid为user3的id;
创建一个用户名为user1,pid为user2的id;
创建99个用户,pid为user7的id;
创建49个用户,pid为user6的id;
创建29个用户,pid为user5的id;
创建19个用户,pid为user4的id;
创建9个用户,pid为user3的id;
创建4个用户,pid为user2的id;
创建1个用户,pid为user1的id;
2. 生成账号和子账号,表accounts和account_sub, 为每个前面新增的用户生成一个账号, 每个账号生成20个子账号, 生成时间为2023年1月1日00:00:00.
账号初始数据:
账号状态status=1,
所有资产total_wallet_balance=20000,
所有子账号资产sub_wallet_balance=20000,
母账号资产wallet_balance=0,
未归集钱包collect_wallet=0,
累积收益sum_income=0,
任务进度task_status=1,
其他数值相关字段默认0,
其他字段根据字段意思或者注释随机生成.
子账号初始数据:
账号状态status=1,
所有资产wallet_balance=1000,
未归集钱包collect_wallet=0,
累积收益sum_income=0,
任务进度task_status=1,
其他数值相关字段默认0,
其他字段根据字段意思或者注释随机生成.
3. 生成资金费率明细,表funding_rate_logs, 为每个子账号生成数据, 生成时间从2023年1月1日00:00:00开始, 到funding_rate_history表的calc_time字段的最后时间结束,取funding_rate_history表symbol_id=1的数据,每条对应的数据生成一条,生成时间为funding_rate_history表的calc_time字段的值,注意时间类型的转换.
资金费率明细初始数据:
费率币种coin=BTC,
资金费率amount=1000*funding_rate_history表的last_funding_rate/funding_rate_history表的mark_price,
费率标记价格markPrice=funding_rate_history表的mark_price,
USDT价值usdt=费率标记价格markPrice*资金费率amount,
其他数值相关字段默认0,
其他字段根据字段意思或者注释随机生成.
同时根据USDT价值usdt增加:
users表的sum_income字段,
accounts表的total_wallet_balance\sub_wallet_balance\collect_wallet\sum_income字段,
account_sub表的wallet_balance\collect_wallet\sum_income字段.
4. 生成交收记录, 表settlement_record,从2023年2月1日00:00:00开始, 每个月的1号00:00:00生成数据, 生成时间为每个月的1号00:00:00,为每个账号生成一条数据.
交收记录初始数据:
归集金额profit=上个月生成资金费率明细表funding_rate_logs该账号account_id下的USDT价值usdt总和,
交收金额amount=归集金额profit*0.5,
最晚交收时间last_time=生成时间+10天,
交收比例ratio=0.5,
状态status=1,
交收时间settlement_time=生成时间+1天,
其他数值相关字段默认0,
其他字段根据字段意思或者注释随机生成.
同时根据上个月生成资金费率明细表funding_rate_logs子账号account_sub_id下的USDT价值usdt总和减少:
account_sub表的wallet_balance\collect_wallet字段.
同时根据上个月生成资金费率明细表funding_rate_logs账号account_id下的USDT价值usdt总和减少:
accounts表的sub_wallet_balance\collect_wallet字段.
同时根据上个月生成资金费率明细表funding_rate_logs账号account_id下的USDT价值usdt总和增加:
accounts表的wallet_balance字段.
5. 生成代理佣金记录, 表agent_commission_records,循环所有用户,从用户的下级用户和下级的用户的下级用户递归下去的所有用户的交收记录settlement_record表中归集金额profit,根据用户代理层级关系和分成比例,计算代理佣金,生成时间为交收记录表settlement_time的时间. 
用户代理层级关系和分成比例从表agent_level_config获取:
当用户表users的用户level=0时, 当前用户获得下级1级用户的settlement_record表归集金额profit的agent_level_config表中level=0的radio比例的佣金.
当用户表users的用户level=1时, 当前用户获得下级1级用户的settlement_record表归集金额profit的agent_level_config表中level=0和1的radio之和比例的佣金.
当用户表users的用户level=2时, 当前用户获得下级1级用户的settlement_record表归集金额profit的agent_level_config表中level=0和1的radio之和比例的佣金, 当前用户获得下级2级用户的settlement_record表归集金额profit的agent_level_config表中level=2的radio之和比例的佣金.
当用户表users的用户level=3时, 当前用户获得下级1级用户的settlement_record表归集金额profit的agent_level_config表中level=0和1的radio之和比例的佣金, 当前用户获得下级2级用户的settlement_record表归集金额profit的agent_level_config表中level=2的radio之和比例的佣金,当前用户获得下级3级用户的settlement_record表归集金额profit的agent_level_config表中level=3的radio之和比例的佣金.
以此类推到用户表users的用户level=7.
表agent_commission_records数据:
收益金额revenue_amount=settlement_record表归集金额profit,
返佣比例agent_ratio=根据用户代理层级关系和分成比例计算,
佣金金额amount=收益金额revenue_amount*返佣比例agent_ratio,
原始金额original_balance=当前用户users表balance字段,
剩余金额remaining_balance=原始金额original_balance+佣金金额amount,
状态status=1,
生成时间create_time=settlement_record表的settlement_time字段的值,
其他数值相关字段默认0,
其他字段根据字段意思或者注释随机生成.
同时根据佣金金额amount增加:
users表balance字段.
6. 生成代理钱包提现记录, 表agent_withdrawal_records, 需要在生成代理佣金记录时执行, 当生成代理佣金记录后,users表balance字段大于1000时, 生成数据, 生成时间为users表balance字段大于1000时agent_commission_records的生成时间.
表agent_withdrawal_records数据:
代币网络network字段跳过,让他使用默认值,
提现地址address字段使用随机值,
原始余额original_balance=users表balance字段,
提款金额amount=1000,
手续费fee=1,
到账金额amount_received=提款金额amount-fee,
剩余余额remaining_balance=原始余额original_balance-提款金额amount,
状态status=1,
生成时间create_time=users表balance字段大于1000时agent_commission_records的生成时间,
其他数值相关字段默认0,
其他字段根据字段意思或者注释随机生成.

