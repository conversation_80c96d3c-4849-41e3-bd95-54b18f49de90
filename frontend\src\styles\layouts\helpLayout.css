.help-container {
  display: flex;
  min-height: calc(100vh - 64px - 300px);
  /* Adjust based on navbar and footer heights */
}

.help-sidebar {
  width: 240px;
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  flex-shrink: 0;
}

.help-sidebar-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.help-sidebar-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.help-sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.help-sidebar-item {
  border-bottom: 1px solid var(--border-color);
}

.help-sidebar-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-secondary);
  text-decoration: none;
  transition: background-color var(--transition-fast), color var(--transition-fast);
}

.help-sidebar-link:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.help-sidebar-item.active .help-sidebar-link {
  background-color: rgba(240, 185, 11, 0.1);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  border-left: 3px solid var(--primary-color);
}

.help-sidebar-link i {
  margin-right: var(--spacing-md);
  width: 20px;
  text-align: center;
}

.help-content {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-x: hidden;
}

.help-content-body {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-md);
}

@media (max-width: 992px) {
  .help-container {
    flex-direction: column;
  }

  .help-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .help-sidebar-menu {
    display: flex;
    overflow-x: auto;
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE and Edge */
  }

  .help-sidebar-menu::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Opera */
  }

  .help-sidebar-item {
    border-bottom: none;
    border-right: 1px solid var(--border-color);
    flex-shrink: 0;
  }

  .help-sidebar-link {
    padding: var(--spacing-sm) var(--spacing-md);
    flex-direction: column;
    text-align: center;
  }

  .help-sidebar-link i {
    margin-right: 0;
    margin-bottom: var(--spacing-xs);
  }

  .help-content {
    padding: var(--spacing-md);
  }
}
/* 帮助页面侧边栏样式 - 科技感设计 */

.help-sidebar {
  width: 240px;
  background: linear-gradient(145deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-right: 1px solid rgba(var(--border-color-rgb), 0.1);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

/* 添加科技感背景元素 */
.help-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 10% 20%, rgba(var(--primary-color-rgb), 0.03) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(var(--primary-color-rgb), 0.03) 0%, transparent 20%),
    linear-gradient(120deg, rgba(var(--primary-color-rgb), 0.02) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

.help-sidebar-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid rgba(var(--border-color-rgb), 0.1);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.help-sidebar-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  position: relative;
  padding-left: var(--spacing-md);
}

.help-sidebar-header h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 18px;
  background: linear-gradient(to bottom, var(--primary-color), var(--primary-color-light));
  border-radius: 2px;
}

.help-sidebar-menu {
  list-style: none;
  padding: var(--spacing-sm) 0;
  margin: 0;
  position: relative;
  z-index: 1;
}

.help-sidebar-item {
  margin: var(--spacing-xs) 0;
  position: relative;
  transition: all var(--transition-normal);
}

.help-sidebar-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-normal);
  border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
  margin-right: var(--spacing-sm);
  position: relative;
  overflow: hidden;
}

/* 悬停效果 */
.help-sidebar-link:hover {
  color: var(--text-primary);
  background: rgba(var(--bg-secondary-rgb), 0.5);
  transform: translateX(4px);
}

/* 悬停时的光效 */
.help-sidebar-link::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.help-sidebar-link:hover::after {
  left: 100%;
}

/* 活动项目样式 */
.help-sidebar-item.active .help-sidebar-link {
  background: linear-gradient(
    90deg,
    rgba(var(--primary-color-rgb), 0.1) 0%,
    rgba(var(--primary-color-rgb), 0.05) 70%,
    transparent 100%
  );
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  border-left: 3px solid var(--primary-color);
  padding-left: calc(var(--spacing-lg) - 3px);
  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.1);
}

/* 图标样式 */
.help-sidebar-link i {
  margin-right: var(--spacing-md);
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  border-radius: var(--border-radius-circle);
  background: rgba(var(--bg-tertiary-rgb), 0.5);
  color: var(--text-secondary);
  transition: all var(--transition-normal);
}

.help-sidebar-item.active .help-sidebar-link i,
.help-sidebar-link:hover i {
  background: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  transform: scale(1.1);
}

/* 响应式样式 */
@media (max-width: 992px) {
  .help-container {
    flex-direction: column;
  }

  .help-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(90deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  }

  .help-sidebar-menu {
    display: flex;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .help-sidebar-menu::-webkit-scrollbar {
    display: none;
  }

  .help-sidebar-item {
    border-bottom: none;
    border-right: none;
    flex-shrink: 0;
    margin: 0 var(--spacing-xs);
  }

  .help-sidebar-link {
    padding: var(--spacing-sm) var(--spacing-md);
    flex-direction: column;
    text-align: center;
    border-radius: var(--border-radius-md);
    margin-right: 0;
  }

  .help-sidebar-item.active .help-sidebar-link {
    border-left: none;
    border-bottom: 2px solid var(--primary-color);
    padding-left: var(--spacing-md);
    padding-bottom: calc(var(--spacing-sm) - 2px);
  }

  .help-sidebar-link i {
    margin-right: 0;
    margin-bottom: var(--spacing-xs);
  }
}