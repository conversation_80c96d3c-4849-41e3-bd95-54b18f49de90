.register-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
    padding: var(--spacing-lg);
  }
  
  .register-container {
    width: 100%;
    max-width: 500px;
    background-color: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    overflow: hidden;
  }
  
  .register-header {
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
    text-align: center;
  }
  
  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
  }
  
  .logo img {
    width: 40px;
    height: 40px;
    margin-right: var(--spacing-sm);
  }
  
  .logo h1 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin: 0;
  }
  
  .register-title {
    font-size: 1.75rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
  }
  
  .register-subtitle {
    color: var(--text-secondary);
    margin-bottom: 0;
  }
  
  .register-form {
    padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
  }
  
  .form-group {
    margin-bottom: var(--spacing-md);
  }
  
  .form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
  }
  
  .input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .input-wrapper i {
    position: absolute;
    left: var(--spacing-md);
    color: var(--text-tertiary);
  }
  
  .input-wrapper input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) calc(var(--spacing-md) * 2 + 1rem);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--bg-input);
    color: var(--text-primary);
    font-size: 1rem;
    transition: border-color 0.3s, box-shadow 0.3s;
  }
  
  .input-wrapper input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
    outline: none;
  }
  
  .toggle-password {
    position: absolute;
    right: 45px; /* 调整为适合输入框的内边距 */
    top: 40%; 
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: 0;
  }
  
  .error-message {
    color: var(--error-color);
    font-size: 0.85rem;
    margin-top: var(--spacing-xs);
  }
  
  .captcha-group {
    margin-bottom: var(--spacing-md);
  }
  
  .captcha-wrapper {
    display: flex;
    gap: var(--spacing-md);
  }
  
  .captcha-wrapper .input-wrapper {
    flex: 1;
  }
  
  .captcha-image {
    width: 120px;
    height: 40px;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    cursor: pointer;
  }
  
  .captcha-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .terms-group {
    margin-bottom: var(--spacing-lg);
  }
  
  .terms-checkbox {
    display: flex;
    align-items: flex-start;
  }
  
  .terms-checkbox input {
    margin-right: var(--spacing-xs);
    margin-top: 0.25rem;
  }
  
  .terms-checkbox label {
    margin-bottom: 0;
    font-weight: normal;
    line-height: 1.5;
  }
  
  .terms-checkbox a {
    color: var(--primary-color);
    text-decoration: none;
  }
  
  .btn-register {
    width: 100%;
    padding: var(--spacing-md);
    background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: background 0.3s, transform 0.1s;
    position: relative;
  }
  
  .btn-register:hover {
    background: linear-gradient(90deg, var(--primary-color-dark), var(--primary-color));
  }
  
  .btn-register:active {
    transform: translateY(1px);
  }
  
  .btn-register:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
  
  .loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  
  .register-footer {
    margin-top: var(--spacing-lg);
    text-align: center;
    color: var(--text-secondary);
  }
  
  .register-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
  }
  
  /* 弹窗样式 */
  .modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-index-modal);
  }
  
  .modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }
  
  .modal-dialog {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    max-height: 90vh;
  }
  
  .modal-content {
    background-color: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: 90vh;
  }
  
  .modal-header {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .modal-title {
    font-size: 1.25rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
  }
  
  .btn-close {
    background: none;
    border: none;
    color: var(--text-tertiary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0;
  }
  
  .modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
    flex: 1;
  }
  
  .terms-content {
    color: var(--text-primary);
  }
  
  .terms-content h3 {
    font-size: 1.1rem;
    font-weight: var(--font-weight-semibold);
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-sm);
  }
  
  .terms-content h3:first-child {
    margin-top: 0;
  }
  
  .terms-content p {
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
  }
  
  .modal-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-sm);
  }
  
  .btn-secondary {
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: 0.9rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: background 0.3s;
  }
  
  .btn-secondary:hover {
    background-color: var(--bg-hover);
  }
  
  .btn-primary {
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--primary-color);
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius-md);
    font-size: 0.9rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: background 0.3s;
  }
  
  .btn-primary:hover {
    background-color: var(--primary-color-dark);
  }
  
  @media (max-width: 480px) {
    .register-container {
      box-shadow: none;
    }
    
    .captcha-wrapper {
      flex-direction: column;
    }
    
    .captcha-image {
      width: 100%;
    }
    
    .modal-dialog {
      max-width: 100%;
      margin: var(--spacing-md);
    }
  }