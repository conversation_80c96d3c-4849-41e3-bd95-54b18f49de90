.fund-security-page {
  padding: var(--spacing-xl) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.page-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 800px;
  margin: 0 auto;
}

.security-content {
  margin-top: var(--spacing-xl);
}

.security-section {
  margin-bottom: var(--spacing-xxl);
}

.section-title {
  font-size: 1.75rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  position: relative;
  padding-bottom: var(--spacing-sm);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  border-radius: 2px;
}

.section-content {
  display: flex;
  align-items: flex-start;
}

.section-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-lg);
  color: var(--text-light);
  font-size: 2rem;
  flex-shrink: 0;
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}

.section-text {
  flex: 1;
}

.section-text p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.security-tips {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.security-tips li {
  position: relative;
  padding-left: 30px;
  margin-bottom: var(--spacing-sm);
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
}

.security-tips li::before {
  content: '\f00c';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  left: 0;
  top: 0;
  color: var(--primary-color);
}

.security-cta {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  margin-top: var(--spacing-xxl);
}

.cta-title {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.cta-text {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 800px;
  margin: 0 auto var(--spacing-lg);
}

.cta-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

@media (max-width: 767px) {
  .page-title {
    font-size: 2rem;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .section-content {
    flex-direction: column;
  }
  
  .section-icon {
    margin-right: 0;
    margin-bottom: var(--spacing-md);
  }
  
  .cta-title {
    font-size: 1.5rem;
  }
}
