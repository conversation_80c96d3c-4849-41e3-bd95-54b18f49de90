<?php
// +----------------------------------------------------------------------
// | Captcha配置文件
// +----------------------------------------------------------------------

return [
    //验证码位数
    'length'   => 4,
    // 验证码字符集合
    'codeSet'  => '2345678abcdefhijkmnpqrstuvwxyzABCDEFGHJKLMNPQRTUVWXY',
    // 验证码过期时间
    'expire'   => 300,
    // 是否使用中文验证码
    'useZh'    => false,
    // 是否使用算术验证码
    'math'     => true,
    // 是否使用背景图
    'useImgBg' => false,
    //验证码字符大小
    'fontSize' => 20,
    // 是否使用混淆曲线
    'useCurve' => false,
    //是否添加杂点
    'useNoise' => true,
    // 验证码字体 不设置则随机
    'fontttf'  => '',
    //背景颜色
    'bg'       => [255, 255, 255],
    // 验证码图片高度
    'imageH'   => 50,
    // 验证码图片宽度
    'imageW'   => 150,
    // 验证码图片透明度
    'alpha'    => 0,
    // 是否采用API模式生成
    'api'      => true,

    // 添加额外的验证码设置
    // verify => [
    //     'length'=>4,
    //    ...
    //],
];
