<?php
declare (strict_types = 1);

namespace app\middleware;

use Closure;
use think\Config;
use think\Request;
use think\Response;
use app\utils\ResponseHelper;

class Cors
{
    protected $config;

    public function __construct(Config $config)
    {
        $this->config = $config;
    }

    /**
     * 处理跨域请求
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle($request, Closure $next)
    {
        // 获取配置的允许跨域的域名
        $allowOrigin = env('CORS_ALLOW_ORIGIN', '*');
        $allowHeaders = env('CORS_ALLOW_HEADERS', 'Content-Type,Authorization,X-Requested-With');
        $allowMethods = env('CORS_ALLOW_METHODS', 'GET,POST,PUT,DELETE,OPTIONS');
        
        $origin = $request->header('origin');
        
        // 如果配置了多个域名，以逗号分隔，则检查当前请求的origin是否在允许列表中
        if ($allowOrigin !== '*' && $origin) {
            $allowOrigins = explode(',', $allowOrigin);
            if (in_array($origin, $allowOrigins)) {
                $allowOrigin = $origin;
            }
        }
        
        $header = [
            'Access-Control-Allow-Origin'      => $allowOrigin,
            'Access-Control-Allow-Headers'     => $allowHeaders,
            'Access-Control-Allow-Methods'     => $allowMethods,
            'Access-Control-Allow-Credentials' => 'true',
        ];

        // 如果是OPTIONS请求，直接返回响应
        if ($request->method(true) == 'OPTIONS') {
            return Response::create()->code(204)->header($header);
        }

        // 其他请求方法
        $response = $next($request);
        
        // 将CORS头添加到响应中
        return $response->header($header);
    }
} 