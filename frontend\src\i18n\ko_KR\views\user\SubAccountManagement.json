﻿{
  "SubAccountManagement": {
    "accountAssets": "계정 자산",
    "addApiKey": "API 키 추가",
    "agreeTerms": "동의합니다",
    "agreeTermsError": "API 사용 약관에 동의하세요",
    "all": "전체",
    "apiTerms": "API 사용 약관",
    "apiTermsContent": "API 키 사용 시 관련 약관과 조건을 준수하고 API 키의 보안을 보장해야 합니다.",
    "apikey": "API Key",
    "apikeyAdded": "API 키가 추가되었습니다",
    "apikeyUpdated": "API 키가 업데이트되었습니다",
    "cancel": "취소",
    "completionTime": "완료 시간",
    "editApiKey": "API 키 편집",
    "error": "오류",
    "getSubAccountError": "서브 계정 세부정보 가져오기 실패",
    "loading": "로딩 중...",
    "noData": "서브 계정 데이터가 없습니다",
    "operation": "작업",
    "parentAccount": "메인 계정",
    "profitDetails": "수익 상세",
    "save": "저장",
    "saveApiKeyError": "API 키 저장 실패",
    "secretkey": "Secret Key",
    "selectAccount": "계정 선택",
    "statistics": "서브 계정 통계",
    "status": "상태",
    "status_disabled": "비활성화됨",
    "status_enabled": "활성화됨",
    "status_expired": "만료됨",
    "status_inactive": "미활성화",
    "subAccount": "서브 계정",
    "subAccountCount": "서브 계정 수량",
    "subtitle": "서브 계정을 관리하세요",
    "success": "성공",
    "taskStatus_completed": "완료됨",
    "taskStatus_notRunning": "실행 안함",
    "taskStatus_running": "실행 중",
    "task_status": "작업 상태",
    "title": "서브 계정 관리",
    "totalAssets": "총 자산",
    "totalProfit": "총 수익",
    "uncollectedFunds": "미수집 자금",
    "apikeyError": "API Key를 입력하세요",
    "secretkeyError": "Secret Key를 입력하세요"
  }
}
