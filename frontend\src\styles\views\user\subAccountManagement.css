
.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
}

.status-enabled {
  background-color: var(--success-color-light);
  color: var(--success-color);
}

.status-inactive {
  background-color: var(--warning-color-light);
  color: var(--warning-color);
}

.status-expired {
  background-color: var(--error-color-light);
  color: var(--error-color);
}

.status-disabled {
  background-color: var(--secondary-color-light);
  color: var(--secondary-color);
}
/* 操作按钮样式 */
.action-buttons {
  display: flex;
}