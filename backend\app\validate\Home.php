<?php
declare(strict_types=1);

namespace app\validate;

use think\Validate;

/**
 * 首页相关验证器
 */
class Home extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        // 暂无需验证的字段
    ];

    /**
     * 错误消息
     * @var array
     */
    protected $message = [
        // 暂无错误消息
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        'index' => [],
        'banner' => [],
        'features' => [],
        'statistics' => []
    ];

    /**
     * 获取首页数据场景
     */
    public function sceneIndex()
    {
        return $this;
    }

    /**
     * 获取横幅数据场景
     */
    public function sceneBanner()
    {
        return $this;
    }

    /**
     * 获取特点数据场景
     */
    public function sceneFeatures()
    {
        return $this;
    }

    /**
     * 获取统计数据场景
     */
    public function sceneStatistics()
    {
        return $this;
    }
}
