<template>
  <div class="user-page">
    <div class="page-header">
      <h1 class="page-title">{{ $t('PersonalInfo.personalInfo') }}</h1>
      <div class="page-subtitle">{{ $t('PersonalInfo.subtitle') }}</div>
    </div>
    
    <div class="info-content">
      <div class="info-section">
        <h2 class="section-title">{{ $t('PersonalInfo.basicInfo') }}</h2>
        <div class="info-card">
          <div class="info-avatar">
            <i class="fa fa-user avatar-icon"></i>
          </div>
          <div class="info-details">
            <div class="info-item">
              <div class="item-label">{{ $t('PersonalInfo.username') }}</div>
              <div class="item-value">{{ userInfo.username }}</div>
            </div>
            <div class="info-item">
              <div class="item-label">{{ $t('PersonalInfo.accountLevel') }}</div>
              <div class="item-value">
                <span class="badge" :class="'level-' + userInfo.level">{{ userInfo.level }}</span>
              </div>
            </div>
            <div class="info-item">
              <div class="item-label">{{ $t('PersonalInfo.logintime') }}</div>
              <div class="item-value">{{ userInfo.logintime }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="info-section">
        <h2 class="section-title">{{ $t('PersonalInfo.contactInfo') }}</h2>
        <div class="contact-cards">
          <div class="contact-card">
            <div class="contact-icon">
              <i class="fa fa-envelope"></i>
            </div>
            <div class="contact-content">
              <div class="contact-type">{{ $t('PersonalInfo.email') }}</div>
              <div class="contact-value">{{ userInfo.email || $t('PersonalInfo.notBound') }}</div>
              <div class="contact-status" :class="userInfo.emailVerified ? 'status-verified' : 'status-unverified'">
                <i :class="userInfo.emailVerified ? 'fa fa-check-circle' : 'fa fa-exclamation-circle'"></i>
                {{ userInfo.emailVerified ? $t('PersonalInfo.verified') : $t('PersonalInfo.unverified') }}
              </div>
              <div class="contact-actions">
                <button class="btn btn-primary btn-sm" @click="showEmailModal = true">
                  {{ userInfo.email ? $t('PersonalInfo.modify') : $t('PersonalInfo.bind') }}
                </button>
                <button v-if="userInfo.email && !userInfo.emailVerified" class="btn btn-outline-primary btn-sm" @click="sendEmailVerification">
                  {{ $t('PersonalInfo.sendVerification') }}
                </button>
              </div>
            </div>
          </div>
          
          <div class="contact-card">
            <div class="contact-icon">
              <i class="fa fa-mobile-alt"></i>
            </div>
            <div class="contact-content">
              <div class="contact-type">{{ $t('PersonalInfo.phone') }}</div>
              <div class="contact-value">{{ userInfo.phone || $t('PersonalInfo.notBound') }}</div>
              <div class="contact-status" :class="userInfo.phoneVerified ? 'status-verified' : 'status-unverified'">
                <i :class="userInfo.phoneVerified ? 'fa fa-check-circle' : 'fa fa-exclamation-circle'"></i>
                {{ userInfo.phoneVerified ? $t('PersonalInfo.verified') : $t('PersonalInfo.unverified') }}
              </div>
              <div class="contact-actions">
                <button class="btn btn-primary btn-sm" @click="showPhoneModal = true">
                  {{ userInfo.phone ? $t('PersonalInfo.modifyPhone') : $t('PersonalInfo.bindPhone') }}
                </button>
                <button v-if="userInfo.phone && !userInfo.phoneVerified" class="btn btn-outline-primary btn-sm" @click="sendPhoneVerification">
                  {{ $t('PersonalInfo.sendCode') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="info-section">
        <h2 class="section-title">{{ $t('PersonalInfo.securitySettings') }}</h2>
        <div class="security-cards">
          <div class="security-card">
            <div class="security-icon">
              <i class="fa fa-lock"></i>
            </div>
            <div class="security-content">
              <div class="security-type">{{ $t('PersonalInfo.loginPassword') }}</div>
              <!-- <div class="security-value">{{ $t('PersonalInfo.lastModified') }}：{{ formatDate(userInfo.lastPasswordChange) }}</div> -->
              <div class="security-actions">
                <button class="btn btn-primary btn-sm" @click="showPasswordModal = true">
                  {{ $t('PersonalInfo.modifyPassword') }}
                </button>
              </div>
            </div>
          </div>
          
          <div class="security-card">
            <div class="security-icon">
              <i class="fa fa-shield-alt"></i>
            </div>
            <div class="security-content">
              <div class="security-type">{{ $t('PersonalInfo.twoFactorAuth') }}</div>
              <div class="security-value">
                <span :class="userInfo.twoFactorEnabled ? 'status-verified' : 'status-unverified'">
                  <i :class="userInfo.twoFactorEnabled ? 'fa fa-check-circle' : 'fa fa-times-circle'"></i>
                  {{ userInfo.twoFactorEnabled ? $t('PersonalInfo.enabled') : $t('PersonalInfo.disabled') }}
                </span>
              </div>
              <div class="security-actions">
                <button class="btn btn-primary btn-sm" @click="showTwoFactorModal = true">
                  {{ userInfo.twoFactorEnabled ? $t('PersonalInfo.manageTwoFactor') : $t('PersonalInfo.enableTwoFactor') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <Modal 
      v-model="showPasswordModal" 
      :title="$t('PersonalInfo.passwordModalTitle')"
      @confirm="savePassword"
    >
      <form @submit.prevent="savePassword">
        <div class="form-group">
          <label for="currentPassword">{{ $t('PersonalInfo.passwordModalCurrentPassword') }}</label>
          <input type="password" id="currentPassword" v-model="passwordForm.currentPassword" class="form-control" required />
        </div>
        <div class="form-group">
          <label for="newPassword">{{ $t('PersonalInfo.passwordModalNewPassword') }}</label>
          <input type="password" id="newPassword" v-model="passwordForm.newPassword" class="form-control" required />
          <small class="form-text text-muted">{{ $t('PersonalInfo.passwordModalPasswordTip') }}</small>
        </div>
        <div class="form-group">
          <label for="confirmPassword">{{ $t('PersonalInfo.passwordModalConfirmPassword') }}</label>
          <input type="password" id="confirmPassword" v-model="passwordForm.confirmPassword" class="form-control" required />
        </div>
      </form>
    </Modal>

  </div>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { focusInput, showError, showSuccess } from '@/utils/utils'

import Modal from '@/components/common/Modal.vue'

export default {
  components: {
    Modal,
  },
  name: 'PersonalInfoPage',
  setup() {
    const router = useRouter()
    const store = useStore()
    const { t } = useI18n()
    
    // 用户信息
    const userInfo = reactive({
      username: '',
      email: '',
      emailVerified: false,
      phone: '',
      phoneVerified: false,
      avatar: '',
      logintime: '',
      lastPasswordChange: '',
      twoFactorEnabled: false,
      level: 0,
    })
    
    // 弹窗显示状态
    const showEmailModal = ref(false)
    const showPhoneModal = ref(false)
    const showPasswordModal = ref(false)
    const showTwoFactorModal = ref(false)
    
    // 表单数据
    const emailForm = reactive({
      email: '',
      code: '',
      password: '',
      codeSent: false,
      countdown: 60
    })
    
    const phoneForm = reactive({
      phone: '',
      code: '',
      password: '',
      codeSent: false,
      countdown: 60
    })
    
    const passwordForm = reactive({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
    
    const twoFactorForm = reactive({
      code: '',
      password: ''
    })

    // 保存密码
    const savePassword = async () => {
      if (passwordForm.currentPassword.length < 6) {
        showError(t,'当前密码长度至少为6位')
        // 光标跳转到新密码输入框
        focusInput('currentPassword')
        return
      }
      if (passwordForm.newPassword !== passwordForm.confirmPassword) {
        showError(t,'两次输入的密码不一致')
        // 光标跳转到确认密码输入框
        focusInput('confirmPassword')
        return
      }
      if (passwordForm.newPassword.length < 6) {
        showError(t,'新密码长度至少为6位')
        // 光标跳转到新密码输入框
        focusInput('newPassword')
        return
      }
      
      try {
        // 调用store的changePassword action
        const result = await store.dispatch('user/changePassword', {
          oldPassword: passwordForm.currentPassword,
          newPassword: passwordForm.newPassword
        })
        
        if (result && result.code === 200) {
          userInfo.lastPasswordChange = new Date().toISOString()
          
          showPasswordModal.value = false
          
          // 调用登出action
          await store.dispatch('user/logout')
          router.push('/auth/login')
          showSuccess(t,'密码已更新')
        }else{
          showError(t,result.message || '修改密码失败')
        }
      } catch (error) {
        console.error('修改密码失败:', error)
        showError(t,error.message || '修改密码失败')
      }
    }
    
    
    
    
    onMounted(async () => {
      try {
        // 从store获取用户信息
        const result = await store.dispatch('user/fetchProfile')
        if (result && result.code === 200) {
          // 更新用户信息
          const userData = result.data
          userInfo.username = userData.username
          userInfo.email = userData.email
          userInfo.emailVerified = userData.emailVerified
          userInfo.phone = userData.phone
          userInfo.phoneVerified = userData.phoneVerified
          userInfo.avatar = userData.avatar
          userInfo.logintime = userData.logintime
          userInfo.lastPasswordChange = userData.lastPasswordChange || ''
          userInfo.twoFactorEnabled = userData.twoFactorEnabled || false
          userInfo.level = userData.level || 0
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        showError(t,'获取用户信息失败')
      }
    })
    
    watch(showEmailModal, (newVal) => {
      if (!newVal) {
        emailForm.email = ''
        emailForm.code = ''
        emailForm.password = ''
        emailForm.codeSent = false
        emailForm.countdown = 60
      }
    })

    watch(showPhoneModal, (newVal) => {
      if (!newVal) {
        phoneForm.phone = ''
        phoneForm.code = ''
        phoneForm.password = ''
        phoneForm.codeSent = false
        phoneForm.countdown = 60
      }
    })

    watch(showPasswordModal, (newVal) => {
      if (!newVal) {
        passwordForm.currentPassword = ''
        passwordForm.newPassword = ''
        passwordForm.confirmPassword = ''
      }
    })

    watch(showTwoFactorModal, (newVal) => {
      if (!newVal) {
        twoFactorForm.code = ''
        twoFactorForm.password = ''
      }
    })

    return {
      userInfo,
      showEmailModal,
      showPhoneModal,
      showPasswordModal,
      showTwoFactorModal,
      emailForm,
      phoneForm,
      passwordForm,
      twoFactorForm,
      savePassword,
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/user/common.css';
@import '@/styles/views/user/personalInfo.css';
</style>
