/* 导航栏样式 */
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  height: 70px;
  background-color: var(--bg-primary);
  box-shadow: var(--box-shadow);
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
  transition: background-color var(--transition-normal), box-shadow var(--transition-normal);
}

/* Logo */
.navbar-logo {
  display: flex;
  align-items: center;
}

.navbar-logo a {
  display: flex;
  align-items: center;
  color: var(--primary-color);
  text-decoration: none;
}

.navbar-logo-image {
  height: 100%; /* 适配导航栏高度 */
  max-height: 70px; /* 限制最大高度 */
}

.logo-icon {
  font-size: 1.8rem;
  margin-right: var(--spacing-xs);
  color: var(--primary-color);
}

/* 导航链接 */
.navbar-nav {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

.navbar-nav-center {
  margin: 0 auto;
}

.navbar-nav-right {
  margin-left: auto;
}

.nav-item {
  margin: 0 var(--spacing-xs);
  position: relative;
}

.nav-link {
  display: block;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: color var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color);
}

.nav-link.active {
  color: var(--primary-color);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 3px;
}

/* 图标按钮 */
.navbar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  transition: background-color var(--transition-fast), transform var(--transition-fast);
}

.navbar-icon:hover {
  background-color: var(--bg-secondary);
}

.navbar-icon i {
  font-size: 1.2rem;
  color: var(--text-primary);
  transition: color var(--transition-fast);
}

/* 主题切换按钮 */
.theme-toggle {
  overflow: hidden;
}

.theme-toggle i {
  color: var(--text-primary);
}

.theme-toggle:hover {
  transform: rotate(15deg);
}

.navbar-icon-badge {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  background-color: var(--error-color);
  color: var(--text-light);
  font-size: 0.75rem;
  font-weight: var(--font-weight-bold);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 5px;
}

/* 下拉菜单 */
.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 200px;
  background-color: var(--bg-card);
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow-dropdown);
  padding: var(--spacing-sm) 0;
  z-index: var(--z-index-dropdown);
  margin-top: var(--spacing-sm);
  overflow: hidden;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 移动端下拉菜单水平居中显示在导航栏下方 */
@media (max-width: 767px) {
  .dropdown-menu {
    position: fixed;
    top: 70px; /* 导航栏高度 */
    left: 50%;
    transform: translateX(-50%);
    right: auto;
    margin-top: 5px; /* 与导航栏的间距 */
    max-width: 90%;
    width: 300px;
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  text-decoration: none;
  transition: background-color var(--transition-fast);
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.dropdown-item:hover {
  background-color: var(--bg-secondary);
}

.dropdown-item.active {
  background-color: var(--bg-secondary);
  color: var(--primary-color);
}

.dropdown-item i {
  margin-right: var(--spacing-sm);
  width: 20px;
  text-align: center;
}

.dropdown-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: var(--spacing-xs) 0;
}

/* 语言下拉菜单 */
.language-flag {
  width: 20px;
  height: 15px;
  margin-right: var(--spacing-sm);
  object-fit: cover;
}

/* 消息下拉菜单 */
.message-dropdown {
  width: 350px;
  max-height: 450px;
  display: flex;
  flex-direction: column;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.message-title {
  font-weight: var(--font-weight-semibold);
}

.message-actions {
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.message-list {
  list-style: none;
  margin: 0;
  padding: 0;
  overflow-y: auto;
  max-height: 350px;
}

.message-item {
  display: flex;
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border-color-light);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.message-item:hover {
  background-color: var(--bg-secondary);
}

.message-item.unread {
  border-left: 3px solid var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.message-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-right: var(--spacing-sm);
  background-color: rgba(var(--primary-color-rgb), 0.1);
  border-radius: var(--border-radius-circle);
  color: var(--primary-color);
}

.message-item-content {
  flex: 1;
  overflow: hidden;
}

.message-item-header {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--spacing-xs);
}

.message-item-title {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-item-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-top: 2px;
}

.message-item-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.message-item-body {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-type,
.message-urgency {
  padding: 1px 4px;
  font-size: var(--font-size-xs);
  border-radius: var(--border-radius-sm);
}

.message-type.type0 {
  background-color: rgba(var(--info-color-rgb), 0.1);
  color: var(--info-color);
}

.message-type.type1 {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

.message-type.type2 {
  background-color: rgba(var(--error-color-rgb), 0.1);
  color: var(--error-color);
}

.message-type.type3 {
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
}

.message-urgency.urgency0 {
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
}

.message-urgency.urgency1 {
  background-color: rgba(var(--warning-color-rgb), 0.1);
  color: var(--warning-color);
}

.message-urgency.urgency2 {
  background-color: rgba(var(--error-color-rgb), 0.1);
  color: var(--error-color);
}

.message-footer {
  padding: var(--spacing-sm) var(--spacing-md);
  border-top: 1px solid var(--border-color);
  text-align: center;
}

.message-empty {
  padding: var(--spacing-md);
  text-align: center;
  color: var(--text-tertiary);
}

/* 二维码下拉菜单 */
.qrcode-dropdown {
  width: 240px;
  padding: var(--spacing-md);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.qrcode-title {
  text-align: center;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-sm);
}

.qrcode-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-md);
}

.qrcode-tab {
  flex: 1;
  text-align: center;
  padding: var(--spacing-xs) 0;
  cursor: pointer;
  transition: color var(--transition-fast);
  position: relative;
}

.qrcode-tab.active {
  color: var(--primary-color);
}

.qrcode-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary-color);
}

.qrcode-image {
  margin-bottom: var(--spacing-md);
  display: flex;
  justify-content: center;
}

.qrcode-placeholder {
  width: 150px;
  height: 150px;
  background-color: var(--bg-secondary);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-md);
}

.qrcode-placeholder i {
  font-size: 3rem;
  margin-bottom: var(--spacing-sm);
  color: var(--text-tertiary);
}

.qrcode-description {
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 用户下拉菜单 */
.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-md);
  transition: background-color var(--transition-fast);
}

.user-dropdown:hover {
  background-color: var(--bg-secondary);
}

.user-avatar-placeholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-sm);
}

.user-avatar-placeholder i {
  color: var(--text-light);
}

.user-info {
  margin-right: var(--spacing-sm);
}

.user-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.user-role {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

/* 移动端菜单 */
.navbar-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-primary);
  cursor: pointer;
}

.navbar-mobile {
  position: fixed;
  top: 0;
  right: -300px;
  width: 300px;
  height: 100vh;
  background-color: var(--bg-primary);
  box-shadow: var(--box-shadow-dropdown);
  z-index: var(--z-index-modal);
  transition: right var(--transition-normal);
  display: flex;
  flex-direction: column;
}

.navbar-mobile.active {
  right: 0;
}

.navbar-mobile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.navbar-mobile-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-primary);
  cursor: pointer;
}

.navbar-mobile-nav {
  list-style: none;
  margin: 0;
  padding: var(--spacing-md) 0;
  overflow-y: auto;
}

.navbar-mobile-nav .nav-item {
  margin: 0;
}

.navbar-mobile-nav .nav-link {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color-light);
  display: flex;
  align-items: center;
}

/* 移动端主题切换按钮 */
.theme-toggle-mobile .nav-link {
  color: var(--text-primary);
}

.theme-toggle-mobile .nav-link i {
  margin-right: var(--spacing-sm);
  font-size: 1.2rem;
  transition: transform var(--transition-normal);
}

.theme-toggle-mobile .nav-link i.theme-toggle-animation {
  animation: theme-toggle-rotate 0.5s ease;
}

.theme-toggle-mobile:active .nav-link {
  background-color: var(--bg-secondary);
}

.navbar-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-index-modal-backdrop);
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--transition-normal), visibility var(--transition-normal);
}

.navbar-backdrop.active {
  opacity: 1;
  visibility: visible;
}

/* 消息详情模态框 */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-index-modal-backdrop);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--transition-normal), visibility var(--transition-normal);
}

.modal-backdrop.show {
  opacity: 1;
  visibility: visible;
}

.modal {
  width: 90%;
  max-width: 500px;
  background-color: var(--bg-modal);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-modal);
  transform: translateY(20px);
  opacity: 0;
  transition: transform var(--transition-normal), opacity var(--transition-normal);
}

.modal.show {
  transform: translateY(0);
  opacity: 1;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--text-tertiary);
  cursor: pointer;
}

.modal-body {
  padding: var(--spacing-md);
}

.message-modal-header {
  display: flex;
  margin-bottom: var(--spacing-md);
}

.message-modal-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  color: var(--text-light);
}

.message-modal-info {
  flex: 1;
}

.message-modal-info h4 {
  margin: 0 0 var(--spacing-xs) 0;
}

.message-modal-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.message-modal-time {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

.message-modal-type,
.message-modal-urgency {
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
}

/* 使用与消息列表相同的样式 */
.message-type.type0,
.message-modal-type.type0 {
  background-color: rgba(var(--info-color-rgb), 0.1);
  color: var(--info-color);
}

.message-type.type1,
.message-modal-type.type1 {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

.message-type.type2,
.message-modal-type.type2 {
  background-color: rgba(var(--error-color-rgb), 0.1);
  color: var(--error-color);
}

.message-type.type3,
.message-modal-type.type3 {
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
}

.message-urgency.urgency0,
.message-modal-urgency.urgency0 {
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
}

.message-urgency.urgency1,
.message-modal-urgency.urgency1 {
  background-color: rgba(var(--warning-color-rgb), 0.1);
  color: var(--warning-color);
}

.message-urgency.urgency2,
.message-modal-urgency.urgency2 {
  background-color: rgba(var(--error-color-rgb), 0.1);
  color: var(--error-color);
}

.message-modal-content {
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacing-md);
}

.message-modal-action {
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 991px) {
  .d-none-md {
    display: none;
  }
  
  .navbar-toggle {
    display: block;
  }
}

@media (min-width: 992px) {
  .d-md-none {
    display: none;
  }
}
