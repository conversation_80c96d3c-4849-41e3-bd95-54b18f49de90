.introduction-page {
  padding: var(--spacing-lg);
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.page-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 800px;
  margin: 0 auto;
}

.introduction-content {
  margin-top: var(--spacing-xl);
}

.intro-section {
  margin-bottom: var(--spacing-xxl);
}

.section-title {
  font-size: 2rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  position: relative;
  padding-bottom: var(--spacing-sm);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  border-radius: 2px;
}

.section-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.intro-image {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--bg-card) 0%, var(--bg-secondary) 100%);
  padding: var(--spacing-md);
}

.advantage-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  height: 100%;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  box-shadow: var(--box-shadow);
  text-align: center;
  margin: var(--spacing-md);
}

.advantage-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--box-shadow-hover);
}

.advantage-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-md);
  color: var(--text-light);
  font-size: 1.75rem;
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}

.advantage-title {
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.advantage-text {
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
}

.steps-container {
  margin-top: var(--spacing-lg);
}

.step {
  display: flex;
  margin-bottom: var(--spacing-lg);
  position: relative;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 50px;
  left: 25px;
  width: 2px;
  height: calc(100% - 30px);
  background-color: var(--border-color);
}

.step-number {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-light) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  font-weight: var(--font-weight-bold);
  font-size: 1.25rem;
  margin-right: var(--spacing-md);
  flex-shrink: 0;
  position: relative;
  z-index: 1;
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.step-text {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.5;
}

.cta-section {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  margin-top: var(--spacing-xxl);
}

.cta-title {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.cta-text {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 800px;
  margin: 0 auto var(--spacing-lg);
}

.cta-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

@media (max-width: 767px) {
  .page-title {
    font-size: 2rem;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .intro-image {
    margin-top: var(--spacing-lg);
  }
  
  .advantage-card {
    margin: var(--spacing-lg);
  }
  
  .cta-title {
    font-size: 1.5rem;
  }
}
