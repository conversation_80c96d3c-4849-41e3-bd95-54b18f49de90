/* 页脚样式 */
.footer {
  background-color: var(--secondary-color);
  color: var(--text-light);
  padding: var(--spacing-xl) 0 var(--spacing-md);
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 10% 10%, rgba(240, 185, 11, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 90% 90%, rgba(240, 185, 11, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.footer-content {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  position: relative;
}

.footer-logo {
  display: inline-flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.footer-logo-text {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  margin-left: var(--spacing-xs);
}

.footer-description {
  max-width: 600px;
  margin: 0 auto var(--spacing-lg);
  opacity: 0.8;
}

.footer-social {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
  transition: background-color var(--transition-fast), transform var(--transition-fast);
}

.social-icon:hover {
  background-color: var(--primary-color);
  transform: translateY(-3px);
}

.footer-links {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
  position: relative;
}

.footer-links-column {
  flex: 1;
  min-width: 200px;
  margin-bottom: var(--spacing-lg);
  padding: 0 var(--spacing-md);
}

.footer-links-title {
  font-size: 1.1rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-md);
  position: relative;
  padding-bottom: var(--spacing-xs);
  color: #ffffff;
}

.footer-links-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 30px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer-links-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links-list li {
  margin-bottom: var(--spacing-sm);
}

.footer-links-list a {
  color: #b7bdc6;
  text-decoration: none;
  transition: color var(--transition-fast);
  display: inline-block;
}

.footer-links-list a:hover {
  color: var(--primary-color);
}

/* 联系图片样式 */
.footer-contact-image {
  margin-top: var(--spacing-sm);
}

.footer-contact-image img {
  max-width: 100%;
  width: 120px;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform var(--transition-fast);
}


.footer-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.footer-copyright {
  font-size: var(--font-size-sm);
  opacity: 0.7;
  text-align: center;
}
.footer-copyright a{
  color: #b7bdc6;
}
/* 响应式调整 */
@media (max-width: 767px) {
  .footer-links {
    flex-direction: column;
  }
  
  .footer-links-column {
    margin-bottom: var(--spacing-lg);
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: var(--spacing-md);
  }
}
