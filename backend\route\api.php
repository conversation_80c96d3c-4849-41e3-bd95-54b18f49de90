<?php
// API路由定义

use think\facade\Route;

// API版本1
Route::group('api/v1', function () {
    // 认证相关接口
    Route::group('auth', function () {
        // 登录
        Route::post('login', 'api/v1.Auth/login');
        // 注册
        Route::post('register', 'api/v1.Auth/register');
        // 忘记密码
        Route::post('forgot-password', 'api/v1.Auth/forgotPassword');
        // 重置密码
        Route::post('reset-password', 'api/v1.Auth/resetPassword');
        // 退出登录
        Route::post('logout', 'api/v1.Auth/logout');
        // 获取验证码图片
        Route::get('captcha', 'api/v1.Auth/getCaptcha');
        // 验证验证码
        Route::post('verify-captcha', 'api/v1.Auth/checkCaptcha');
    });

    // 用户相关接口
    Route::group('user', function () {
        // 获取用户资料
        Route::get('profile', 'api/v1.User/profile');
        // 更新用户资料
        Route::post('profile', 'api/v1.User/updateProfile');
        // 修改密码
        Route::post('change-password', 'api/v1.User/changePassword');
        // 绑定邮箱
        Route::post('bind-email', 'api/v1.User/bindEmail');
        // 发送邮箱验证码
        Route::post('send-email-code', 'api/v1.User/sendEmailCode');
        // 绑定手机
        Route::post('bind-phone', 'api/v1.User/bindPhone');
        // 发送手机验证码
        Route::post('send-phone-code', 'api/v1.User/sendPhoneCode');
    });

    // 账户相关接口
    Route::group('accounts', function () {
        // 获取账户列表
        Route::get('', 'api/v1.Account/index');
        // 获取账户详情
        Route::get('<id>', 'api/v1.Account/read')->pattern(['id' => '\d+']);
        // 添加账户
        Route::post('', 'api/v1.Account/create');
        // 更新账户
        Route::put('<id>', 'api/v1.Account/update')->pattern(['id' => '\d+']);
        // 归集资金
        Route::post('collect-funds', 'api/v1.Account/collectFunds');
        // 获取仪表盘数据
        Route::get('dashboard', 'api/v1.Account/dashboard');
        // 获取每日收益记录
        Route::get('daily-profit-records', 'api/v1.Account/dailyProfitRecords');
        // 获取账户统计数据
        Route::get('account-statistics', 'api/v1.Account/accountStatistics');
    });

    // 子账号相关接口
    Route::group('sub-accounts', function () {
        // 获取子账号列表
        Route::get('', 'api/v1.SubAccount/index');
        // 获取子账号详情
        Route::get('<id>', 'api/v1.SubAccount/read')->pattern(['id' => '\d+']);
        // 更新子账号API
        Route::put('<id>/api', 'api/v1.SubAccount/updateApi')->pattern(['id' => '\d+']);
        // 获取资金归集记录
        Route::get('collection-records', 'api/v1.SubAccount/collectionRecords');
    });

    // 获取收益详情
    Route::get('profit-details', 'api/v1.SubAccount/profitDetails');

    // 代理相关接口
    Route::group('agent', function () {
        // 获取代理概览
        Route::get('overview', 'api/v1.Agent/overview');
        // 获取代理统计
        Route::get('profit', 'api/v1.Agent/profit');
        // 获取代理等级列表
        Route::get('level-list', 'api/v1.Agent/levelList');
        // 提现申请接口
        Route::post('withdraw-apply', 'api/v1.Agent/withdrawApply');
        // 提现记录列表接口
        Route::get('withdrawal-records', 'api/v1.Agent/withdrawalRecords');
        // 确认提现接口
        Route::post('withdraw-confirm', 'api/v1.Agent/withdrawConfirm');
        // 取消提现接口
        Route::post('withdraw-cancel', 'api/v1.Agent/cancelWithdrawal'); // 修正控制器方法名为 cancelWithdrawal
        // 代理佣金记录列表接口
        Route::get('commission-records', 'api/v1.Agent/commissionRecords');
    });

    // 消息相关接口
    Route::group('messages', function () {
        // 获取消息列表
        Route::get('', 'api/v1.Message/index');
        // 标记消息为已读
        Route::put('<id>/read', 'api/v1.Message/markAsRead')->pattern(['id' => '\d+']);
        // 标记所有消息为已读
        Route::put('read-all', 'api/v1.Message/markAllAsRead');
        // 获取消息详情
        Route::get('<id>', 'api/v1.Message/read')->pattern(['id' => '\d+']);
    });

    // 资金费率历史相关接口
    Route::group('funding-rate', function () {
        // 获取合约列表
        Route::get('symbols', 'api/v1.FundingRateHistory/symbolList');
        // 获取历史资金费率
        Route::get('history', 'api/v1.FundingRateHistory/historyList');
        // 爬取资金费率历史数据
        Route::get('fetch', 'api/v1.FundingRateHistory/fetchFundingRateHistory');
    });
});
