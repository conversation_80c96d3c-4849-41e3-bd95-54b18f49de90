.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1051;
}

.modal-dialog {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 1.75rem auto;
  z-index: 1052;
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: var(--bg-modal);
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow-modal);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.btn-close {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  margin: 0;
  line-height: 1;
  color: var(--text-primary);
}

.btn-close:hover {
  color: var(--primary-color);
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: var(--spacing-md);
  color: var(--text-primary);
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  gap: 0.5rem;
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}
/* 暗黑模式样式 */
.dark-mode .modal-content {
  background-color: var(--bg-modal);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.dark-mode .modal-header,
.dark-mode .modal-footer {
  border-color: var(--border-color);
}

.dark-mode .modal-title {
  color: var(--text-primary);
}

.dark-mode .btn-close {
  color: var(--text-primary);
}

.dark-mode .btn-close:hover {
  color: var(--primary-color);
}

.dark-mode .modal-body {
  color: var(--text-primary);
}

.dark-mode .modal-backdrop {
  background-color: rgba(0, 0, 0, 0.7);
}

/* 响应式调整 */
@media (max-width: 576px) {
  .modal-dialog {
    margin: 0.5rem;
  }
}
