<?php
declare(strict_types=1);

namespace app\controller\api\v1;

use app\controller\api\BaseController;
use think\Response;
use think\facade\Db;
use think\facade\Cache;
use app\validate\User as UserValidate;

/**
 * 用户控制器
 */
class User extends BaseController
{
    /**
     * 获取用户资料
     */
    public function profile(): Response
    {

        // 获取当前登录用户ID
        $userId = request()->user['id'];
        
        // 查询用户信息
        $user = Db::name('users')
            ->where('id', $userId)
            ->field('id, username, email, phone, level, balance, logintime')
            ->find();
        
        if (empty($user)) {
            return $this->error('用户不存在');
        }
        
        // 处理用户数据
        $user['emailVerified'] = !empty($user['email']);
        $user['phoneVerified'] = !empty($user['phone']);
        
        return $this->success($user, '获取用户资料成功');
    }
    
    /**
     * 更新用户资料
     */
    public function updateProfile(): Response
    {
        $validate = new UserValidate();
        if (!$validate->scene('updateProfile')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        // 获取当前登录用户ID
        $userId = request()->user['id'];
        
        // 获取请求参数
        $username = input('username', '');
        $avatar = input('avatar', '');
        
        // 更新用户信息
        $updated_data = [
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if (!empty($username)) {
            $updated_data['username'] = $username;
        }
        
        if (!empty($avatar)) {
            $updated_data['avatar'] = $avatar;
        }
        
        $result = Db::name('users')
            ->where('id', $userId)
            ->update($updated_data);
        
        if ($result === false) {
            return $this->error('用户资料更新失败');
        }
        
        // 查询更新后的用户信息
        $user = Db::name('users')
            ->where('id', $userId)
            ->field('id, username, email, phone, created_at as registrationTime, avatar')
            ->find();
        
        // 处理用户数据
        $user['emailVerified'] = !empty($user['email']);
        $user['phoneVerified'] = !empty($user['phone']);
        
        // 如果没有头像，设置默认头像
        if (empty($user['avatar'])) {
            $user['avatar'] = '/static/images/default-avatar.png';
        }
        
        return $this->success([
            'user' => $user
        ], '用户资料更新成功');
    }
    
    /**
     * 修改密码
     */
    public function changePassword(): Response
    {
        $validate = new UserValidate();
        if (!$validate->scene('changePassword')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        // 获取当前登录用户ID
        $userId = request()->user['id'];
        
        // 获取请求参数
        $oldPassword = input('oldPassword', '');
        $newPassword = input('newPassword', '');
        
        // 验证旧密码
        $user = Db::name('users')
            ->where('id', $userId)
            ->field('password')
            ->find();
        
        if (empty($user)) {
            return $this->error('用户不存在');
        }
        
        if (!password_verify($oldPassword, $user['password'])) {
            return $this->error('旧密码不正确');
        }
        
        // 更新密码
        $result = Db::name('users')
            ->where('id', $userId)
            ->update([
                'password' => password_hash($newPassword, PASSWORD_DEFAULT),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        
        if ($result === false) {
            return $this->error('密码修改失败');
        }
        
        return $this->success([], '密码修改成功');
    }
    
    /**
     * 绑定邮箱
     */
    public function bindEmail(): Response
    {
        $validate = new UserValidate();
        if (!$validate->scene('bindEmail')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        // 获取当前登录用户ID
        $userId = request()->user['id'];
        
        // 获取请求参数
        $email = input('email', '');
        $code = input('code', '');
        
        // 验证验证码
        $cacheKey = 'email_code_' . $email;
        $cacheCode = Cache::get($cacheKey);
        
        if (empty($cacheCode) || $cacheCode !== $code) {
            return $this->error('验证码不正确或已过期');
        }
        
        // 检查邮箱是否已被其他用户绑定
        $exists = Db::name('users')
            ->where('email', $email)
            ->where('id', '<>', $userId)
            ->find();
        
        if ($exists) {
            return $this->error('该邮箱已被其他用户绑定');
        }
        
        // 更新用户邮箱
        $result = Db::name('users')
            ->where('id', $userId)
            ->update([
                'email' => $email,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        
        if ($result === false) {
            return $this->error('邮箱绑定失败');
        }
        
        // 清除验证码缓存
        Cache::delete($cacheKey);
        
        return $this->success([], '邮箱绑定成功');
    }
    
    /**
     * 发送邮箱验证码
     */
    public function sendEmailCode(): Response
    {
        $validate = new UserValidate();
        if (!$validate->scene('sendEmailCode')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        // 获取请求参数
        $email = input('email', '');
        
        // 生成验证码
        $code = mt_rand(100000, 999999);
        
        // 缓存验证码，有效期10分钟
        $cacheKey = 'email_code_' . $email;
        Cache::set($cacheKey, $code, 600);
        
        // 发送邮件
        // 这里需要实现邮件发送功能，可以使用第三方邮件服务或PHP的mail函数
        // 为了演示，这里只记录日志
        \think\facade\Log::info('发送邮箱验证码：' . $email . ' - ' . $code);
        
        return $this->success([], '验证码已发送');
    }
    
    /**
     * 绑定手机
     */
    public function bindPhone(): Response
    {
        $validate = new UserValidate();
        if (!$validate->scene('bindPhone')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        // 获取当前登录用户ID
        $userId = request()->user['id'];
        
        // 获取请求参数
        $phone = input('phone', '');
        $code = input('code', '');
        
        // 验证验证码
        $cacheKey = 'phone_code_' . $phone;
        $cacheCode = Cache::get($cacheKey);
        
        if (empty($cacheCode) || $cacheCode !== $code) {
            return $this->error('验证码不正确或已过期');
        }
        
        // 检查手机号是否已被其他用户绑定
        $exists = Db::name('users')
            ->where('phone', $phone)
            ->where('id', '<>', $userId)
            ->find();
        
        if ($exists) {
            return $this->error('该手机号已被其他用户绑定');
        }
        
        // 更新用户手机号
        $result = Db::name('users')
            ->where('id', $userId)
            ->update([
                'phone' => $phone,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        
        if ($result === false) {
            return $this->error('手机绑定失败');
        }
        
        // 清除验证码缓存
        Cache::delete($cacheKey);
        
        return $this->success([], '手机绑定成功');
    }
    
    /**
     * 发送手机验证码
     */
    public function sendPhoneCode(): Response
    {
        $validate = new UserValidate();
        if (!$validate->scene('sendPhoneCode')->check(request()->param())) {
            return $this->error($validate->getError());
        }

        // 获取请求参数
        $phone = input('phone', '');
        
        // 生成验证码
        $code = mt_rand(100000, 999999);
        
        // 缓存验证码，有效期10分钟
        $cacheKey = 'phone_code_' . $phone;
        Cache::set($cacheKey, $code, 600);
        
        // 发送短信
        // 这里需要实现短信发送功能，可以使用第三方短信服务
        // 为了演示，这里只记录日志
        \think\facade\Log::info('发送手机验证码：' . $phone . ' - ' . $code);
        
        return $this->success([], '验证码已发送');
    }
}
