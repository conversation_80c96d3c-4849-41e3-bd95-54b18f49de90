{"AccountManagement": {"account": "母账号", "accountAdded": "账号已添加", "accountManagement": "母账号管理", "accountRemark": "母账号备注", "accountRemarkHint": "用于区分不同的交易账号", "accountUpdated": "账号信息已更新", "addAccount": "添加母账号", "addTime": "添加时间", "agreeTerms": "我已阅读并同意", "agreeTermsError": "请阅读并同意API密钥使用条款", "apiTerms": "API密钥使用条款", "cancel": "取消", "completedCount": "完成数量", "edit": "编辑", "editAccount": "编辑母账号", "noAccountData": "暂无母账号数据", "operation": "操作", "profitDetails": "盈利明细", "runningCount": "运行数量", "save": "保存", "saveAccountError": "保存账号失败", "secretkey": "API密钥", "status": "状态", "statusDisabled": "已停用", "statusEnabled": "已启用", "statusExpired": "已失效", "statusInactive": "未启用", "subAccountCount": "子账号数量", "subtitle": "管理您的交易母账号", "taskStatusCompleted": "已完成", "taskStatusNotRunning": "未运行", "taskStatusRunning": "执行中", "task_status": "任务状态", "totalAssets": "总资产", "totalProfit": "总收益", "todayProfit": "今日收益", "uncollectedFunds": "未归集资金", "statistics": "母账号统计", "accountCount": "母账号数量", "totalAccountCount": "母账号总数", "accountRequired": "请输入母账号", "apikeyRequired": "请输入API KEY", "secretkeyRequired": "请输入API密钥"}}