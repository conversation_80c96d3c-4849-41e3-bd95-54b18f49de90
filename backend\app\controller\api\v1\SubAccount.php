<?php
declare(strict_types=1);

namespace app\controller\api\v1;

use app\controller\api\BaseController;
use think\Response;
use think\facade\Db;
use app\validate\SubAccount as SubAccountValidate;
use app\utils\ApiKeyChecker;

/**
 * 子账号控制器
 */
class SubAccount extends BaseController
{
    /**
     * 获取子账号列表
     */
    public function index(): Response
    {
        // 验证请求参数
        $validate = new SubAccountValidate();
        if (!$validate->scene('index')->check(request()->param())) {
            return $this->error($validate->getError());
        }
        
        // 获取当前登录用户ID
        $userId = request()->user['id'];
        
        // 获取分页参数
        $page = input('page/d', 1);
        $limit = input('limit/d', 10);
        $status = input('status', '');
        $accountId = input('accountId/d', 0);
        
        // 构建查询条件
        $where = [
            'user_id' => $userId
        ];
        
        if ($status !== '') {
            $where['status'] = $status;
        }
        
        if ($accountId > 0) {
            $where['account_id'] = $accountId;
        }
        
        // 查询子账号列表
        $subAccounts = Db::name('account_sub')
            ->field('id, account_id, email, wallet_balance, sum_income, collect_wallet, task_status, status, apikey')
            ->where($where)
            ->page($page, $limit)
            ->order('id', 'desc')
            ->select()
            ->toArray();
        
        // 获取总数
        $total = Db::name('account_sub')
            ->where($where)
            ->count();
        
        // 处理子账号数据
        foreach ($subAccounts as &$subAccount) {
            $subAccount['accountId'] = $subAccount['account_id'];
            $subAccount['hasApiKey'] = !empty($subAccount['apikey']);
        }
        
        // 获取子账号统计数据
        // 子账号数量
        $subAccountCount = $total;
        
        // 总资产
        $totalAssets = Db::name('account_sub')
            ->where($where)
            ->sum('wallet_balance');
            
        // 总收益
        $totalProfit = Db::name('account_sub')
            ->where($where)
            ->sum('sum_income');
            
        // 未归集资金
        $uncollectedFunds = Db::name('account_sub')
            ->where($where)
            ->sum('collect_wallet');
        
        return $this->success([
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'subAccounts' => $subAccounts,
            'subAccountCount' => $subAccountCount,
            'totalAssets' => $totalAssets,
            'totalProfit' => $totalProfit,
            'uncollectedFunds' => $uncollectedFunds
        ], '获取子账号列表成功');
    }
    
    /**
     * 获取子账号详情
     */
    public function read($id): Response
    {
        // 获取当前登录用户ID
        $userId = request()->user['id'];
        
        // 查询子账号信息
        $subAccount = Db::name('account_sub')
            ->where('id', $id)
            ->where('user_id', $userId)
            ->find();
        
        if (empty($subAccount)) {
            return $this->error('子账号不存在');
        }
        
        // 获取账户名称
        $accountName = Db::name('accounts')
            ->where('id', $subAccount['account_id'])
            ->value('account');
        
        // 处理子账号数据
        $subAccount['name'] = $subAccount['email'];
        $subAccount['balance'] = $subAccount['wallet_balance'];
        $subAccount['assets'] = $subAccount['wallet_balance'];
        $subAccount['totalProfit'] = $this->calculateTotalProfit($subAccount['id']);
        $subAccount['uncollectedFunds'] = $subAccount['collect_wallet'];
        $subAccount['status'] = $this->getStatusText($subAccount['status']);
        $subAccount['accountId'] = $subAccount['account_id'];
        $subAccount['parentAccountId'] = $subAccount['account_id'];
        $subAccount['created_at'] = $subAccount['created_at'];
        $subAccount['updated_at'] = $subAccount['updated_at'];
        $subAccount['apikey'] = $subAccount['apikey'];
        $subAccount['secretkey'] = $subAccount['secretkey'];
        $subAccount['hasApiKey'] = !empty($subAccount['apikey']);
        $subAccount['completedAt'] = $subAccount['task_status'] == 3 ? $subAccount['updated_at'] : null;
        
        return $this->success($subAccount, '获取子账号详情成功');
    }
    
    /**
     * 更新子账号API
     */
    public function updateApi($id): Response
    {
        // 验证请求参数
        $validate = new SubAccountValidate();
        if (!$validate->scene('updateApi')->check(request()->param())) {
            return $this->error($validate->getError());
        }
        
        // 获取当前登录用户ID
        $userId = request()->user['id'];
        
        // 查询子账号是否存在
        $subAccount = Db::name('account_sub')
            ->where('id', $id)
            ->where('user_id', $userId)
            ->find();
        
        if (empty($subAccount)) {
            return $this->error('子账号不存在');
        }
        
        // 获取请求参数
        $apikey = input('apikey', '');
        $secretkey = input('secretkey', '');
        
        // 检查 apikey 和 secretkey 是否已存在
        if (!empty($apikey)) {
            $existingApiKey = Db::name('account_sub')
                ->where('apikey', $apikey)
                ->where('id', '<>', $id)
                ->find();
            
            if ($existingApiKey) {
                return $this->error('API Key 已存在，请使用其他 API Key');
            }
        }

        if (!empty($secretkey)) {
            $existingSecretKey = Db::name('account_sub')
                ->where('secretkey', $secretkey)
                ->where('id', '<>', $id)
                ->find();
            
            if ($existingSecretKey) {
                return $this->error('Secret Key 已存在，请使用其他 Secret Key');
            }
        }
        
        // 更新子账号API信息
        $updated_data = [
            'updated_at' => date('Y-m-d H:i:s'),
            'status' => 1 // 更新API信息后需要重新验证
        ];
        

        if (!empty($apikey) && !empty($secretkey)) {
            // 验证API Key
            $checkResult = ApiKeyChecker::check($apikey, $secretkey, $id);
            if (!$checkResult['success']) {
                return $this->error('API Key验证失败: '.$checkResult['message'] ?? 'API Key验证失败');
            }
            
            $updated_data['apikey'] = $apikey;
            $updated_data['secretkey'] = $secretkey;
            $updated_data['status'] = 1;
        }
        
        $result = Db::name('account_sub')
            ->where('id', $id)
            ->update($updated_data);
        
        if ($result === false) {
            return $this->error('子账号API更新失败');
        }
        
        // 获取更新后的子账号信息
        $updatedSubAccount = Db::name('account_sub')
            ->where('id', $id)
            ->find();
        
        
        $subAccountData = [
            'id' => $updatedSubAccount['id'],
            'name' => $updatedSubAccount['email'],
            'status' => 1,
        ];
        
        return $this->success([
            'subAccount' => $subAccountData
        ], '子账号API更新成功');
    }
    
    /**
     * 获取资金归集记录
     */
    public function collectionRecords(): Response
    {
        // 验证请求参数
        $validate = new SubAccountValidate();
        if (!$validate->scene('collectionRecords')->check(request()->param())) {
            return $this->error($validate->getError());
        }
        
        // 获取当前登录用户ID
        $userId = request()->user['id'];
        
        // 获取分页参数
        $page = input('page/d', 1);
        $limit = input('limit/d', 10);
        $startDate = input('startDate', '');
        $endDate = input('endDate', '');
        
        // 构建查询条件
        $where = [
            'user_id' => $userId
        ];
        
        // 添加日期筛选条件
        if (!empty($startDate) && !empty($endDate)) {
            $where[] = ['created_at', 'between', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']];
        } else if (!empty($startDate)) {
            $where[] = ['created_at', '>=', $startDate . ' 00:00:00'];
        } else if (!empty($endDate)) {
            $where[] = ['created_at', '<=', $endDate . ' 23:59:59'];
        }
        
        // 查询资金归集记录
        $records = Db::name('settlement_record')
            ->where($where)
            ->page($page, $limit)
            ->order('created_at', 'desc')
            ->select()
            ->toArray();
        
        // 获取总数
        $total = Db::name('settlement_record')
            ->where($where)
            ->count();
        
        // 处理记录数据
        foreach ($records as &$record) {
            // 获取账户信息
            $account = Db::name('accounts')
                ->where('id', $record['account_id'])
                ->field('id, account')
                ->find();
            
            $record['amount'] = $record['profit'];
            $record['timestamp'] = $record['created_at'];
            $record['status'] = $this->getSettlementStatusText($record['status']);
            $record['fromAccount'] = $account ? $account['account'] : '系统归集';
            $record['toAccount'] = '主账户';
        }
        
        return $this->success([
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'records' => $records
        ], '获取资金归集记录成功');
    }
    
    /**
     * 获取收益详情
     */
    public function profitDetails(): Response
    {
        // 验证请求参数
        $validate = new SubAccountValidate();
        if (!$validate->scene('profitDetails')->check(request()->param())) {
            return $this->error($validate->getError());
        }
        
        // 获取当前登录用户ID
        $userId = request()->user['id'];
        
        // 获取分页参数
        $page = input('page/d', 1);
        $limit = input('limit/d', 10);
        $startDate = input('startDate', '');
        $endDate = input('endDate', '');
        $accountId = input('accountId/d', 0);
        $subAccountId = input('subAccountId/d', 0);
        
        // 构建查询条件
        $where = [
            'user_id' => $userId
        ];
        
        if ($accountId > 0) {
            $where['account_id'] = $accountId;
        }
        
        if ($subAccountId > 0) {
            $where['account_sub_id'] = $subAccountId;
        }
        
        // 添加日期筛选条件
        if (!empty($startDate) && !empty($endDate)) {
            $where[] = ['time', 'between', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']];
        } else if (!empty($startDate)) {
            $where[] = ['time', '>=', $startDate . ' 00:00:00'];
        } else if (!empty($endDate)) {
            $where[] = ['time', '<=', $endDate . ' 23:59:59'];
        }
        
        // 查询收益详情
        $details = Db::name('funding_rate_logs')
            ->where($where)
            ->field('time, account_id, account_sub_id, coin, amount, usdt')
            ->page($page, $limit)
            ->order('time', 'desc')
            ->select()
            ->toArray();
        
        // 获取总数
        $total = Db::name('funding_rate_logs')
            ->where($where)
            ->count();

        // 总盈利金额
        $totalProfit = Db::name('funding_rate_logs')
            ->where($where)
            ->sum('usdt');

        // 平均盈利金额
        $averageProfit = $total > 0 ? $totalProfit / $total : 0;

        // 最高盈利金额
        $maxProfit = Db::name('funding_rate_logs')
            ->where($where)
            ->max('usdt');
        
        
        return $this->success([
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'details' => $details,
            'totalProfit' => $totalProfit,
            'averageProfit' => round($averageProfit, 2),
            'maxProfit' => $maxProfit
        ], '获取收益详情成功');
    }
    
    /**
     * 计算子账号总收益
     * @param int $subAccountId
     * @return float
     */
    private function calculateTotalProfit(int $subAccountId): float
    {
        $totalProfit = Db::name('funding_rate_logs')
            ->where('account_sub_id', $subAccountId)
            ->sum('amount');
        
        return floatval($totalProfit);
    }
    
    /**
     * 获取状态文本
     * @param int $status
     * @return string
     */
    private function getStatusText(int $status): string
    {
        $statusMap = [
            0 => 'inactive',
            1 => 'running',
            2 => 'failed'
        ];
        
        return $statusMap[$status] ?? 'unknown';
    }
    
    /**
     * 获取交收状态文本
     * @param int $status
     * @return string
     */
    private function getSettlementStatusText(int $status): string
    {
        $statusMap = [
            0 => 'pending',
            1 => 'success',
            2 => 'processing',
            3 => 'failed'
        ];
        
        return $statusMap[$status] ?? 'unknown';
    }
}
