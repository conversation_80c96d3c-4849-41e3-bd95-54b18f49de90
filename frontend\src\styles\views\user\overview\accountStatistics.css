.account-statistics-content {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: var(--card-shadow);
}

/* 统计卡片样式 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stats-card {
  background: linear-gradient(135deg, var(--card-bg) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(var(--border-color-rgb), 0.1);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all var(--transition-normal);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 5px;
  font-family: 'Roboto Mono', monospace;
}

.stats-label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 收益状态样式 */
.profit-positive {
  color: var(--success-color) !important;
}

.profit-negative {
  color: var(--error-color) !important;
}



/* 树状表格样式 */
.tree-table .account-cell {
  display: flex;
  align-items: center;
  position: relative;
}

/* 母账号样式 - 不缩进 */
.account-cell {
  padding-left: 0;
}

/* 子账号样式 - 缩进到母账号文字后面 */
.account-cell.sub-account {
  padding-left: 28px; /* 展开图标宽度(20px) + 间距(8px) */
}

.expand-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: all var(--transition-normal);
  color: var(--text-secondary);
  flex-shrink: 0;
}

.expand-icon:hover {
  background-color: var(--bg-secondary);
  color: var(--primary-color);
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.expand-icon i {
  font-size: 12px;
}

/* 账户名称样式 */
.account-name {
  font-weight: 500;
  color: var(--text-primary);
  flex: 1;
}

/* 子账号名称样式 */
.sub-account-name {
  color: var(--text-secondary);
  font-weight: 400;
  font-size: 0.95em;
}

/* 子账号连接线效果 */
.account-cell.sub-account::before {
  content: '';
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 50%;
  width: 1px;
  background-color: rgba(var(--border-color-rgb), 0.3);
}

.account-cell.sub-account::after {
  content: '';
  position: absolute;
  left: 10px;
  top: 50%;
  width: 15px;
  height: 1px;
  background-color: rgba(var(--border-color-rgb), 0.3);
}



/* 任务状态徽章样式 */
.task-status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

.task-status-0 {
  background-color: rgba(var(--text-tertiary-rgb), 0.1);
  color: var(--text-tertiary);
}

.task-status-1 {
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
}

.task-status-2 {
  background-color: rgba(var(--error-color-rgb), 0.1);
  color: var(--error-color);
}

/* 其他状态（进行中） */
.task-status-badge:not(.task-status-0):not(.task-status-1):not(.task-status-2) {
  background-color: rgba(var(--warning-color-rgb), 0.1);
  color: var(--warning-color);
}

/* 表格样式增强 */
.account-statistics-content .table-container {
  margin-top: 20px;
}

.account-statistics-content .table th {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  color: var(--text-primary);
  font-weight: 600;
  border-bottom: 2px solid var(--primary-color);
  text-align: left; /* 表头左对齐 */
}

.account-statistics-content .table td {
  vertical-align: middle;
  padding: 12px 15px;
  text-align: left; /* 表格内容左对齐 */
}

/* 卡片视图样式 */
.account-statistics-content .cards-container .card {
  border: 1px solid rgba(var(--border-color-rgb), 0.1);
  border-radius: 8px;
  transition: all var(--transition-normal);
}

.account-statistics-content .cards-container .card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .account-statistics-content {
    padding: 15px;
  }

  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
  }

  .stats-card {
    padding: 15px;
  }

  .stats-value {
    font-size: 20px;
  }

  .stats-icon {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  /* 移动端子账号缩进调整 */
  .account-cell.sub-account {
    padding-left: 28px; /* 保持与桌面端一致 */
  }

  /* 移动端连接线调整 */
  .account-cell.sub-account::before {
    left: 10px;
  }

  .account-cell.sub-account::after {
    left: 10px;
    width: 15px;
  }
}

@media (max-width: 480px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }

  .stats-card {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .stats-value {
    font-size: 18px;
  }
}



/* 分页组件样式调整 */
.account-statistics-content .pagination-container {
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid rgba(var(--border-color-rgb), 0.1);
}
