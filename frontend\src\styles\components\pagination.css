.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
  font-size: 14px;
}

.pagination-info {
  color: var(--color-text-secondary);
}

.pagination-controls {
  display: flex;
  align-items: center;
}

.pagination-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  margin: 0 4px;
  padding: 0 8px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background-color: var(--color-bg-white);
  color: var(--color-text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.pagination-button:hover {
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.pagination-button:disabled {
  color: var(--color-text-disabled);
  border-color: var(--color-border);
  cursor: not-allowed;
  background-color: var(--color-bg-disabled);
}

.pagination-button.active {
  color: var(--text-light);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(var(--primary-color-rgb), 0.3);
}

.pagination-ellipsis {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  margin: 0 4px;
  color: var(--color-text-secondary);
}

.pagination-goto {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

.pagination-goto-input {
  width: 50px;
  height: 32px;
  margin: 0 8px;
  padding: 0 8px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  text-align: center;
}

.pagination-goto-input:focus {
  border-color: var(--color-primary);
  outline: none;
}

.pagination-per-page {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

.pagination-per-page-select {
  /* height: 32px; */
  margin: 0 8px;
  padding: 0.5rem 2.25rem 0.5rem 0.75rem;
  font-size: 14px;
  font-weight: var(--font-weight-regular);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23666' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  appearance: none;
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

/* 为了确保下拉图标颜色与文本颜色一致，我们为不同主题单独定义 */
.dark-mode .pagination-per-page-select {
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ccc' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}

/* 添加下拉选项的样式 */
.dark-mode .pagination-per-page-select option {
  color: var(--text-light);
  background-color: var(--bg-secondary);
}

.pagination-per-page-select:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .pagination-info {
    margin-bottom: 10px;
  }
  
  .pagination-controls {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .pagination-goto,
  .pagination-per-page {
    margin-left: 0;
    margin-top: 10px;
  }
}
