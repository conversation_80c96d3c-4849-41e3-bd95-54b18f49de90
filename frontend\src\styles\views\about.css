.about-page {
  padding: var(--spacing-lg);
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.page-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 800px;
  margin: 0 auto;
}

.about-content {
  margin-top: var(--spacing-xl);
}

.about-section {
  margin-bottom: var(--spacing-xxl);
}

.section-title {
  font-size: 1.75rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  position: relative;
  padding-bottom: var(--spacing-sm);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  border-radius: 2px;
}

.section-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.values-list {
  margin-top: var(--spacing-lg);
}

.value-item {
  display: flex;
  margin-bottom: var(--spacing-lg);
}

.value-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  color: var(--text-light);
  font-size: 1.25rem;
  flex-shrink: 0;
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}

.value-content {
  flex: 1;
}

.value-title {
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.value-text {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.5;
}



.contact-info {
  margin-top: var(--spacing-lg);
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.contact-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  color: var(--text-light);
  font-size: 1rem;
  flex-shrink: 0;
}

.contact-text {
  flex: 1;
  color: var(--text-secondary);
  font-size: 1rem;
}

@media (max-width: 767px) {
  .page-title {
    font-size: 2rem;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .team-members {
    grid-template-columns: 1fr;
  }
  
  .team-member {
    margin-bottom: var(--spacing-lg);
  }
}
