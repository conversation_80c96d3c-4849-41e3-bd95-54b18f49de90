<template>
  <div class="funding-rate-history-container">
    <div class="funding-rate-history-header">
      <h1>{{ $t('FundingRateHistory.title') }} <a href="https://www.binance.com/zh-CN/futures/funding-history/quarterly/funding-fee-history" target="_blank" class="binance-link">{{ $t('FundingRateHistory.viewOnBinance') }}</a></h1>
    </div>

  <FilterComponent
    ref="filterComponentRef"
    :filters="filterConfig"
    v-model="filters"
    :initialValues="filters"
    @search="applyFilters"
    @reset="resetFilters"
    @select-change="handleSymbolChange"
  />

    <!-- 折线图 -->
    <div class="chart-container">
      <h2 class="chart-title">{{ $t('FundingRateHistory.fundingRateChart') }} ({{ $t('FundingRateHistory.totalFundingRate') }}: <span :class="totalFundingRateSum > 0 ? 'positive-rate' : 'negative-rate'">{{ totalFundingRateSum.toFixed(6) }}%</span>)</h2>
      <div v-if="chartData.length === 0" class="no-data-chart">
        {{ $t('FundingRateHistory.noData') }}
      </div>
      <canvas v-else ref="chartCanvas"></canvas>
    </div>

    <Table
      :columns="tableColumns"
      :data="historyList"
      :noDataText="$t('FundingRateHistory.noData')"
      class="desktop-only"
    >
      <!-- 自定义列格式化 -->
      <template #last_funding_rate="{ item }">
        <span :class="getFundingRateClass(item.last_funding_rate)">
          {{ formatFundingRate(item.last_funding_rate) }}
        </span>
      </template>

      <template #mark_price="{ item }">
        {{ formatPrice(item.mark_price) }}
      </template>
    </Table>

    <!-- 卡片组件 - 移动端显示 -->
    <Cards
      :columns="tableColumns"
      :data="historyList"
      :noDataText="$t('FundingRateHistory.noData')"
    >
      <!-- 标题插槽 -->
      <template #title="{ item }">
        {{ item.calc_time }}
      </template>

      <!-- 资金费率插槽 -->
      <template #last_funding_rate="{ item }">
        <span :class="getFundingRateClass(item.last_funding_rate)">
          {{ formatFundingRate(item.last_funding_rate) }}
        </span>
      </template>

      <!-- 标记价格插槽 -->
      <template #mark_price="{ item }">
        {{ formatPrice(item.mark_price) }}
      </template>
    </Cards>

    <!-- 分页控件 -->
    <Pagination
      v-if="totalItems > 0"
      :total="totalItems"
      :page="currentPage"
      :limit="pageLimit"
      @change="handlePageChange"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import Chart from 'chart.js/auto'
import FilterComponent from '@/components/common/FilterComponent.vue'
import Table from '@/components/common/Table.vue'
import Cards from '@/components/common/Cards.vue'
import Pagination from '@/components/common/Pagination.vue'

export default {
  name: 'FundingRateHistory',
  components: {
    FilterComponent,
    Table,
    Cards,
    Pagination
  },
  setup() {
    const { t } = useI18n()
    const store = useStore()
    const filterComponentRef = ref(null)
    const chartCanvas = ref(null)
    let chart = null

    // 加载状态
    const loading = ref(false)

    // 日期格式化工具函数
    const dateUtils = {
      // 格式化日期为 YYYY-MM-DD
      formatDate: (date) => {
        const d = new Date(date)
        const year = d.getFullYear()
        const month = String(d.getMonth() + 1).padStart(2, '0')
        const day = String(d.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      },

      // 格式化日期，只显示月和日 MM-DD
      formatShortDate: (dateString) => {
        const date = new Date(dateString)
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${month}-${day}`
      },

      // 格式化完整日期（用于tooltip）YYYY-MM-DD HH:MM
      formatFullDate: (dateString) => {
        const date = new Date(dateString)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        return `${year}-${month}-${day} ${hours}:${minutes}`
      },

      // 获取指定天数前的日期
      getDateBefore: (days) => {
        const date = new Date()
        date.setDate(date.getDate() - days)
        return dateUtils.formatDate(date)
      },

      // 设置默认日期范围
      getDefaultDateRange: () => {
        const end = new Date()
        const start = new Date()
        start.setDate(start.getDate() - 14)

        return {
          start_date: dateUtils.formatDate(start),
          end_date: dateUtils.formatDate(end)
        }
      }
    }

    // 筛选条件，默认设置为近14天
    const defaultDateRange = dateUtils.getDefaultDateRange()
    const filters = reactive({
      symbol_id: '',
      start_date: defaultDateRange.start_date,
      end_date: defaultDateRange.end_date
    })

    // 合约列表
    const symbols = computed(() => store.getters['fundingRate/symbols'])

    // 历史资金费率列表（表格数据）
    const historyList = computed(() => store.getters['fundingRate/historyList'])

    // 历史资金费率列表（图表数据）
    const chartData = computed(() => store.getters['fundingRate/chartData'])

    // 当前选中的币种ID
    const currentSymbolId = computed(() => store.getters['fundingRate/currentSymbolId'])

    // 获取当前主题模式
    const isDarkMode = computed(() => store.getters.isDarkMode)

    // 更新日期选择器的值
    const updateDateRangePicker = (days) => {
      filters.start_date = dateUtils.getDateBefore(days)
      filters.end_date = dateUtils.formatDate(new Date())

      if (filterComponentRef.value) {
        filterComponentRef.value.filterValues = {
          ...filterComponentRef.value.filterValues,
          start_date: filters.start_date,
          end_date: filters.end_date
        }
        filterComponentRef.value.dateRangeValues['fundingRateDateRange'] = [
          new Date(filters.start_date),
          new Date(filters.end_date)
        ]

        // 手动触发搜索，确保使用最新的日期值
        applyFilters({
          ...filterComponentRef.value.filterValues,
          start_date: filters.start_date,
          end_date: filters.end_date
        })
      }
    }

    // 筛选配置
    const filterConfig = computed(() => [
      {
        type: 'select',
        id: 'symbolSelect',
        label: t('FundingRateHistory.symbol'),
        key: 'symbol_id',
        // placeholder: t('FundingRateHistory.selectSymbol'),
        options: symbols.value.map(symbol => ({
          value: symbol.id,
          text: symbol.symbol_name
        }))
      },
      {
        type: 'dateRange',
        id: 'fundingRateDateRange',
        label: t('FundingRateHistory.dateRange'),
        startKey: 'start_date',
        endKey: 'end_date',
        clearable: false,
      },
      {
        type: 'buttonGroup',
        id: 'dateQuickSelect',
        label: t('FundingRateHistory.quickSelect'),
        buttons: [
          {
            label: t('FundingRateHistory.last7Days'),
            value: 7,
            callback: () => updateDateRangePicker(7)
          },
          {
            label: t('FundingRateHistory.last14Days'),
            value: 14,
            callback: () => updateDateRangePicker(14)
          }
        ],
        // 默认选中第二个按钮（14天）
        defaultActiveIndex: 1,
        // 禁用自动搜索，因为在updateDateRangePicker中已经手动触发了搜索
        autoSearch: false
      },
    ])

    // 表格列配置
    const tableColumns = computed(() => [
      { key: 'calc_time', title: t('FundingRateHistory.calcTime')},
      { key: 'last_funding_rate', title: t('FundingRateHistory.lastFundingRate')},
      { key: 'mark_price', title: t('FundingRateHistory.markPrice')},
      { key: 'funding_interval_hours', title: t('FundingRateHistory.fundingIntervalHours')}
    ])

    // 分页相关
    const currentPage = computed(() => store.getters['pagination/pagination'].current)
    const pageLimit = computed(() => store.getters['pagination/pagination'].limit)
    const totalItems = computed(() => store.getters['pagination/pagination'].total)

    // 获取合约列表
    const fetchSymbols = async () => {
      try {
        loading.value = true
        await store.dispatch('fundingRate/fetchSymbols')

        // 强制默认选择第一个合约
        if (symbols.value.length > 0) {
          filters.symbol_id = symbols.value[0].id
          store.dispatch('fundingRate/setCurrentSymbolId', symbols.value[0].id)

          // 手动更新FilterComponent中的filterValues
          if (filterComponentRef.value) {
            filterComponentRef.value.filterValues.symbol_id = symbols.value[0].id
            // 手动触发emit('update:modelValue', {...filterValues})
            filterComponentRef.value.$emit('update:modelValue', {...filterComponentRef.value.filterValues})
          }
        }
      } catch (error) {
        store.dispatch('showNotification', {
          type: 'error',
          message: t('common.error')
        })
      } finally {
        loading.value = false
      }
    }

    // 获取历史资金费率（表格数据）
    const fetchHistoryList = async (params = {}) => {
      try {
        loading.value = true

        // 构建请求参数
        const requestParams = {
          page: params.page || currentPage.value,
          limit: params.limit || pageLimit.value,
          symbol_id: filters.symbol_id || currentSymbolId.value
        }

        // 从fundingRate模块获取历史资金费率
        await store.dispatch('fundingRate/fetchHistoryList', requestParams)
      } catch (error) {
        store.dispatch('showNotification', {
          type: 'error',
          message: t('common.error')
        })
      } finally {
        loading.value = false
      }
    }

    // 获取历史资金费率（图表数据）
    const fetchChartData = async () => {
      try {
        loading.value = true

        // 构建请求参数，不包含分页参数
        const requestParams = {
          symbol_id: filters.symbol_id || currentSymbolId.value,
          start_date: filters.start_date,
          end_date: filters.end_date
        }
        // 从fundingRate模块获取历史资金费率图表数据
        await store.dispatch('fundingRate/fetchChartData', requestParams)

        // 更新图表
        updateChart()
      } catch (error) {
        store.dispatch('showNotification', {
          type: 'error',
          message: t('common.error')
        })
      } finally {
        loading.value = false
      }
    }

    // 创建图表配置
    const createChartConfig = (data, labels, fundingRates, originalDates) => {
      // 获取主题相关颜色
      const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim()
      const textColor = getComputedStyle(document.documentElement).getPropertyValue('--text-primary').trim()
      const gridColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color').trim()
      const successColor = getComputedStyle(document.documentElement).getPropertyValue('--success-color').trim()
      const errorColor = getComputedStyle(document.documentElement).getPropertyValue('--error-color').trim()

      // 计算资金费率的最小值和最大值
      const minRate = Math.min(...fundingRates)
      const maxRate = Math.max(...fundingRates)

      // 计算数据范围
      const dataRange = maxRate - minRate

      // 扩大y轴范围，上下各增加30%的空间
      const padding = dataRange * 0.3
      const yMin = minRate - padding
      const yMax = maxRate + padding

      return {
        type: 'line',
        data: {
          labels: labels,
          datasets: [{
            label: t('FundingRateHistory.lastFundingRate'),
            data: fundingRates,
            borderColor: primaryColor,
            borderWidth: 2,
            pointRadius: 3,
            pointBackgroundColor: function(context) {
              const value = context.raw;
              return value >= 0 ? successColor : errorColor;
            }
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          interaction: {
            mode: 'index',
            intersect: false,
          },
          plugins: {
            tooltip: {
              callbacks: {
                title: function(tooltipItems) {
                  // 显示完整日期
                  const dataIndex = tooltipItems[0].dataIndex;
                  return dateUtils.formatFullDate(originalDates[dataIndex]);
                },
                label: function(context) {
                  return `${context.dataset.label}: ${context.raw.toFixed(6)}%`
                }
              }
            },
            legend: {
              display: false,
            }
          },
          scales: {
            x: {
              grid: {
                color: gridColor,
                borderColor: gridColor,
                tickColor: gridColor
              },
              ticks: {
                color: textColor,
                font: {
                  family: 'var(--font-family)',
                  size: 12
                }
              }
            },
            y: {
              grid: {
                color: function(context) {
                  // 突显y=0的x轴，根据主题模式设置不同颜色
                  if (context.tick.value === 0) {
                    return isDarkMode.value ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)';
                  }
                  return gridColor;
                },
                lineWidth: function(context) {
                  // 加粗y=0的线
                  if (context.tick.value === 0) {
                    return 2;
                  }
                  return 1;
                },
                borderColor: gridColor,
                tickColor: gridColor,
              },
              ticks: {
                color: textColor,
                font: {
                  family: 'var(--font-family)',
                  size: 12
                },
                callback: function(value) {
                  return value.toFixed(4) + '%'
                }
              },
              // 设置y轴的最小值和最大值，使折线图显示在中间位置
              min: yMin,
              max: yMax,
              // 确保y=0的线始终显示
              afterFit: function(scaleInstance) {
                // 确保y=0在可见范围内
                if (scaleInstance.min > 0 || scaleInstance.max < 0) {
                  if (scaleInstance.min > 0) {
                    scaleInstance.min = 0;
                  }
                  if (scaleInstance.max < 0) {
                    scaleInstance.max = 0;
                  }
                }
              }
            }
          }
        }
      }
    }

    // 更新图表
    const updateChart = () => {
      if (chartData.value.length === 0) return

      // 销毁旧图表
      if (chart) {
        chart.destroy()
      }

      // 准备图表数据
      const data = chartData.value
        .slice()
        .sort((a, b) => new Date(a.calc_time) - new Date(b.calc_time))

      // 保存原始日期数据用于tooltip
      const originalDates = data.map(item => item.calc_time)
      // 对labels进行处理，连续相同日期只显示一次，后续用空字符串替代
      const rawLabels = data.map(item => dateUtils.formatShortDate(item.calc_time))
      const labels = rawLabels.map((label, index) => {
        if (index === 0) return label
        return label === rawLabels[index - 1] ? '' : label
      })
      const fundingRates = data.map(item => parseFloat(item.last_funding_rate) * 100)

      // 创建图表配置
      const chartConfig = createChartConfig(data, labels, fundingRates, originalDates)

      // 创建新图表
      const ctx = chartCanvas.value.getContext('2d')
      chart = new Chart(ctx, chartConfig)
    }

    // 应用筛选
    const applyFilters = (filterParams) => {
      // 更新本地筛选条件
      Object.keys(filterParams).forEach(key => {
        if (filterParams[key] !== undefined) {
          filters[key] = filterParams[key]
        }
      })

      // 如果选择了币种，更新当前选中的币种ID
      if (filterParams.symbol_id) {
        store.dispatch('fundingRate/setCurrentSymbolId', filterParams.symbol_id)
      }

      // 重新加载历史资金费率列表，使用当前页码
      fetchHistoryList({
        page: 1, // 筛选时重置为第一页
        limit: pageLimit.value
      })

      // 重新加载图表数据
      fetchChartData()
    }

    // 重置筛选
    const resetFilters = () => {
      // 重置为默认值
      if (symbols.value.length > 0) {
        filters.symbol_id = symbols.value[0].id
        store.dispatch('fundingRate/setCurrentSymbolId', symbols.value[0].id)
      }
      filters.start_date = defaultDateRange.start_date
      filters.end_date = defaultDateRange.end_date

      // 手动更新FilterComponent中的filterValues
      if (filterComponentRef.value) {
        filterComponentRef.value.filterValues = {
          symbol_id: symbols.value.length > 0 ? symbols.value[0].id : '',
          start_date: defaultDateRange.start_date,
          end_date: defaultDateRange.end_date
        }
        filterComponentRef.value.dateRangeValues['fundingRateDateRange'] = [
          new Date(defaultDateRange.start_date),
          new Date(defaultDateRange.end_date)
        ]
        // 重置快捷按钮选中状态为默认（14天）
        filterComponentRef.value.activeButtonIndex['dateQuickSelect'] = 1
        // 触发对应按钮的回调，更新日期范围
        const quickSelectFilter = filterComponentRef.value.filters.find(f => f.id === 'dateQuickSelect')
        if (quickSelectFilter && quickSelectFilter.buttons && quickSelectFilter.buttons[1] && quickSelectFilter.buttons[1].callback) {
          quickSelectFilter.buttons[1].callback()
        }
      }

      // 重新加载数据
      fetchHistoryList({ page: 1, limit: pageLimit.value })
      fetchChartData()
    }

    // 处理分页变化
    const handlePageChange = ({ page, limit }) => {
      if (page && limit) {
        fetchHistoryList({ page, limit })
      }
    }

    // 处理币种变化
    const handleSymbolChange = ({ key, value }) => {
      if (key === 'symbol_id' && value) {
        store.dispatch('fundingRate/setCurrentSymbolId', value)

        // 更新本地筛选条件
        filters.symbol_id = value

        // 手动触发搜索，确保使用最新的合约ID
        applyFilters({
          ...filters,
          symbol_id: value
        })
      }
    }

    // 格式化资金费率
    const formatFundingRate = (rate) => {
      if (rate === null || rate === undefined) return '-'
      return (parseFloat(rate) * 100).toFixed(6) + '%'
    }

    // 获取资金费率的CSS类
    const getFundingRateClass = (rate) => {
      if (rate === null || rate === undefined) return ''
      const rateValue = parseFloat(rate)
      if (rateValue > 0) return 'positive-rate'
      if (rateValue < 0) return 'negative-rate'
      return ''
    }

    // 格式化价格
    const formatPrice = (price) => {
      if (price === null || price === undefined || price === 0) return '-'
      return parseFloat(price).toFixed(8) + ' USD'
    }

    // 监听当前选中的币种ID变化
    watch(currentSymbolId, (newVal, oldVal) => {
      if (newVal !== oldVal && newVal) {
        filters.symbol_id = newVal
      }
    })

    // 监听时间范围变化，更新快捷按钮选中状态
    watch(
      () => [filters.start_date, filters.end_date],
      ([newStart, newEnd]) => {
        if (!filterComponentRef.value) return

        const today = new Date()
        const formatDate = (date) => {
          const d = new Date(date)
          const year = d.getFullYear()
          const month = String(d.getMonth() + 1).padStart(2, '0')
          const day = String(d.getDate()).padStart(2, '0')
          return `${year}-${month}-${day}`
        }

        const dateBefore = (days) => {
          const date = new Date()
          date.setDate(date.getDate() - days)
          return formatDate(date)
        }

        const last7DaysStart = dateBefore(7)
        const last14DaysStart = dateBefore(14)
        const todayStr = formatDate(today)

        if (newStart === last7DaysStart && newEnd === todayStr) {
          filterComponentRef.value.activeButtonIndex['dateQuickSelect'] = 0
        } else if (newStart === last14DaysStart && newEnd === todayStr) {
          filterComponentRef.value.activeButtonIndex['dateQuickSelect'] = 1
        } else {
          filterComponentRef.value.activeButtonIndex['dateQuickSelect'] = -1
        }
      }
    )

    // 监听主题模式变化，当主题变化时重绘图表但不重新获取数据
    watch(isDarkMode, (newVal, oldVal) => {
      if (newVal !== oldVal && chartData.value.length > 0) {
        // 只重绘图表，不重新获取数据
        updateChart()
      }
    })

    // 初始化
    onMounted(async () => {
      // 获取合约列表
      await fetchSymbols()

      // 获取历史资金费率列表
      fetchHistoryList({
        page: 1,
        limit: pageLimit.value
      })

      // 获取图表数据
      fetchChartData()
    })

    // 组件卸载时清理图表
    onUnmounted(() => {
      if (chart) {
        chart.destroy()
        chart = null
      }
    })

    const totalFundingRateSum = computed(() => {
      if (!chartData.value || chartData.value.length === 0) return 0
      return chartData.value.reduce((sum, item) => {
        const rate = parseFloat(item.last_funding_rate)
        return sum + (isNaN(rate) ? 0 : rate * 100)
      }, 0)
    })

    return {
      loading,
      filters,
      filterConfig,
      tableColumns,
      historyList,
      chartData,
      currentPage,
      pageLimit,
      totalItems,
      symbols,
      currentSymbolId,
      applyFilters,
      resetFilters,
      handlePageChange,
      handleSymbolChange,
      formatFundingRate,
      getFundingRateClass,
      formatPrice,
      filterComponentRef,
      chartCanvas,
      totalFundingRateSum
    }
  }
}
</script>

<style>
@import '@/styles/views/fundingRateHistory.css';
</style>
