/* App-specific styles */
#app {
  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  min-height: 100vh;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Page transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Notification styles */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: var(--z-index-tooltip);
  max-width: 350px;
}

.notification {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-md);
  background-color: var(--bg-card);
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow);
  margin-bottom: var(--spacing-md);
  animation: slide-in 0.3s ease;
  cursor: pointer;
}

.notification-icon {
  margin-right: var(--spacing-md);
  font-size: var(--font-size-xl);
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-success .notification-icon {
  color: var(--success-color);
}

.notification-error .notification-icon {
  color: var(--error-color);
}

.notification-warning .notification-icon {
  color: var(--warning-color);
}

.notification-info .notification-icon {
  color: var(--info-color);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
}

.notification-message {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: var(--spacing-xs);
  margin-left: var(--spacing-sm);
  font-size: var(--font-size-md);
  transition: color var(--transition-fast);
}

.notification-close:hover {
  color: var(--text-primary);
}

@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@media (max-width: 576px) {
  .notification-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
}
