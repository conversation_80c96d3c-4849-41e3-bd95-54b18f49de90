<template>
  <div class="user-page">
    <div class="page-header">
      <h1 class="page-title">{{ $t('DailyProfit.title') }}</h1>
      <div class="page-subtitle">{{ $t('DailyProfit.subtitle') }}</div>
    </div>

    <div class="daily-profit-content">
      <!-- 筛选组件 -->
      <FilterComponent
        :filters="filterConfig"
        :initial-values="filterValues"
        @search="handleFilterSearch"
        @reset="handleFilterReset"
      />



      <!-- 数据表格 -->
      <Table :columns="tableColumns" :data="records" :no-data-text="$t('DailyProfit.noData')"
        class="desktop-only">
        <template #date="{ item }">
          {{ item.date }}
        </template>
        <template #profit="{ item }">
          <span :class="item.profit > 0 ? 'profit-positive' : 'profit-negative'">
            {{ item.profit }}
          </span>
        </template>
        <template #daily_rate="{ item }">
          <span v-if="item.daily_rate !== null" :class="item.daily_rate > 0 ? 'profit-positive' : 'profit-negative'">
            {{ item.daily_rate }}%
          </span>
          <span v-else>-</span>
        </template>
        <template #trend="{ item }">
          <span v-if="item.trend !== null" :class="item.trend > 0 ? 'trend-up' : 'trend-down'">
            <i :class="item.trend > 0 ? 'fa fa-arrow-up' : 'fa fa-arrow-down'"></i>
            {{ Math.abs(item.trend) }}%
          </span>
          <span v-else>-</span>
        </template>
      </Table>

      <!-- 移动端卡片视图 -->
      <Cards :columns="tableColumns" :data="records" :no-data-text="$t('DailyProfit.noData')">
        <template #date="{ item }">
          {{ item.date }}
        </template>
        <template #profit="{ item }">
          <span :class="item.profit > 0 ? 'profit-positive' : 'profit-negative'">
            {{ item.profit }}
          </span>
        </template>
        <template #daily_rate="{ item }">
          <span v-if="item.daily_rate !== null" :class="item.daily_rate > 0 ? 'profit-positive' : 'profit-negative'">
            {{ item.daily_rate }}%
          </span>
          <span v-else>-</span>
        </template>
        <template #trend="{ item }">
          <span v-if="item.trend !== null" :class="item.trend > 0 ? 'trend-up' : 'trend-down'">
            <i :class="item.trend > 0 ? 'fa fa-arrow-up' : 'fa fa-arrow-down'"></i>
            {{ Math.abs(item.trend) }}%
          </span>
          <span v-else>-</span>
        </template>
      </Cards>

      <!-- 分页组件 -->
      <Pagination v-if="totalRecords > 0" :total="totalRecords" :page="currentPage" :limit="limit"
        @change="handlePageChange" use-store />
    </div>
  </div>
</template>

<script>
import { computed, onMounted, reactive } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import Table from '@/components/common/Table.vue'
import Pagination from '@/components/common/Pagination.vue'
import Cards from '@/components/common/Cards.vue'
import FilterComponent from '@/components/common/FilterComponent.vue'
import { showError } from '@/utils/utils'

export default {
  name: 'DailyProfitPage',
  components: {
    Table,
    Pagination,
    Cards,
    FilterComponent
  },
  setup() {
    const store = useStore()
    const { t } = useI18n()

    // 筛选配置
    const filterConfig = computed(() => [
      {
        type: 'dateRange',
        id: 'profitDateRange',
        label: 'FilterComponent.dateRange',
        startKey: 'startDate',
        endKey: 'endDate'
      }
    ])

    // 筛选值
    const filterValues = reactive({
      startDate: '',
      endDate: ''
    })

    // 表格列配置
    const tableColumns = computed(() => [
      { key: 'date', title: t('DailyProfit.date') },
      { key: 'profit', title: t('DailyProfit.profit') },
      { key: 'daily_rate', title: t('DailyProfit.dailyRate') },
      { key: 'trend', title: t('DailyProfit.trend') }
    ])



    const records = computed(() => store.getters['account/dailyProfitRecords'])
    const currentPage = computed(() => store.getters['pagination/pagination'].current)
    const limit = computed(() => store.getters['pagination/pagination'].limit)
    const totalRecords = computed(() => store.getters['pagination/pagination'].total)

    // 获取每日收益记录
    const fetchDailyProfitRecords = (params = {}) => {
      // 合并筛选参数
      const queryParams = {
        ...params,
        startDate: filterValues.startDate || '',
        endDate: filterValues.endDate || ''
      }

      store.dispatch('account/fetchDailyProfitRecords', queryParams).catch(error => {
        console.error('获取每日收益记录失败:', error)
        showError(t, error.message || '获取每日收益记录失败')
      })
    }

    // 处理分页变化
    const handlePageChange = ({ page, limit: size }) => {
      if (page && size) {
        fetchDailyProfitRecords({ page, limit: size })
      }
    }

    // 处理筛选搜索
    const handleFilterSearch = (values) => {
      // 重置分页到第一页
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value || 10
      }, { root: true })

      // 更新筛选值
      Object.keys(values).forEach(key => {
        filterValues[key] = values[key]
      })

      // 获取数据
      fetchDailyProfitRecords({
        page: 1,
        limit: limit.value || 10
      })
    }

    // 处理筛选重置
    const handleFilterReset = () => {
      // 重置分页到第一页
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value || 10
      }, { root: true })

      // 重置筛选值
      filterValues.startDate = ''
      filterValues.endDate = ''

      // 获取数据
      fetchDailyProfitRecords({
        page: 1,
        limit: limit.value || 10
      })
    }

    onMounted(() => {
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value || 10
      }, { root: true })

      fetchDailyProfitRecords({
        page: 1,
        limit: limit.value || 10
      })
    })

    return {
      tableColumns,
      records,
      currentPage,
      limit,
      totalRecords,
      handlePageChange,
      // 筛选相关
      filterConfig,
      filterValues,
      handleFilterSearch,
      handleFilterReset
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/user/common.css';
@import '@/styles/views/user/overview/dailyProfit.css';
</style>
