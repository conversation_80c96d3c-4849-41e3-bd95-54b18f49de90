<?php
declare(strict_types=1);

namespace app\validate;

use think\Validate;

/**
 * 用户相关验证器
 */
class User extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'username' => 'max:30',
        'avatar' => 'url',
        'oldPassword' => 'require|min:6|max:20',
        'newPassword' => 'require|min:6|max:20|different:oldPassword',
        'email' => 'require|email',
        'code' => 'require|number|length:6',
        'phone' => 'require|mobile'
    ];

    /**
     * 错误消息
     * @var array
     */
    protected $message = [
        'username.max' => '用户名不能超过30个字符',
        'avatar.url' => '头像URL格式不正确',
        'oldPassword.require' => '旧密码不能为空',
        'oldPassword.min' => '旧密码长度不能少于6个字符',
        'oldPassword.max' => '旧密码长度不能超过20个字符',
        'newPassword.require' => '新密码不能为空',
        'newPassword.min' => '新密码长度不能少于6个字符',
        'newPassword.max' => '新密码长度不能超过20个字符',
        'newPassword.different' => '新密码不能与旧密码相同',
        'email.require' => '邮箱不能为空',
        'email.email' => '邮箱格式不正确',
        'code.require' => '验证码不能为空',
        'code.number' => '验证码必须为数字',
        'code.length' => '验证码长度必须为6位',
        'phone.require' => '手机号不能为空',
        'phone.mobile' => '手机号格式不正确'
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        'updateProfile' => ['username', 'avatar'],
        'changePassword' => ['oldPassword', 'newPassword'],
        'bindEmail' => ['email', 'code'],
        'sendEmailCode' => ['email'],
        'bindPhone' => ['phone', 'code'],
        'sendPhoneCode' => ['phone']
    ];
}
