/* RGB 颜色变量，用于透明度效果 */
:root {
  /* 主要颜色的 RGB 值 */
  --primary-color-rgb: 240, 185, 11;
  --primary-color-light-rgb: 248, 211, 58;
  --primary-color-dark-rgb: 224, 168, 0;
  --secondary-color-rgb: 30, 35, 41;
  --secondary-color-light-rgb: 43, 49, 57;
  --secondary-color-dark-rgb: 20, 23, 28;
  --success-color-rgb: 2, 192, 118;
  --warning-color-rgb: 240, 185, 11;
  --error-color-rgb: 246, 70, 93;
  --info-color-rgb: 14, 202, 240;
  
  /* 背景颜色的 RGB 值 */
  --bg-primary-rgb: 255, 255, 255;
  --bg-secondary-rgb: 245, 245, 245;
  --bg-tertiary-rgb: 234, 236, 239;
  
  /* 边框颜色的 RGB 值 */
  --border-color-rgb: 230, 232, 234;
  --border-color-light-rgb: 240, 241, 242;
  --border-color-dark-rgb: 207, 214, 228;
}

/* 暗色模式的 RGB 值 */
.dark-mode {
  --bg-primary-rgb: 11, 14, 17;
  --bg-secondary-rgb: 30, 35, 41;
  --bg-tertiary-rgb: 43, 49, 57;
  --border-color-rgb: 43, 49, 57;
  --border-color-light-rgb: 43, 49, 57;
  --border-color-dark-rgb: 71, 77, 87;
}
