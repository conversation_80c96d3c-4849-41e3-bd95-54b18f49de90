/* 首页样式 */
.home-page {
  overflow-x: hidden;
}

/* 顶部横幅 */
.banner {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-color-dark) 100%);
  padding: var(--spacing-xxl) 0;
  position: relative;
  overflow: hidden;
  color: var(--text-light);
}

.banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQ0MCIgaGVpZ2h0PSI1MDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBkPSJNMCAwaDEyMDB2NTAwSDB6Ii8+PHBhdGggZD0iTTAgMGw1MDAgNTAwSDB6IiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9Ii4wNSIvPjxwYXRoIGQ9Ik0wIDUwMGw1MDAtNTAwaDUwMHoiIGZpbGw9IiNmZmYiIGZpbGwtb3BhY2l0eT0iLjA1Ii8+PHBhdGggZD0iTTUwMCA1MDBsMjAwLTIwMGg1MDB2MjAwaC03MDB6IiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9Ii4wNSIvPjxwYXRoIGQ9Ik0xMDAwIDUwMGwyMDAtMjAwdjIwMGgtMjAweiIgZmlsbD0iI2ZmZiIgZmlsbC1vcGFjaXR5PSIuMDUiLz48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI1MCIgZmlsbD0iI2ZmZiIgZmlsbC1vcGFjaXR5PSIuMDUiLz48Y2lyY2xlIGN4PSIxNTAiIGN5PSIxNTAiIHI9IjUwIiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9Ii4wNSIvPjxjaXJjbGUgY3g9IjI1MCIgY3k9IjI1MCIgcj0iNTAiIGZpbGw9IiNmZmYiIGZpbGwtb3BhY2l0eT0iLjA1Ii8+PGNpcmNsZSBjeD0iMzUwIiBjeT0iMzUwIiByPSI1MCIgZmlsbD0iI2ZmZiIgZmlsbC1vcGFjaXR5PSIuMDUiLz48Y2lyY2xlIGN4PSI0NTAiIGN5PSI0NTAiIHI9IjUwIiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9Ii4wNSIvPjwvZz48L3N2Zz4=');
  background-size: cover;
  opacity: 0.1;
}

.banner .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.banner-content {
  flex: 1;
  max-width: 600px;
}

.banner-title {
  font-size: 3rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  line-height: 1.2;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(240, 185, 11, 0.3);
}

.banner-subtitle {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.9;
}

.banner-buttons {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.banner-image {
  flex: 1;
  max-width: 500px;
  margin-left: var(--spacing-xl);
  position: relative;
  animation: float 6s ease-in-out infinite;
}

.banner-img {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
}

.banner-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.tech-circle {
  position: absolute;
  border-radius: 50%;
  border: 2px solid var(--primary-color);
  opacity: 0.3;
}

.tech-circle:nth-child(1) {
  width: 100px;
  height: 100px;
  top: -20px;
  left: -20px;
  animation: pulse 3s infinite;
}

.tech-circle:nth-child(2) {
  width: 150px;
  height: 150px;
  bottom: -30px;
  right: -30px;
  animation: pulse 4s infinite;
  animation-delay: 1s;
}

.tech-circle:nth-child(3) {
  width: 70px;
  height: 70px;
  top: 50%;
  right: -10px;
  animation: pulse 2.5s infinite;
  animation-delay: 0.5s;
}


@keyframes glow {
  0% {
    transform: rotate(45deg) translateX(-100%) translateY(-100%);
  }
  100% {
    transform: rotate(45deg) translateX(100%) translateY(100%);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* 核心功能 */
.features {
  padding: var(--spacing-xxl) 0;
  background-color: var(--bg-secondary);
}

.section-title {
  text-align: center;
  margin-bottom: var(--spacing-md);
  font-weight: var(--font-weight-bold);
  position: relative;
  padding-bottom: var(--spacing-md);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  border-radius: 2px;
}

.section-subtitle {
  text-align: center;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.feature-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  height: 100%;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  box-shadow: var(--box-shadow);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin: var(--spacing-md);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--box-shadow-hover);
}

.feature-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
  color: var(--text-light);
  font-size: 2rem;
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}

.feature-title {
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-sm);
}

.feature-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--spacing-md) 0;
  text-align: left;
  width: 100%;
}

.feature-list li {
  padding: var(--spacing-xs) 0;
  position: relative;
  padding-left: 25px;
  color: var(--text-secondary);
}

.feature-list li::before {
  content: '\f00c';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-size: 0.8rem;
}

.feature-link {
  margin-top: auto;
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  display: inline-flex;
  align-items: center;
  transition: color var(--transition-fast);
}

.feature-link i {
  margin-left: var(--spacing-xs);
  transition: transform var(--transition-fast);
}

.feature-link:hover {
  color: var(--primary-color-dark);
}

.feature-link:hover i {
  transform: translateX(3px);
}

/* 资金费率走势图 */
.funding-rate-chart-section {
  padding: var(--spacing-xxl) 0;
  background-color: var(--bg-primary);
}

.funding-rate-chart-section .chart-container {
  margin: 20px 0;
  padding: 15px;
  background-color: var(--bg-card);
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow);
  height: 400px;
  transition: background-color var(--transition-normal),
              box-shadow var(--transition-normal),
              border-color var(--transition-normal);
}

.funding-rate-chart-section .chart-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 15px;
  color: var(--text-primary);
  transition: color var(--transition-normal);
}

.funding-rate-chart-section .no-data-chart {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: var(--text-tertiary);
  font-size: 16px;
}

.funding-rate-chart-section .positive-rate {
  color: var(--success-color);
}

.funding-rate-chart-section .negative-rate {
  color: var(--error-color);
}

.funding-rate-chart-section .chart-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.funding-rate-chart-section .view-more-link {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  transition: color var(--transition-fast);
}

.funding-rate-chart-section .view-more-link i {
  margin-left: var(--spacing-xs);
  transition: transform var(--transition-fast);
}

.funding-rate-chart-section .view-more-link:hover {
  color: var(--primary-color-dark);
}

.funding-rate-chart-section .view-more-link:hover i {
  transform: translateX(3px);
}

/* 暗色模式下的图表容器样式 */
.dark-mode .funding-rate-chart-section .chart-container {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
}

.dark-mode .funding-rate-chart-section .chart-title {
  color: var(--text-primary);
}

.dark-mode .funding-rate-chart-section .no-data-chart {
  color: var(--text-tertiary);
}

/* 开始使用 */
.cta {
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-color-light) 100%);
  color: var(--text-light);
  position: relative;
  overflow: hidden;
}

.cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQ0MCIgaGVpZ2h0PSI1MDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBkPSJNMCAwaDEyMDB2NTAwSDB6Ii8+PHBhdGggZD0iTTAgMGw1MDAgNTAwSDB6IiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9Ii4wNSIvPjxwYXRoIGQ9Ik0wIDUwMGw1MDAtNTAwaDUwMHoiIGZpbGw9IiNmZmYiIGZpbGwtb3BhY2l0eT0iLjA1Ii8+PHBhdGggZD0iTTUwMCA1MDBsMjAwLTIwMGg1MDB2MjAwaC03MDB6IiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9Ii4wNSIvPjxwYXRoIGQ9Ik0xMDAwIDUwMGwyMDAtMjAwdjIwMGgtMjAweiIgZmlsbD0iI2ZmZiIgZmlsbC1vcGFjaXR5PSIuMDUiLz48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI1MCIgZmlsbD0iI2ZmZiIgZmlsbC1vcGFjaXR5PSIuMDUiLz48Y2lyY2xlIGN4PSIxNTAiIGN5PSIxNTAiIHI9IjUwIiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9Ii4wNSIvPjxjaXJjbGUgY3g9IjI1MCIgY3k9IjI1MCIgcj0iNTAiIGZpbGw9IiNmZmYiIGZpbGwtb3BhY2l0eT0iLjA1Ii8+PGNpcmNsZSBjeD0iMzUwIiBjeT0iMzUwIiByPSI1MCIgZmlsbD0iI2ZmZiIgZmlsbC1vcGFjaXR5PSIuMDUiLz48Y2lyY2xlIGN4PSI0NTAiIGN5PSI0NTAiIHI9IjUwIiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9Ii4wNSIvPjwvZz48L3N2Zz4=');
  background-size: cover;
  opacity: 0.1;
}

.cta-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(240, 185, 11, 0.3);
}

.cta-description {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.9;
}

.cta-features {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.cta-feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.cta-feature i {
  font-size: 2rem;
  color: var(--primary-color);
  background: rgba(240, 185, 11, 0.1);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-xs);
}

.cta-feature span {
  font-weight: var(--font-weight-medium);
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

/* 响应式调整 */
@media (max-width: 991px) {
  .banner .container {
    flex-direction: column;
    text-align: center;
  }

  .banner-content {
    max-width: 100%;
    margin-bottom: var(--spacing-xl);
  }

  .banner-image {
    margin-left: 0;
    max-width: 80%;
  }

  .banner-title {
    font-size: 2.5rem;
  }

  .banner-buttons {
    justify-content: center;
  }

  .cta-title {
    font-size: 2rem;
  }

  .cta-features {
    gap: var(--spacing-lg);
  }
}

@media (max-width: 767px) {
  .banner-title {
    font-size: 2rem;
  }

  .banner-subtitle {
    font-size: 1rem;
  }

  .cta-title {
    font-size: 1.75rem;
  }

  .cta-description {
    font-size: 1rem;
  }

  .cta-features {
    gap: var(--spacing-md);
  }
}

/* 科技感元素 */
.tech-accent {
  position: relative;
}

.tech-accent::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 10% 10%, rgba(240, 185, 11, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 90% 90%, rgba(240, 185, 11, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: 50px 50px;
  background-image:
    linear-gradient(to right, rgba(240, 185, 11, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(240, 185, 11, 0.05) 1px, transparent 1px);
  opacity: 0.3;
  pointer-events: none;
}

.tech-glow {
  position: relative;
}

.tech-glow::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 0 30px rgba(240, 185, 11, 0.3);
  opacity: 0;
  transition: opacity var(--transition-normal);
  pointer-events: none;
  border-radius: inherit;
}

.tech-glow:hover::after {
  opacity: 1;
}

/* 动画效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1; /* 保持不变 */
  }
  50% {
    transform: scale(1.05);
    opacity: 1; /* 保持不变 */
  }
  100% {
    transform: scale(1);
    opacity: 1; /* 保持不变 */
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* 额外的科技感元素 */
.tech-dots {
  position: absolute;
  width: 200px;
  height: 200px;
  background-image: radial-gradient(circle, var(--primary-color) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.1;
  pointer-events: none;
}

.tech-line {
  position: absolute;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.2;
  pointer-events: none;
}
/* 加载动画样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(240, 185, 11, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}