<template>
  <div class="modal" v-if="modelValue">
    <div class="modal-backdrop"></div>
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{{ title }}</h5>
          <button type="button" class="btn-close" @click="closeModal">
            <i class="fa fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <slot></slot>
        </div>
        <div class="modal-footer" v-if="showFooter">
          <slot name="footer">
            <button type="button" class="btn btn-secondary" @click="closeModal">
              {{ $t('Modal.cancel') }}
            </button>
            <button type="button" class="btn btn-primary" @click="confirm">
              {{ $t('Modal.confirm') }}
            </button>
          </slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'Modal',
  props: {
    // 使用v-model绑定显示状态
    modelValue: {
      type: Boolean,
      default: false
    },
    // 模态框标题
    title: {
      type: String,
      required: true
    },
    // 是否显示底部按钮区域
    showFooter: {
      type: Boolean,
      default: true
    }
  },
  emits: ['update:modelValue', 'confirm'],
  setup(props, { emit }) {
    // 关闭模态框
    const closeModal = () => {
      emit('update:modelValue', false)
    }

    // 确认操作
    const confirm = () => {
      emit('confirm')
    }

    return {
      closeModal,
      confirm
    }
  }
})
</script>

<style scoped>
@import '@/styles/components/modal.css';
</style>
