<?php
declare(strict_types=1);

namespace app\utils;

use think\facade\Config;

/**
 * 数据加密解密工具类
 */
class Crypto
{
    /**
     * 加密方法
     *
     * @param array|string $data 要加密的数据
     * @return string|array 加密后的数据
     */
    public static function encrypt($data)
    {
        $config = Config::get('crypto', []);
        $key = $config['secret_key'] ?? 'lianghua_encryption_key';
        $cipher = $config['cipher'] ?? 'aes-256-cbc';

        // 生成IV
        $ivlen = openssl_cipher_iv_length($cipher);
        $iv = openssl_random_pseudo_bytes($ivlen);

        // 如果是数组，先转成JSON
        if (is_array($data)) {
            $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        }

        // 加密数据
        $encrypted = openssl_encrypt($data, $cipher, $key, 0, $iv);
        
        // 将IV和加密数据合并，并进行base64编码
        $result = base64_encode($iv . $encrypted);
        
        return $result;
    }

    /**
     * 解密方法
     *
     * @param string $data 要解密的数据
     * @return string|array 解密后的数据
     */
    public static function decrypt(string $data)
    {
        $config = Config::get('crypto', []);
        $key = $config['secret_key'] ?? 'lianghua_encryption_key';
        $cipher = $config['cipher'] ?? 'aes-256-cbc';

        // base64解码
        $data = base64_decode($data);
        if ($data === false) {
            return '';
        }

        // 获取IV长度
        $ivlen = openssl_cipher_iv_length($cipher);
        
        // 分离IV和加密数据
        $iv = substr($data, 0, $ivlen);
        $encrypted = substr($data, $ivlen);
        
        // 解密
        $decrypted = openssl_decrypt($encrypted, $cipher, $key, 0, $iv);
        
        // 尝试JSON解码
        $json = json_decode($decrypted, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $json;
        }
        
        return $decrypted;
    }
} 