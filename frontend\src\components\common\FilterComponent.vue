<template>
  <div class="filter-section">
    <template v-for="(filter, index) in filters" :key="index">
      <!-- 按钮组筛选 -->
      <div v-if="filter.type === 'buttonGroup'" class="filter-item">
        <label v-if="filter.label">{{ $t(filter.label) }}</label>
        <div class="filter-button-group">
          <button
            v-for="(button, btnIndex) in filter.buttons"
            :key="btnIndex"
            class="filter-button"
            :class="{ 'active': activeButtonIndex[filter.id] === btnIndex }"
            @click="handleButtonGroupClick(filter, button, btnIndex)"
          >
            {{ button.label }}
          </button>
        </div>
      </div>

      <!-- 日期范围筛选 -->
      <div v-else-if="filter.type === 'dateRange'" class="filter-item">
        <label :for="filter.id">{{ $t(filter.label) }}</label>
        <Datepicker
          v-model="dateRangeValues[filter.id]"
          :id="filter.id"
          range
          :enable-time-picker="false"
          :format="formatDate"
          :placeholder="$t('FilterComponent.selectDateRange')"
          :locale="currentLocale"
          :preview-format="formatPreview"
          :dark="isDarkMode"
          @update:model-value="handleDateRangeChange(filter.startKey, filter.endKey, filter.id)"
          class="date-picker"
          :select-text="$t('FilterComponent.select')"
          :cancel-text="$t('FilterComponent.cancel')"
          :clearable="filter.clearable !== false"
        />
      </div>

      <!-- 下拉选择筛选 -->
      <div v-else-if="filter.type === 'select'" class="filter-item">
        <label :for="filter.id">{{ $t(filter.label) }}</label>
        <select :id="filter.id" v-model="filterValues[filter.key]" class="form-control" @change="handleSelectChange(filter.key, filterValues[filter.key])">
          <option v-if="filter.placeholder" value="">{{ filter.placeholder || $t('FilterComponent.all')}}</option>
          <option
            v-for="option in filter.options"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label ? option.label : option.text }}
          </option>
        </select>
      </div>

      <!-- 文本搜索筛选 -->
      <div v-else-if="filter.type === 'search'" class="filter-item">
        <label :for="filter.id">{{ $t(filter.label) }}</label>
        <input
          type="text"
          :id="filter.id"
          v-model="filterValues[filter.key]"
          class="form-control"
          :placeholder="$t(filter.placeholder || '')"
        />
      </div>
    </template>

    <!-- 筛选操作按钮 -->
    <div class="filter-actions">
      <button class="btn btn-primary" @click="handleSearch">
        <i class="fa fa-search"></i> {{ $t('FilterComponent.search') }}
      </button>
      <button class="btn btn-outline-secondary" @click="handleReset">
        <i class="fa fa-redo"></i> {{ $t('FilterComponent.reset') }}
      </button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, watch, toRefs, computed } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import Datepicker from '@vuepic/vue-datepicker'
import '@vuepic/vue-datepicker/dist/main.css'

export default {
  name: 'FilterComponent',
  props: {
    // 筛选配置
    filters: {
      type: Array,
      required: true,
      // 每个筛选项的配置格式
      // {
      //   type: 'dateRange' | 'select' | 'search', // 筛选类型
      //   id: String, // 元素ID
      //   label: String, // 标签文本（i18n键）
      //   key: String, // 值的键名（用于select和search类型）
      //   startKey: String, // 开始日期的键名（用于dateRange类型）
      //   endKey: String, // 结束日期的键名（用于dateRange类型）
      //   placeholder: String, // 占位符文本（i18n键，可选）
      //   options: Array, // 选项数组（用于select类型）
      //   // 选项格式：{ value: any, label: String, text: String }
      //   // label是i18n键，text是直接显示的文本，优先使用label
      // }
    },
    // 初始筛选值
    initialValues: {
      type: Object,
      default: () => ({})
    },
    // 是否在组件挂载时自动触发搜索
    autoSearch: {
      type: Boolean,
      default: false
    }
  },
  emits: ['search', 'reset', 'update:modelValue', 'select-change'],
  components: {
    Datepicker
  },
  setup(props, { emit }) {
    const store = useStore()
    const { t, locale } = useI18n()
    const { filters, initialValues, autoSearch } = toRefs(props)

    // 筛选值
    const filterValues = reactive({...initialValues.value})

    // 日期范围值（用于Datepicker组件）
    const dateRangeValues = reactive({})

    // 当前语言环境
    const currentLocale = computed(() => {
      return locale.value === 'zh_CN' ? 'zh-CN' : 'en-US'
    })

    // 从store获取暗色模式状态
    const isDarkMode = computed(() => store.getters.isDarkMode)

    // 初始化日期范围值
    const initDateRangeValues = () => {
      filters.value.forEach(filter => {
        if (filter.type === 'dateRange') {
          const startDate = filterValues[filter.startKey]
          const endDate = filterValues[filter.endKey]

          if (startDate && endDate) {
            dateRangeValues[filter.id] = [
              new Date(startDate),
              new Date(endDate)
            ]
          } else {
            dateRangeValues[filter.id] = null
          }
        }
      })
    }

    // 格式化日期显示
    const formatDate = (date) => {
      if (Array.isArray(date)) {
        return date.map(d => formatSingleDate(d)).join(' - ')
      }
      return formatSingleDate(date)
    }

    // 格式化单个日期
    const formatSingleDate = (date) => {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }

    // 格式化预览
    const formatPreview = (date) => {
      return formatDate(date)
    }

    // 处理日期范围变化
    const handleDateRangeChange = (startKey, endKey, filterId) => {
      const dateRange = dateRangeValues[filterId]

      if (dateRange && dateRange.length === 2) {
        filterValues[startKey] = formatSingleDate(dateRange[0])
        filterValues[endKey] = formatSingleDate(dateRange[1])
      } else {
        filterValues[startKey] = ''
        filterValues[endKey] = ''
      }
    }

    // 搜索处理
    const handleSearch = () => {
      // 重置页码为1
      store.dispatch('pagination/setCurrentPage', 1)

      // 过滤掉空值，避免传递空参数
      const filteredValues = {}
      Object.keys(filterValues).forEach(key => {
        if (filterValues[key] !== '' && filterValues[key] !== null && filterValues[key] !== undefined) {
          filteredValues[key] = filterValues[key]
        } else {
          // 确保明确传递空值，以覆盖之前可能存在的值
          filteredValues[key] = ''
        }
      })

      console.log('FilterComponent 发送搜索参数:', filteredValues)

      // 发送搜索事件，包含所有值（包括空值）
      emit('search', filteredValues)

      // 更新组件的v-model值
      emit('update:modelValue', {...filterValues})
    }

    // 重置处理
    const handleReset = () => {
      // 重置所有筛选值
      Object.keys(filterValues).forEach(key => {
        filterValues[key] = ''
      })

      // 重置日期范围值
      Object.keys(dateRangeValues).forEach(key => {
        dateRangeValues[key] = null
      })

      emit('reset', {...filterValues})
      emit('update:modelValue', {...filterValues})
    }

    // 监听初始值变化
    watch(initialValues, (newVal) => {
      Object.keys(newVal).forEach(key => {
        filterValues[key] = newVal[key]
      })

      // 当初始值变化时，重新初始化日期范围值
      initDateRangeValues()
    }, { immediate: true })

    // 初始化日期范围值
    initDateRangeValues()

    // 按钮组激活状态
    const activeButtonIndex = reactive({})

    // 初始化按钮组默认选中状态
    filters.value.forEach(filter => {
      if (filter.type === 'buttonGroup' && filter.defaultActiveIndex !== undefined) {
        activeButtonIndex[filter.id] = filter.defaultActiveIndex

        // 如果有默认选中的按钮，触发其回调
        if (filter.buttons && filter.buttons[filter.defaultActiveIndex] && filter.buttons[filter.defaultActiveIndex].callback) {
          filter.buttons[filter.defaultActiveIndex].callback(filterValues)
        }
      }
    })

    // 组件挂载时，如果设置了自动搜索，则触发搜索
    if (autoSearch.value) {
      setTimeout(() => {
        handleSearch()
      }, 0)
    }

    // 处理按钮组点击
    const handleButtonGroupClick = (filter, button, btnIndex) => {
      // 更新激活状态
      activeButtonIndex[filter.id] = btnIndex

      // 更新筛选值
      if (filter.key) {
        filterValues[filter.key] = button.value
      }

      // 如果有回调函数，执行回调
      if (button.callback) {
        button.callback(filterValues)
      }

      // 如果设置了自动搜索，触发搜索
      if (filter.autoSearch !== false) {
        handleSearch()
      }
    }

    // 下拉选择变化处理
    const handleSelectChange = (key, value) => {
      // 触发选择变化事件
      emit('select-change', { key, value })
    }

    return {
      filterValues,
      dateRangeValues,
      activeButtonIndex,
      currentLocale,
      isDarkMode,
      formatDate,
      formatPreview,
      handleDateRangeChange,
      handleButtonGroupClick,
      handleSearch,
      handleReset,
      handleSelectChange
    }
  }
}
</script>

<style>
@import '@/styles/components/filterComponent.css';
</style>
