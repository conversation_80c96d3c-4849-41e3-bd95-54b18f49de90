<template>
  <div class="agent-rules-page">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">代理规则</h1>
        <div class="page-subtitle">了解我们的代理规则和收益分配方式</div>
      </div>

      <div class="rules-content">
        <div class="rules-section">
          <h2 class="section-title">代理体系介绍</h2>
          <div class="section-content">
            <div class="section-icon">
              <i class="fa fa-users"></i>
            </div>
            <div class="section-text">
              <p>
                我们的代理体系是一个多级裂变的推广体系，通过邀请新用户加入平台，
                您可以获得被邀请用户交易收益的一部分作为奖金。
              </p>
              <p>
                我们的代理体系分为{{ levelList.length }}个级别：{{ getAgentLevelRange() }}，
                不同级别的代理享有不同的直推奖金和团队奖金。
              </p>
            </div>
          </div>
        </div>

        <div class="rules-section">
          <h2 class="section-title">代理级别和权益</h2>
          <div class="agent-levels">
            <div class="agent-level" v-for="level in levelList" :key="level.level">
              <div class="level-header">
                <div class="level-icon">
                  <i :class="getStarIcon(parseInt(level.level))"></i>
                </div>
                <h3 class="level-title">{{ level.level }}星代理</h3>
              </div>
              <div class="level-content">
                <div class="level-item">
                  <div class="item-label">直推奖金</div>
                  <div class="item-value">{{ getCommissionRate(0) }}%</div>
                </div>
                <div class="level-item">
                  <div class="item-label">升级条件</div>
                  <div class="item-value">{{ getUpgradeCondition(level) }}</div>
                </div>
                <div class="level-item" v-if="parseInt(level.level) > 0">
                  <div class="item-label">团队奖励</div>
                  <div class="item-value">
                    <ul class="level-privileges">
                      <li v-for="(rate, index) in getTeamRewardLevels(parseInt(level.level))" :key="index">
                        {{ index + 1 }}级团队奖金比例：{{ rate }}
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="rules-section">
          <h2 class="section-title">奖金计算方式</h2>
          <div class="section-content">
            <div class="section-icon">
              <i class="fa fa-calculator"></i>
            </div>
            <div class="section-text">
              <p>
                奖金计算基于被邀请用户的交易收益，并按照您的代理级别对应的直推奖金和团队奖金计算。<br>
                奖金结算采用实时结算方式，每当被邀请用户产生交易收益时，您的代理账户将立即增加相应的奖金金额。
              </p>
              <p>例如:</p>

              <ul class="promotion-tools">
                <li><strong>0星代理</strong> (直推奖金{{getCommissionRate(0)}}%)，<br>
                  您邀请的1级用户交易收益为100 USDT，<br>
                  那么您将获得(100*{{getCommissionRate(0)}}%)=
                  <strong>{{100*getCommissionRate(0)/100}}</strong>
                  USDT的奖金。
                </li>
                <li><strong>1星代理</strong> (直推奖金{{getCommissionRate(0)}}%，1级团队奖金{{getCommissionRate(1)}}%)，<br>
                  您邀请的1级用户交易收益为100 USDT，<br>
                  那么您将获得(100*({{ getCommissionRate(0) }}%+{{getCommissionRate(1)}}%))=
                  <strong>{{100*(getCommissionRate(0)+getCommissionRate(1))/100}}</strong>
                  USDT的奖金。
                </li>
                <li><strong>2星代理</strong> (直推奖金{{ getCommissionRate(0) }}%，1级团队奖金{{getCommissionRate(1)}}%，2级团队奖金{{getCommissionRate(2)}}%)，<br>
                  您邀请的1级用户交易收益为100 USDT，<br>
                  您邀请的2级用户交易收益为200 USDT，<br>
                  那么您将获得(100*({{ getCommissionRate(0) }}%+{{getCommissionRate(1)}}%) + 200*{{getCommissionRate(2)}}%)=
                  <strong>{{(100*(getCommissionRate(0)+getCommissionRate(1))+200*getCommissionRate(2))/100}}</strong>
                  USDT的奖金。
                </li>
                <li>
                  以此类推。
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div class="rules-section">
          <h2 class="section-title">奖金提现</h2>
          <div class="section-content">
            <div class="section-icon">
              <i class="fa fa-wallet"></i>
            </div>
            <div class="section-text">
              <p>
                您可以在"代理中心"的"概览"页面申请提现您的奖金。
                提现金额最低为1000 USDT，提现手续费为交易所收取，我们平台不收取任何费用，实际到账金额为 提现金额 - 手续费。
              </p>
              <p>
                提现申请提交后，我们将在1-3个工作日内处理您的提现请求，
                并将资金转入您指定的钱包地址。
              </p>
              <p>
                为了确保资金安全，首次提现需要完成身份验证，包括邮箱验证和手机验证。
              </p>
            </div>
          </div>
        </div>

        <div class="rules-section">
          <h2 class="section-title">推广工具</h2>
          <div class="section-content">
            <div class="section-icon">
              <i class="fa fa-tools"></i>
            </div>
            <div class="section-text">
              <p>
                我们为代理提供多种推广工具，帮助您更有效地推广平台：
              </p>
              <ul class="promotion-tools">
                <li>
                  <strong>专属推广链接：</strong> 每个代理都有一个唯一的推广链接，
                  用户通过该链接注册，系统会自动将其标记为您的邀请用户。
                </li>
                <li>
                  <strong>推广海报：</strong> 我们提供多种精美的推广海报，
                  您可以将其分享到社交媒体或发送给潜在用户。
                </li>
                <li>
                  <strong>数据分析：</strong> 我们提供详细的推广数据分析，
                  包括点击量、注册量、转化率等，帮助您优化推广策略。
                </li>
                <li>
                  <strong>推广教程：</strong> 我们提供专业的推广教程，
                  帮助您更好地了解如何有效推广平台。
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div class="rules-section">
          <h2 class="section-title">常见问题</h2>
          <div class="faq-list">
            <div class="faq-item">
              <div class="faq-question">
                <span>如何成为代理？</span>
              </div>
              <div class="faq-answer">
                成为代理非常简单，只需在个人中心的代理中心页面申请成为代理，
                通过审核后即可获得专属推广链接和推广材料，开始您的推广之旅。
              </div>
            </div>

            <div class="faq-item">
              <div class="faq-question">
                <span>什么是有效用户？</span>
              </div>
              <div class="faq-answer">
                有效用户是指通过您的推广链接注册，并至少获取一次收益的用户。
                只有有效用户才会计入您的邀请人数，并提升代理星级。
              </div>
            </div>

            <div class="faq-item">
              <div class="faq-question">
                <span>奖金如何结算？</span>
              </div>
              <div class="faq-answer">
                奖金采用实时结算方式，每当被邀请用户产生交易收益时，
                您的代理账户将立即增加相应的奖金金额。您可以在代理中心查看您的奖金明细。
                <p>注意，可提现收益为邀请用户交收后的收益。</p>
              </div>
            </div>

            <div class="faq-item">
              <div class="faq-question">
                <span>如何提高我的代理星级？</span>
              </div>
              <div class="faq-answer">
                提高代理星级的关键是邀请更多的有效用户。您可以通过社交媒体、论坛、博客等渠道
                分享您的推广链接，吸引更多用户注册。同时，我们也建议您了解平台的功能和优势，
                以便更好地向潜在用户介绍平台。
                <p>注意，当邀请用户产生收益后，记录一个有效用户，第二天0点结算星级。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="rules-cta">
        <h2 class="cta-title">准备好成为我们的代理了吗？</h2>
        <p class="cta-text">
          立即注册，成为我们的代理，享受丰厚的奖金和多种特权。
        </p>
        <div class="cta-buttons">
          <router-link to="/auth/register" class="btn btn-primary btn-lg">
            立即注册
          </router-link>
          <router-link to="/help" class="btn btn-outline-secondary btn-lg">
            返回帮助中心
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AgentRulesPage',
  data() {
    return {
      levelList: []
    }
  },
  mounted() {
    this.fetchLevelData()
  },
  methods: {
    async fetchLevelData() {
      try {
        const response = await this.$store.dispatch('agent/fetchAgentLevelList');
        if (response?.code === 200) {
          this.levelList = response.data
            .sort((a, b) => a.level - b.level)
            .map(item => ({
              ...item,
              radio: parseFloat(item.radio)
            }));
        }
      } catch (error) {
        console.error('获取代理等级数据失败:', error);
      }
    },
    // 直推奖金
    getCommissionRate(star) {
      const level = this.levelList.find(item => item.level === String(star));
      return level ? level.radio : 0;
    },
    // 升星条件
    getUpgradeCondition(level) {
      return level?.invited_users > 0
        ? `有效直推子账户${level.invited_users}个`
        : '-';
    },
    // 团队奖金
    getTeamRewardLevels(star) {
      return this.levelList
        .filter(item => item.level > 0 && item.level <= star)
        .map(item => `${item.radio}%`);
    },
    // 星级图标
    getStarIcon(star) {
      // 根据星级返回不同图标，0星用fa-users，1星及以上用fa-star
      if (star === 0) return 'fa fa-users';
      return 'fa fa-star';
    },
    // 获取代理级别范围
    getAgentLevelRange() {
      if (this.levelList.length === 0) return '';
      const levels = this.levelList.map(item => parseInt(item.level)).sort((a, b) => a - b);
      const minLevel = levels[0];
      const maxLevel = levels[levels.length - 1];
      return `${minLevel}-${maxLevel}星`;
    },
  },
}
</script>

<style scoped>
@import '@/styles/views/help/agentRules.css';
</style>
