.funding-rate-history-container {
  padding: 20px;
}

.funding-rate-history-header {
  margin-bottom: 20px;
}

.funding-rate-history-header h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
}

.binance-link {
  font-size: 14px;
  margin-left: 12px;
  color: var(--primary-color);
  text-decoration: none;
  padding: 4px 8px;
  border: 1px solid var(--primary-color);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.binance-link:hover {
  background-color: var(--primary-color);
  color: white;
}

.positive-rate {
  color: var(--success-color);
}

.negative-rate {
  color: var(--error-color);
}

.chart-container {
  margin: 20px 0;
  padding: 15px;
  background-color: var(--bg-card);
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow);
  height: 400px;
  transition: background-color var(--transition-normal),
              box-shadow var(--transition-normal),
              border-color var(--transition-normal);
}

.chart-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 15px;
  color: var(--text-primary);
  transition: color var(--transition-normal);
}

.no-data-chart {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: var(--text-tertiary);
  font-size: 16px;
}

/* 暗色模式下的图表容器样式 */
.dark-mode .chart-container {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
}

.dark-mode .chart-title {
  color: var(--text-primary);
}

.dark-mode .no-data-chart {
  color: var(--text-tertiary);
}
