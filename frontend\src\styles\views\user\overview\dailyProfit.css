.daily-profit-content {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: var(--card-shadow);
}



/* 收益状态样式 */
.profit-positive {
  color: var(--success-color) !important;
}

.profit-negative {
  color: var(--error-color) !important;
}

.trend-up {
  color: var(--success-color);
  font-weight: 600;
}

.trend-down {
  color: var(--error-color);
  font-weight: 600;
}

.trend-up i,
.trend-down i {
  margin-right: 4px;
}

/* 表格样式增强 */
.daily-profit-content .table-container {
  margin-top: 20px;
}

.daily-profit-content .table th {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  color: var(--text-primary);
  font-weight: 600;
  border-bottom: 2px solid var(--primary-color);
}

.daily-profit-content .table td {
  vertical-align: middle;
  padding: 12px 15px;
}

/* 卡片视图样式 */
.daily-profit-content .cards-container .card {
  border: 1px solid rgba(var(--border-color-rgb), 0.1);
  border-radius: 8px;
  transition: all var(--transition-normal);
}

.daily-profit-content .cards-container .card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .daily-profit-content {
    padding: 15px;
  }
}

/* 筛选组件样式调整 */
.daily-profit-content .filter-component {
  margin-bottom: 25px;
}

/* 分页组件样式调整 */
.daily-profit-content .pagination-container {
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid rgba(var(--border-color-rgb), 0.1);
}
