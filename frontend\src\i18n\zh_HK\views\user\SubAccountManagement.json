﻿{
    "SubAccountManagement":  {
                                 "accountAssets":  "账户资产",
                                 "addApiKey":  "添加API密钥",
                                 "agreeTerms":  "我同意",
                                 "agreeTermsError":  "请同意API使用条款",
                                 "all":  "全部",
                                 "apiTerms":  "API使用条款",
                                 "apiTermsContent":  "使用API密钥需遵守相关条款和条件，确保API密钥的安全性。",
                                 "apikey":  "API Key",
                                 "apikeyAdded":  "API密钥已添加",
                                 "apikeyUpdated":  "API密钥已更新",
                                 "cancel":  "取消",
                                 "completionTime":  "完成时间",
                                 "editApiKey":  "编辑API密钥",
                                 "error":  "錯誤",
                                 "getSubAccountError":  "获取子账号详情失败",
                                 "loading":  "載入中...",
                                 "noData":  "暂无子账号数据",
                                 "operation":  "操作",
                                 "parentAccount":  "母账号",
                                 "profitDetails":  "盈利明細",
                                 "save":  "保存",
                                 "saveApiKeyError":  "保存API密钥失败",
                                 "secretkey":  "Secret Key",
                                 "selectAccount":  "选择账号",
                                 "statistics":  "子账号统计",
                                 "status":  "狀態",
                                 "status_disabled":  "已停用",
                                 "status_enabled":  "已启用",
                                 "status_expired":  "已失效",
                                 "status_inactive":  "未启用",
                                 "subAccount":  "子账号",
                                 "subAccountCount":  "子账号数量",
                                 "subtitle":  "管理您的子账号",
                                 "success":  "成功",
                                 "taskStatus_completed":  "已完成",
                                 "taskStatus_notRunning":  "未运行",
                                 "taskStatus_running":  "执行中",
                                 "task_status":  "任务狀態",
                                 "title":  "子帳戶管理",
                                 "totalAssets":  "总资产",
                                 "totalProfit":  "总收益",
                                 "uncollectedFunds":  "未归集资金",
                                 "apikeyError":  "请输入API Key",
                                 "secretkeyError":  "请输入Secret Key"
                             }
}
