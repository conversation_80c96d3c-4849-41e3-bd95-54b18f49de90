.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.stats-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: center;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  color: var(--text-light);
  font-size: 1.5rem;
  flex-shrink: 0;
  box-shadow: 0 5px 15px rgba(240, 185, 11, 0.3);
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stats-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: var(--spacing-xs);
}

.stats-trend {
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.trend-up {
  color: var(--success-color);
}

.trend-down {
  color: var(--error-color);
}

.stats-info {
  font-size: 0.85rem;
  color: var(--text-tertiary);
}

.agent-sections {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
}

.agent-section {
  margin-bottom: var(--spacing-xl);
}

.section-title {
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  position: relative;
  padding-bottom: var(--spacing-xs);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  border-radius: 1.5px;
}

.referral-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
}

.referral-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.referral-link {
  margin-bottom: var(--spacing-md);
}

.link-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.link-value {
  display: flex;
  gap: var(--spacing-sm);
}

.link-value input {
  flex: 1;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background-color: var(--bg-input);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.referral-qr {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.qr-code {
  width: 50%;
  height: 50%;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.qr-code canvas {
  max-width: 100%;
  max-height: 100%;
}

.qr-actions {
  flex: 1;
  display: flex;
  flex-direction: row;
  /* 修改为行方向 */
  gap: var(--spacing-md);
  /* 调整间距 */
  justify-content: center;
  /* 居中对齐 */
}

.qr-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.qr-overlay-content {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 0 25px rgba(0, 0, 0, 0.5);
  cursor: default;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.qr-overlay-content canvas {
  width: 300px !important;
  height: 300px !important;
  display: block;
}

.chart-container {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  height: 300px;
}

.recent-invites-table-wrapper,
.recent-commissions-table-wrapper {
  overflow-x: auto;
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  margin-bottom: var(--spacing-md);
}

.recent-invites-table,
.recent-commissions-table {
  width: 100%;
  border-collapse: collapse;
}

.recent-invites-table th,
.recent-invites-table td,
.recent-commissions-table th,
.recent-commissions-table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.recent-invites-table th,
.recent-commissions-table th {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
}

.recent-invites-table tr:last-child td,
.recent-commissions-table tr:last-child td {
  border-bottom: none;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
}

.status-active,
.status-settled {
  background-color: var(--success-color-light);
  color: var(--success-color);
}

.status-inactive,
.status-pending {
  background-color: var(--warning-color-light);
  color: var(--warning-color);
}

.no-data {
  text-align: center;
  padding: var(--spacing-xl) !important;
}

.no-data-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
}

.no-data-content i {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
}

.no-data-content p {
  margin-bottom: var(--spacing-md);
}

.table-footer {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-md);
}

@media (max-width: 1199px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 991px) {
  .agent-sections {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 767px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }

  .referral-qr {
    flex-direction: column;
    align-items: center;
  }

  .qr-actions {
    width: 100%;
  }
}