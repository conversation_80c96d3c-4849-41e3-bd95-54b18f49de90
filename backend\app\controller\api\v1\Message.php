<?php
declare(strict_types=1);

namespace app\controller\api\v1;

use app\controller\api\BaseController;
use think\Response;
use think\facade\Db;

/**
 * 消息控制器
 */
class Message extends BaseController
{
    /**
     * 获取消息列表
     */
    public function index(): Response
    {
        // 获取当前登录用户ID
        $userId = request()->user['id'];
        
        // 获取分页参数
        $page = input('page/d', 1);
        $limit = input('limit/d', 10);
        $type = input('type');
        $status = input('status');
        
        // 构建查询条件
        $where = [
            'um.user_id' => $userId
        ];
        
        if ($type !== '' && $type !== null) {
            $where['m.type'] = $type;
        }
        if ($status !== ''&& $status !== null) {
            $where['um.status'] = $status;
        }
        
        // 查询消息列表
        $messages = Db::name('user_messages')
            ->alias('um')
            ->leftJoin('messages m', 'm.id = um.message_id')
            ->where($where)
            ->field('um.id, m.title, m.content, m.type, m.urgency, m.action_name, m.action_url, m.created_at, um.status')
            ->page($page, $limit)
            ->order('um.status')
            ->order('m.created_at', 'desc')
            ->select()
            ->toArray();
        
        // 获取总数
        $total = Db::name('user_messages')
            ->alias('um')
            ->leftJoin('messages m', 'm.id = um.message_id')
            ->where($where)
            ->count();
        // 查询未读消息数量
        $unread_count = Db::name('user_messages')
            ->where('user_id', $userId)
            ->where('status', 0)
            ->count();
        return $this->success([
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'unread_count' => $unread_count,
            'messages' => $messages
        ], '获取消息列表成功');
    }
    
    /**
     * 标记消息为已读
     */
    public function markAsRead($id): Response
    {
        // 获取当前登录用户ID
        $userId = request()->user['id'];
        
        // 查询消息是否存在
        $message = Db::name('user_messages')
            ->where('id', $id)
            ->where('user_id', $userId)
            ->find();
        
        if (empty($message)) {
            return $this->error('消息不存在');
        }
        
        // 更新消息状态为已读
        $result = Db::name('user_messages')
            ->where('user_id', $userId)
            ->where('id', $id)
            ->update([
                'status' => 1,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        
        if ($result === false) {
            return $this->error('标记消息为已读失败');
        }
        
        return $this->success([
            'message' => [
                'id' => $id,
                'status' => 1,
            ]
        ], '消息已标记为已读');
    }
    
    /**
     * 标记所有消息为已读
     */
    public function markAllAsRead(): Response
    {
        // 获取当前登录用户ID
        $userId = request()->user['id'];
        
        // 更新所有消息状态为已读
        $result = Db::name('user_messages')
            ->where('user_id', $userId)
            ->where('status', 0)
            ->update([
                'status' => 1,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        
        if ($result === false) {
            return $this->error('标记所有消息为已读失败');
        }
        
        return $this->success([], '所有消息已标记为已读');
    }
    
    /**
     * 获取消息详情
     */
    public function read($id): Response
    {
        // 获取当前登录用户ID
        $userId = request()->user['id'];
        
        // 查询消息是否存在
        $message = Db::name('user_messages')
            ->alias('um')
            ->join('messages m', 'm.id = um.message_id')
            ->where('um.id', $id)
            ->where('um.user_id', $userId)
            ->field('um.id, m.title, m.content, m.type, m.urgency, m.action_name, m.action_url, m.created_at, um.status')
            ->find();
        
        if (empty($message)) {
            return $this->error('消息不存在');
        }
        if($message['status'] == 0){
            // 更新消息状态为已读
            Db::name('user_messages')
                ->where('user_id', $userId)
                ->where('id', $id)
                ->update([
                    'status' => 1,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            
            // 处理消息数据
            $message['status'] = 1;
        }
        
        return $this->success($message, '获取消息详情成功');
    }
}
