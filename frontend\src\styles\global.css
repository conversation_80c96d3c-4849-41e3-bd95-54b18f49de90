:root {
  /* Colors */
  --primary-color: #F0B90B;
  --primary-color-light: #F8D33A;
  --primary-color-dark: #E0A800;
  --secondary-color: #1E2329;
  --secondary-color-light: #2B3139;
  --secondary-color-dark: #14171C;
  --success-color: #02C076;
  --warning-color: #F0B90B;
  --error-color: #F6465D;
  --info-color: #0ECAF0;
  
  /* Text colors */
  --text-primary: #1E2329;
  --text-secondary: #474D57;
  --text-tertiary: #707A8A;
  --text-quaternary: #B7BDC6;
  --text-light: #FFFFFF;
  --text-link: #0ECAF0;
  
  /* Background colors */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F5F5F5;
  --bg-tertiary: #EAECEF;
  --bg-card: #FFFFFF;
  --bg-modal: #FFFFFF;
  --bg-tooltip: #1E2329;
  
  /* Border colors */
  --border-color: #E6E8EA;
  --border-color-light: #F0F1F2;
  --border-color-dark: #CFD6E4;
  
  /* Shadow */
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  --box-shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.12);
  --box-shadow-dropdown: 0 8px 16px rgba(0, 0, 0, 0.16);
  --box-shadow-modal: 0 12px 24px rgba(0, 0, 0, 0.2);
  
  /* Typography */
  --font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-md: 1rem;       /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-xxl: 1.5rem;    /* 24px */
  --font-size-xxxl: 2rem;     /* 32px */
  
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-loose: 1.8;
  
  /* Spacing */
  --spacing-xs: 0.25rem;      /* 4px */
  --spacing-sm: 0.5rem;       /* 8px */
  --spacing-md: 1rem;         /* 16px */
  --spacing-lg: 1.5rem;       /* 24px */
  --spacing-xl: 2rem;         /* 32px */
  --spacing-xxl: 3rem;        /* 48px */
  
  /* Border radius */
  --border-radius-sm: 0.25rem;    /* 4px */
  --border-radius-md: 0.5rem;     /* 8px */
  --border-radius-lg: 0.75rem;    /* 12px */
  --border-radius-xl: 1rem;       /* 16px */
  --border-radius-circle: 50%;
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Z-index */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  
  /* Container widths */
  --container-sm: 540px;
  --container-md: 720px;
  --container-lg: 960px;
  --container-xl: 1140px;
  --container-xxl: 1320px;
}

/* Dark mode variables */
.dark-mode {
  --primary-color: #F0B90B;
  --primary-color-light: #F8D33A;
  --primary-color-dark: #E0A800;
  --secondary-color: #1E2329;
  --secondary-color-light: #2B3139;
  --secondary-color-dark: #14171C;
  
  --text-primary: #FFFFFF;
  --text-secondary: #B7BDC6;
  --text-tertiary: #848E9C;
  --text-quaternary: #5E6673;
  --text-light: #FFFFFF;
  --text-link: #0ECAF0;
  
  --bg-primary: #0B0E11;
  --bg-secondary: #1E2329;
  --bg-tertiary: #2B3139;
  --bg-card: #1E2329;
  --bg-modal: #1E2329;
  --bg-tooltip: #474D57;
  
  --border-color: #2B3139;
  --border-color-light: #2B3139;
  --border-color-dark: #474D57;
  
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  --box-shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.3);
  --box-shadow-dropdown: 0 8px 16px rgba(0, 0, 0, 0.4);
  --box-shadow-modal: 0 12px 24px rgba(0, 0, 0, 0.5);
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

a {
  color: var(--text-link);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-color);
}

button {
  cursor: pointer;
  font-family: var(--font-family);
}

img {
  max-width: 100%;
  height: auto;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

h1 {
  font-size: var(--font-size-xxxl);
}

h2 {
  font-size: var(--font-size-xxl);
}

h3 {
  font-size: var(--font-size-xl);
}

h4 {
  font-size: var(--font-size-lg);
}

h5 {
  font-size: var(--font-size-md);
}

h6 {
  font-size: var(--font-size-sm);
}

p {
  margin-bottom: var(--spacing-md);
}

small {
  font-size: var(--font-size-xs);
}

/* Container */
.container {
  width: 100%;
  padding-right: var(--spacing-md);
  padding-left: var(--spacing-md);
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container {
    max-width: var(--container-sm);
  }
}

@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}

@media (min-width: 992px) {
  .container {
    max-width: var(--container-lg);
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: var(--container-xl);
  }
}

@media (min-width: 1400px) {
  .container {
    max-width: var(--container-xxl);
  }
}

.container-fluid {
  width: 100%;
  padding-right: var(--spacing-md);
  padding-left: var(--spacing-md);
  margin-right: auto;
  margin-left: auto;
}

/* Grid system */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: calc(var(--spacing-md) * -1);
  margin-left: calc(var(--spacing-md) * -1);
}

.col {
  flex: 1 0 0%;
  padding-right: var(--spacing-md);
  padding-left: var(--spacing-md);
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
  padding-right: var(--spacing-md);
  padding-left: var(--spacing-md);
}

/* Columns for different screen sizes */
@media (min-width: 576px) {
  .col-sm {
    flex: 1 0 0%;
  }
  
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
  }
}

@media (min-width: 768px) {
  .col-md {
    flex: 1 0 0%;
  }
  
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
  }
}

@media (min-width: 992px) {
  .col-lg {
    flex: 1 0 0%;
  }
  
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
  }
}

@media (min-width: 1200px) {
  .col-xl {
    flex: 1 0 0%;
  }
  
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
  }
}

/* Generate column classes */
.col-1 { flex: 0 0 auto; width: 8.33333333%; }
.col-2 { flex: 0 0 auto; width: 16.66666667%; }
.col-3 { flex: 0 0 auto; width: 25%; }
.col-4 { flex: 0 0 auto; width: 33.33333333%; }
.col-5 { flex: 0 0 auto; width: 41.66666667%; }
.col-6 { flex: 0 0 auto; width: 50%; }
.col-7 { flex: 0 0 auto; width: 58.33333333%; }
.col-8 { flex: 0 0 auto; width: 66.66666667%; }
.col-9 { flex: 0 0 auto; width: 75%; }
.col-10 { flex: 0 0 auto; width: 83.33333333%; }
.col-11 { flex: 0 0 auto; width: 91.66666667%; }
.col-12 { flex: 0 0 auto; width: 100%; }

@media (min-width: 576px) {
  .col-sm-1 { flex: 0 0 auto; width: 8.33333333%; }
  .col-sm-2 { flex: 0 0 auto; width: 16.66666667%; }
  .col-sm-3 { flex: 0 0 auto; width: 25%; }
  .col-sm-4 { flex: 0 0 auto; width: 33.33333333%; }
  .col-sm-5 { flex: 0 0 auto; width: 41.66666667%; }
  .col-sm-6 { flex: 0 0 auto; width: 50%; }
  .col-sm-7 { flex: 0 0 auto; width: 58.33333333%; }
  .col-sm-8 { flex: 0 0 auto; width: 66.66666667%; }
  .col-sm-9 { flex: 0 0 auto; width: 75%; }
  .col-sm-10 { flex: 0 0 auto; width: 83.33333333%; }
  .col-sm-11 { flex: 0 0 auto; width: 91.66666667%; }
  .col-sm-12 { flex: 0 0 auto; width: 100%; }
}

@media (min-width: 768px) {
  .col-md-1 { flex: 0 0 auto; width: 8.33333333%; }
  .col-md-2 { flex: 0 0 auto; width: 16.66666667%; }
  .col-md-3 { flex: 0 0 auto; width: 25%; }
  .col-md-4 { flex: 0 0 auto; width: 33.33333333%; }
  .col-md-5 { flex: 0 0 auto; width: 41.66666667%; }
  .col-md-6 { flex: 0 0 auto; width: 50%; }
  .col-md-7 { flex: 0 0 auto; width: 58.33333333%; }
  .col-md-8 { flex: 0 0 auto; width: 66.66666667%; }
  .col-md-9 { flex: 0 0 auto; width: 75%; }
  .col-md-10 { flex: 0 0 auto; width: 83.33333333%; }
  .col-md-11 { flex: 0 0 auto; width: 91.66666667%; }
  .col-md-12 { flex: 0 0 auto; width: 100%; }
}

@media (min-width: 992px) {
  .col-lg-1 { flex: 0 0 auto; width: 8.33333333%; }
  .col-lg-2 { flex: 0 0 auto; width: 16.66666667%; }
  .col-lg-3 { flex: 0 0 auto; width: 25%; }
  .col-lg-4 { flex: 0 0 auto; width: 33.33333333%; }
  .col-lg-5 { flex: 0 0 auto; width: 41.66666667%; }
  .col-lg-6 { flex: 0 0 auto; width: 50%; }
  .col-lg-7 { flex: 0 0 auto; width: 58.33333333%; }
  .col-lg-8 { flex: 0 0 auto; width: 66.66666667%; }
  .col-lg-9 { flex: 0 0 auto; width: 75%; }
  .col-lg-10 { flex: 0 0 auto; width: 83.33333333%; }
  .col-lg-11 { flex: 0 0 auto; width: 91.66666667%; }
  .col-lg-12 { flex: 0 0 auto; width: 100%; }
}

@media (min-width: 1200px) {
  .col-xl-1 { flex: 0 0 auto; width: 8.33333333%; }
  .col-xl-2 { flex: 0 0 auto; width: 16.66666667%; }
  .col-xl-3 { flex: 0 0 auto; width: 25%; }
  .col-xl-4 { flex: 0 0 auto; width: 33.33333333%; }
  .col-xl-5 { flex: 0 0 auto; width: 41.66666667%; }
  .col-xl-6 { flex: 0 0 auto; width: 50%; }
  .col-xl-7 { flex: 0 0 auto; width: 58.33333333%; }
  .col-xl-8 { flex: 0 0 auto; width: 66.66666667%; }
  .col-xl-9 { flex: 0 0 auto; width: 75%; }
  .col-xl-10 { flex: 0 0 auto; width: 83.33333333%; }
  .col-xl-11 { flex: 0 0 auto; width: 91.66666667%; }
  .col-xl-12 { flex: 0 0 auto; width: 100%; }
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-medium);
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.5rem 1rem;
  font-size: var(--font-size-md);
  line-height: 1.5;
  border-radius: var(--border-radius-md);
  transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.btn:hover {
  text-decoration: none;
}

.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(240, 185, 11, 0.25);
}

.btn:disabled {
  opacity: 0.65;
  pointer-events: none;
}

.btn-primary {
  color: var(--text-light);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  color: var(--text-light);
  background-color: var(--primary-color-dark);
  border-color: var(--primary-color-dark);
}

.btn-secondary {
  color: var(--text-light);
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-secondary:hover {
  color: var(--text-light);
  background-color: var(--secondary-color-dark);
  border-color: var(--secondary-color-dark);
}

.btn-success {
  color: var(--text-light);
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-success:hover {
  color: var(--text-light);
  background-color: #01a865;
  border-color: #01a865;
}

.btn-warning {
  color: var(--text-primary);
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.btn-warning:hover {
  color: var(--text-primary);
  background-color: var(--primary-color-dark);
  border-color: var(--primary-color-dark);
}

.btn-danger {
  color: var(--text-light);
  background-color: var(--error-color);
  border-color: var(--error-color);
}

.btn-danger:hover {
  color: var(--text-light);
  background-color: #e03e53;
  border-color: #e03e53;
}

.btn-info {
  color: var(--text-light);
  background-color: var(--info-color);
  border-color: var(--info-color);
}

.btn-info:hover {
  color: var(--text-light);
  background-color: #0bb8d9;
  border-color: #0bb8d9;
}

.btn-light {
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  border-color: var(--bg-secondary);
}

.btn-light:hover {
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
  border-color: var(--bg-tertiary);
}

.btn-dark {
  color: var(--text-light);
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-dark:hover {
  color: var(--text-light);
  background-color: var(--secondary-color-dark);
  border-color: var(--secondary-color-dark);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background-color: transparent;
}

.btn-outline-primary:hover {
  color: var(--text-light);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-secondary {
  color: var(--text-secondary);
  background-color: transparent;
  border-color: var(--border-color);
}

.btn-outline-secondary:hover {
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  border-color: var(--border-color-dark);
}

.btn-link {
  font-weight: var(--font-weight-regular);
  color: var(--text-link);
  text-decoration: none;
  background-color: transparent;
  border: none;
}

.btn-link:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  margin: 0 5px;
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius-sm);
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: var(--font-size-lg);
  border-radius: var(--border-radius-lg);
}

.btn-block {
  display: block;
  width: 100%;
}

/* Forms */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: inline-block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-regular);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(240, 185, 11, 0.25);
}

.form-control::placeholder {
  color: var(--text-tertiary);
  opacity: 1;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: var(--bg-secondary);
  opacity: 1;
}

.form-text {
  margin-top: 0.25rem;
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

.form-check {
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5rem;
  margin-bottom: 0.125rem;
}

.form-check-input {
  position: absolute;
  margin-top: 0.25rem;
  margin-left: -1.5rem;
}

.form-check-label {
  margin-bottom: 0;
}

.form-select {
  display: block;
  width: 100%;
  padding: 0.5rem 2.25rem 0.5rem 0.75rem;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-regular);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  appearance: none;
}

.form-select:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(240, 185, 11, 0.25);
}

/* Alerts */
.alert {
  position: relative;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
}

.alert-primary {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeeba;
}

.alert-secondary {
  color: #383d41;
  background-color: #e2e3e5;
  border-color: #d6d8db;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.alert-warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeeba;
}

.alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

.alert-light {
  color: #818182;
  background-color: #fefefe;
  border-color: #fdfdfe;
}

.alert-dark {
  color: #1b1e21;
  background-color: #d6d8d9;
  border-color: #c6c8ca;
}

/* Badges */
.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: var(--font-weight-bold);
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--border-radius-sm);
}

.badge-primary {
  color: var(--text-light);
  background-color: var(--primary-color);
}

.badge-secondary {
  color: var(--text-light);
  background-color: var(--secondary-color);
}

.badge-success {
  color: var(--text-light);
  background-color: var(--success-color);
}

.badge-danger {
  color: var(--text-light);
  background-color: var(--error-color);
}

.badge-warning {
  color: var(--text-primary);
  background-color: var(--warning-color);
}

.badge-info {
  color: var(--text-light);
  background-color: var(--info-color);
}

/* Utilities */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--error-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-light { color: var(--text-light) !important; }
.text-dark { color: var(--text-primary) !important; }
.text-muted { color: var(--text-tertiary) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-danger { background-color: var(--error-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-light { background-color: var(--bg-secondary) !important; }
.bg-dark { background-color: var(--secondary-color) !important; }

.border { border: 1px solid var(--border-color) !important; }
.border-top { border-top: 1px solid var(--border-color) !important; }
.border-right { border-right: 1px solid var(--border-color) !important; }
.border-bottom { border-bottom: 1px solid var(--border-color) !important; }
.border-left { border-left: 1px solid var(--border-color) !important; }
/* 默认情况下，desktop-only 类在所有设备上显示 */
.desktop-only {
  display: block;
}

/* 在移动设备上隐藏 desktop-only 类的元素 */
@media (max-width: 991px) {
  .desktop-only {
    display: none;
  }
}