<template>
  <div class="table-wrapper">
    <!-- 自定义Tooltip -->
    <div v-if="showTooltip" class="column-tooltip" :style="tooltipStyle">
      {{ tooltipContent }}
    </div>

    <table class="data-table">
      <thead>
        <tr>
          <th v-for="column in columns" :key="column.key">
            <div class="th-content">
              {{ column.title }}
              <span
                v-if="column.description"
                class="column-info-icon"
                @mouseenter="showColumnTooltip($event, column.description)"
                @mouseleave="handleTooltipMouseLeave"
                @click="toggleTooltipLock($event, column.description)"
              >
                <i class="fa fa-question-circle"></i>
              </span>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <template v-for="(item, index) in data" :key="item.id || index">
          <!-- 渲染顶层行 -->
          <tr>
            <template v-for="(column, colIndex) in columns" :key="column.key">
              <td :style="{ textAlign: column.align || 'center' }">
                <!-- 第一列添加展开/折叠图标 -->
                <div class="cell-content" :class="{ 'tree-cell': colIndex === 0 && isTree }">
                  <div v-if="colIndex === 0 && isTree" class="tree-indent" :style="{ paddingLeft: `${(item.level || 0) * 5}px` }">
                    <!-- 展开/折叠图标，仅当有子项时显示 -->
                    <span
                      v-if="canExpand(item)"
                      class="tree-expand-icon"
                      @click="toggleExpand(item)"
                    >
                      <i :class="isExpanded(item) ? 'fa fa-caret-down' : 'fa fa-caret-right'"></i>
                    </span>
                    <span v-else-if="hasChildren(item)" class="tree-expand-icon disabled">
                      <i class="fa fa-caret-right"></i>
                    </span>
                    <span v-else class="tree-placeholder"></span>
                  </div>
                  <!-- 使用具名插槽允许自定义单元格内容 -->
                  <slot :name="column.key" :item="item" :column="column">
                    {{ item[column.key] }}
                  </slot>
                </div>
              </td>
            </template>
          </tr>
          <!-- 递归渲染子项 -->
          <template v-if="isTree && item.children && isExpanded(item)">
            <template v-for="(child, childIndex) in item.children" :key="`${item.id}-${childIndex}`">
              <!-- 使用递归方法渲染子项及其所有后代 -->
              <template v-for="row in renderTreeRows(child, `${item.id}-${childIndex}`)" :key="row.key">
                <tr>
                  <template v-for="(column, colIndex) in columns" :key="`${column.key}-${row.key}`">
                    <td :style="{ textAlign: column.align || 'center' }">
                      <div class="cell-content" :class="{ 'tree-cell': colIndex === 0 }">
                        <div v-if="colIndex === 0" class="tree-indent" :style="{ paddingLeft: `${(row.item.level || 0) * 5}px` }">
                          <!-- 展开/折叠图标，仅当有子项时显示 -->
                          <span
                            v-if="canExpand(row.item)"
                            class="tree-expand-icon"
                            @click="toggleExpand(row.item)"
                          >
                            <i :class="isExpanded(row.item) ? 'fa fa-caret-down' : 'fa fa-caret-right'"></i>
                          </span>
                          <span v-else-if="hasChildren(row.item)" class="tree-expand-icon disabled">
                            <i class="fa fa-caret-right"></i>
                          </span>
                          <span v-else class="tree-placeholder"></span>
                        </div>
                        <!-- 使用具名插槽允许自定义单元格内容 -->
                        <slot :name="column.key" :item="row.item" :column="column">
                          {{ row.item[column.key] }}
                        </slot>
                      </div>
                    </td>
                  </template>
                </tr>
              </template>
            </template>
          </template>
        </template>
        <tr v-if="data.length === 0">
          <td :colspan="columns.length" class="no-data">
            <div class="no-data-content">
              <i class="fa fa-info-circle"></i>
              <p>{{ noDataText }}</p>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import { defineComponent, ref, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'

export default defineComponent({
  name: 'DataTable',
  props: {
    // 表格列配置
    columns: {
      type: Array,
      required: true,
      // 每列的格式: { key: 'fieldName', title: '列标题', align: '对齐方式(left/center/right)', description: '列描述' }
      // align参数可选，默认为'center'
      // description参数可选，当提供时会在列标题旁显示问号图标，鼠标悬停时显示描述文本
    },
    // 表格数据
    data: {
      type: Array,
      default: () => []
    },
    // 无数据时显示的文本
    noDataText: {
      type: String,
      default: ''
    },
    // 是否启用树状表格
    isTree: {
      type: Boolean,
      default: false
    },
    // 默认展开的行ID
    defaultExpandedIds: {
      type: Array,
      default: () => []
    }
  },
  emits: ['expand-change'],
  setup(props, { emit }) {
    const { t } = useI18n()

    // 存储展开状态的行ID
    const expandedIds = ref([...props.defaultExpandedIds])

    // Tooltip相关状态
    const showTooltip = ref(false)
    const tooltipContent = ref('')
    const tooltipStyle = ref({
      top: '0px',
      left: '0px',
      '--arrow-left': '50%' // 箭头位置，默认居中
    })
    const tooltipLocked = ref(false) // 是否锁定Tooltip（通过点击）
    const activeTooltipIcon = ref(null) // 当前激活的问号图标元素

    // 判断行是否有子项
    const hasChildren = (row) => {
      // 检查hasChildren属性或children数组
      return row.hasChildren || (row.children && row.children.length > 0)
    }

    // 判断行是否可以展开
    const canExpand = (row) => {
      // 优先使用canExpand属性，如果没有则使用hasChildren
      return row.canExpand !== undefined ? row.canExpand : hasChildren(row)
    }

    // 判断行是否展开
    const isExpanded = (row) => {
      return expandedIds.value.includes(row.id)
    }

    // 切换行的展开/折叠状态
    const toggleExpand = (row) => {
      const index = expandedIds.value.indexOf(row.id)
      if (index > -1) {
        // 已展开，则折叠
        expandedIds.value.splice(index, 1)
      } else {
        // 未展开，则展开
        expandedIds.value.push(row.id)
      }

      // 触发展开状态变化事件
      emit('expand-change', {
        row,
        expanded: index === -1,
        expandedIds: [...expandedIds.value]
      })
    }

    /**
     * 递归渲染树状表格行
     * @param {Object} item - 当前行数据
     * @param {String} parentKey - 父行的唯一标识
     * @returns {Array} - 包含当前行及其所有子行的数组
     */
    const renderTreeRows = (item, parentKey) => {
      // 创建结果数组，首先添加当前行
      const rows = [{ key: parentKey, item }]

      // 如果当前行有子项且已展开，则递归渲染子项
      if (item.children && isExpanded(item)) {
        item.children.forEach((child, index) => {
          // 为每个子项生成唯一的key
          const childKey = `${parentKey}-${index}`
          // 递归获取子项及其后代
          const childRows = renderTreeRows(child, childKey)
          // 将子项及其后代添加到结果数组
          rows.push(...childRows)
        })
      }

      return rows
    }

    // 显示列描述Tooltip
    const showColumnTooltip = (event, content) => {
      // 如果Tooltip已锁定且不是当前图标触发的，则不显示
      if (tooltipLocked.value && event.currentTarget !== activeTooltipIcon.value) {
        return
      }

      tooltipContent.value = content
      activeTooltipIcon.value = event.currentTarget

      // 计算Tooltip位置，避免被窗口边缘遮挡
      const rect = event.target.getBoundingClientRect()
      const tooltipWidth = 200 // 预估的Tooltip宽度
      const tooltipHeight = 80 // 预估的Tooltip高度

      // 默认显示在图标下方
      let top = rect.bottom + window.scrollY + 5
      let left = rect.left + window.scrollX - tooltipWidth / 2 + rect.width / 2

      // 检查是否会超出右边界
      if (left + tooltipWidth > window.innerWidth) {
        left = window.innerWidth - tooltipWidth - 10
      }

      // 检查是否会超出左边界
      if (left < 10) {
        left = 10
      }

      // 检查是否会超出下边界，如果会则显示在图标上方
      if (top + tooltipHeight > window.innerHeight + window.scrollY) {
        top = rect.top + window.scrollY - tooltipHeight - 5
      }

      // 计算箭头位置：箭头应该指向问号图标的中心
      // 计算问号图标中心相对于tooltip左边缘的位置
      const iconCenterX = rect.left + rect.width / 2
      const tooltipLeft = left
      const arrowLeftPosition = `${iconCenterX - tooltipLeft}px`

      tooltipStyle.value = {
        top: `${top}px`,
        left: `${left}px`,
        '--arrow-left': arrowLeftPosition, // 设置箭头位置
        '--tooltip-pointer-events': tooltipLocked.value ? 'auto' : 'none' // 锁定时可接收鼠标事件
      }

      showTooltip.value = true
    }

    // 处理鼠标离开Tooltip图标
    const handleTooltipMouseLeave = () => {
      // 如果Tooltip未锁定，则隐藏
      if (!tooltipLocked.value) {
        hideTooltip()
      }
    }

    // 隐藏Tooltip
    const hideTooltip = () => {
      showTooltip.value = false
    }

    // 切换Tooltip锁定状态
    const toggleTooltipLock = (event, content) => {
      event.stopPropagation() // 阻止事件冒泡

      // 如果点击的是当前激活的图标，则切换锁定状态
      if (activeTooltipIcon.value === event.currentTarget) {
        tooltipLocked.value = !tooltipLocked.value
      } else {
        // 如果点击的是其他图标，则锁定新的Tooltip
        tooltipLocked.value = true
        showColumnTooltip(event, content)
      }

      // 更新Tooltip样式，锁定时可接收鼠标事件
      tooltipStyle.value = {
        ...tooltipStyle.value,
        '--tooltip-pointer-events': tooltipLocked.value ? 'auto' : 'none'
      }

      // 如果锁定了Tooltip，添加文档点击事件监听器
      if (tooltipLocked.value) {
        // 使用setTimeout确保事件监听器在当前点击事件处理完后添加
        setTimeout(() => {
          document.addEventListener('click', handleDocumentClick)
        }, 0)
      }
    }

    // 处理文档点击事件
    const handleDocumentClick = (event) => {
      // 如果点击的不是Tooltip或问号图标，则解锁并隐藏Tooltip
      const tooltip = document.querySelector('.column-tooltip')
      const isTooltipClick = tooltip && tooltip.contains(event.target)
      const isIconClick = activeTooltipIcon.value && activeTooltipIcon.value.contains(event.target)

      if (!isTooltipClick && !isIconClick) {
        tooltipLocked.value = false
        hideTooltip()
        document.removeEventListener('click', handleDocumentClick)
      }
    }

    // 组件卸载时清理事件监听器
    onUnmounted(() => {
      document.removeEventListener('click', handleDocumentClick)
    })

    return {
      t,
      hasChildren,
      isExpanded,
      toggleExpand,
      canExpand,
      renderTreeRows,
      // Tooltip相关
      showTooltip,
      tooltipContent,
      tooltipStyle,
      showColumnTooltip,
      handleTooltipMouseLeave,
      toggleTooltipLock
    }
  }
})
</script>

<style scoped>
@import '@/styles/components/table.css';
</style>
