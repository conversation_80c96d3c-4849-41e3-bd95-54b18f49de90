import api from './index'

export default {
  // 代理概览
  getAgentOverview() {
    return api.get('/agent/overview')
  },


  // 代理利润
  getAgentProfit(params) {
    return api.get('/agent/profit', { params })
  },

  // 代理等级列表
  getAgentLevelList() {
    return api.get('/agent/level-list')
  },

  // 提现申请
  withdrawApply(data) {
    return api.post('/agent/withdraw-apply', data)
  },

  // 提现记录列表
  getWithdrawalRecords(params) {
    return api.get('/agent/withdrawal-records', { params })
  },

  /**
   * 取消提现申请
   * @param {number} withdrawalId 提现记录ID
   * @returns Promise
   */
  cancelWithdrawal(withdrawalId) {
    return api.post('/agent/withdraw-cancel', { withdrawal_id: withdrawalId })
  },

  // 确认提现
  withdrawConfirm(withdrawalId) {
    return api.post('/agent/withdraw-confirm', { withdrawal_id: withdrawalId })
  },

  // 代理佣金记录列表
  getCommissionRecords(params) {
    return api.get('/agent/commission-records', { params })
  },
}
