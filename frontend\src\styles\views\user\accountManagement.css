/* 账号管理页面样式 */
.user-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  margin-bottom: 8px;
}

.page-subtitle {
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.page-actions {
  margin-top: 16px;
}

.account-content {
  margin-top: 20px;
}


/* 状态标签样式 */
.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
}

.status-enabled {
  background-color: var(--success-color-light);
  color: var(--success-color);
}

.status-inactive {
  background-color: var(--warning-color-light);
  color: var(--warning-color);
}

.status-expired {
  background-color: var(--error-color-light);
  color: var(--error-color);
}

.status-disabled {
  background-color: var(--secondary-color-light);
  color: var(--secondary-color);
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
}

/* 正负值颜色 */
.positive-rate {
  color: var(--success-color);
}

.negative-rate {
  color: var(--error-color);
}

/* 总收益值颜色 */
.summary-value-positive {
  color: var(--success-color);
}

.summary-value-negative {
  color: var(--error-color);
}
