<template>
  <div class="home-page tech-accent">
    <div class="tech-grid"></div>

    <!-- 顶部横幅 -->
    <section class="banner tech-accent">
      <div class="tech-dots" style="top: 50px; right: 10%;"></div>
      <div class="tech-line" style="top: 30%; left: 0;"></div>
      <div class="container">
        <div class="banner-content">
          <h1 class="banner-title">启您所想，能您所期</h1>
          <p class="banner-subtitle">用算法重新定义加密资产价值，让每一份波动都能启赋理性收益</p>
          <div class="banner-buttons">
            <router-link to="/auth/register" class="btn btn-primary btn-lg pulse-animation">
              立即注册
            </router-link>
            <router-link to="/introduction" class="btn btn-outline-secondary btn-lg">
              了解更多
            </router-link>
          </div>
        </div>
        <div class="banner-image">
          <BannerImage />
        </div>
      </div>
    </section>

    <!-- 核心功能 -->
    <section class="features">
      <div class="container">
        <h2 class="section-title">核心功能</h2>
        <div class="section-subtitle">专业的服务平台，为您提供全方位的服务</div>

        <div class="row">
          <div class="col-12 col-md-6 col-lg-3">
            <div class="feature-card tech-glow">
              <div class="feature-icon">
                <i class="fa fa-shield-alt"></i>
              </div>
              <h3 class="feature-title">资金安全</h3>
              <p class="feature-description">多重安全措施保障您的资金安全</p>
              <ul class="feature-list">
                <li>多重安全验证</li>
                <li>资金隔离存储</li>
                <li>24*7安全监控</li>
              </ul>
              <router-link to="/help/fund-security" class="feature-link">
                了解更多 <i class="fa fa-arrow-right"></i>
              </router-link>
            </div>
          </div>
          <div class="col-12 col-md-6 col-lg-3">
            <div class="feature-card tech-glow">
              <div class="feature-icon">
                <i class="fa fa-chart-line"></i>
              </div>
              <h3 class="feature-title">稳定收益</h3>
              <p class="feature-description">专项训练的AI大模型帮助您获得稳定收益</p>
              <ul class="feature-list">
                <li>AI大模型助力</li>
                <li>实时收益统计</li>
                <li>风险控制机制</li>
              </ul>
              <router-link to="/help/profit-model" class="feature-link">
                了解更多 <i class="fa fa-arrow-right"></i>
              </router-link>
            </div>
          </div>
          <div class="col-12 col-md-6 col-lg-3">
            <div class="feature-card tech-glow">
              <div class="feature-icon">
                <i class="fa fa-cogs"></i>
              </div>
              <h3 class="feature-title">操作简便</h3>
              <p class="feature-description">简单易用的操作界面</p>
              <ul class="feature-list">
                <li>简单易用界面</li>
                <li>一键启动交易</li>
                <li>详细操作指南</li>
              </ul>
              <router-link to="/help/operation-process" class="feature-link">
                了解更多 <i class="fa fa-arrow-right"></i>
              </router-link>
            </div>
          </div>
          <div class="col-12 col-md-6 col-lg-3">
            <div class="feature-card tech-glow">
              <div class="feature-icon">
                <i class="fa fa-users"></i>
              </div>
              <h3 class="feature-title">代理体系</h3>
              <p class="feature-description">完善的多级代理体系</p>
              <ul class="feature-list">
                <li>多级代理体系</li>
                <li>透明佣金结算</li>
                <li>专业推广工具</li>
              </ul>
              <router-link to="/help/agent-rules" class="feature-link">
                了解更多 <i class="fa fa-arrow-right"></i>
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 资金费率走势图 -->
    <section class="funding-rate-chart-section">
      <div class="container">
        <h2 class="section-title">{{ $t('Home.fundingRateChart') }}</h2>
        <div class="section-subtitle">{{ $t('Home.fundingRateChartDesc') }}</div>

        <div class="chart-container">
          <h3 class="chart-title">{{ $t('Home.fundingRateTrend') }} ({{ $t('Home.totalFundingRate') }}: <span :class="totalFundingRateSum > 0 ? 'positive-rate' : 'negative-rate'">{{ totalFundingRateSum.toFixed(6) }}%</span>)</h3>
          <div v-if="chartData.length === 0" class="no-data-chart">
            {{ $t('Home.noChartData') }}
          </div>
          <canvas v-else ref="chartCanvas" style="width: 100%; height: 300px;"></canvas>

          <div class="chart-footer">
            <router-link to="/zjfl" class="view-more-link">
              {{ $t('Home.viewMoreFundingRate') }} <i class="fa fa-arrow-right"></i>
            </router-link>
          </div>
        </div>
      </div>
    </section>

    <!-- 开始使用 -->
    <section class="cta tech-accent">
      <div class="tech-dots" style="top: 30px; left: 5%;"></div>
      <div class="tech-line" style="top: 52%; right: 0;"></div>
      <div class="container">
        <div class="cta-content">
          <h2 class="cta-title">准备好开始赚取您的收益了吗？</h2>
          <p class="cta-description">立即注册，体验专业的服务，让您的投资更加高效、安全、稳定。</p>
          <div class="cta-buttons">
            <router-link to="/auth/register" class="btn btn-primary btn-lg pulse-animation">
              立即注册
            </router-link>
            <router-link to="/introduction" class="btn btn-outline-secondary btn-lg">
              了解更多
            </router-link>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import Chart from 'chart.js/auto'
import BannerImage from '@/components/home/<USER>'

export default {
  name: 'HomePage',
  components: {
    BannerImage
  },
  setup() {
    const { t } = useI18n()
    const store = useStore()
    const chartCanvas = ref(null)
    let chart = null

    // 加载状态
    const loading = ref(false)

    // 日期格式化工具函数
    const dateUtils = {
      // 格式化日期为 YYYY-MM-DD
      formatDate: (date) => {
        const d = new Date(date)
        const year = d.getFullYear()
        const month = String(d.getMonth() + 1).padStart(2, '0')
        const day = String(d.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      },

      // 格式化日期，只显示月和日 MM-DD
      formatShortDate: (dateString) => {
        const date = new Date(dateString)
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${month}-${day}`
      },

      // 格式化完整日期（用于tooltip）YYYY-MM-DD HH:MM
      formatFullDate: (dateString) => {
        const date = new Date(dateString)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        return `${year}-${month}-${day} ${hours}:${minutes}`
      },

      // 获取指定天数前的日期
      getDateBefore: (days) => {
        const date = new Date()
        date.setDate(date.getDate() - days)
        return dateUtils.formatDate(date)
      },

      // 设置默认日期范围
      getDefaultDateRange: () => {
        const end = new Date()
        const start = new Date()
        start.setDate(start.getDate() - 30) // 修改为近一个月（30天）

        return {
          start_date: dateUtils.formatDate(start),
          end_date: dateUtils.formatDate(end)
        }
      }
    }

    // 历史资金费率列表（图表数据）
    const chartData = computed(() => store.getters['fundingRate/chartData'])

    // 获取当前主题模式
    const isDarkMode = computed(() => store.getters.isDarkMode)

    // 获取历史资金费率（图表数据）
    const fetchChartData = async () => {
      try {
        loading.value = true

        // 获取默认日期范围
        const defaultDateRange = dateUtils.getDefaultDateRange()

        // 构建请求参数，不包含分页参数，默认使用symbol_id=1
        const requestParams = {
          symbol_id: 1, // 默认使用ID为1的合约
          start_date: defaultDateRange.start_date,
          end_date: defaultDateRange.end_date
        }

        console.log('Fetching chart data with params:', requestParams)

        // 使用store的dispatch方法获取数据，与FundingRateHistory.vue保持一致
        await store.dispatch('fundingRate/fetchChartData', requestParams)

        console.log('Chart data after dispatch:', store.getters['fundingRate/chartData'])

        // 更新图表 - chartData现在是计算属性，会自动更新
        updateChart()
      } catch (error) {
        console.error('获取历史资金费率图表数据出错:', error)
      } finally {
        loading.value = false
      }
    }

    // 创建图表配置
    const createChartConfig = (_, labels, fundingRates, originalDates) => {
      // 获取主题相关颜色
      const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim()
      const textColor = getComputedStyle(document.documentElement).getPropertyValue('--text-primary').trim()
      const gridColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color').trim()
      const successColor = getComputedStyle(document.documentElement).getPropertyValue('--success-color').trim()
      const errorColor = getComputedStyle(document.documentElement).getPropertyValue('--error-color').trim()

      console.log('Creating chart config with funding rates:', fundingRates)

      // 计算资金费率的最小值和最大值
      const minRate = fundingRates.length > 0 ? Math.min(...fundingRates) : -0.01
      const maxRate = fundingRates.length > 0 ? Math.max(...fundingRates) : 0.01

      console.log('Min rate:', minRate, 'Max rate:', maxRate)

      // 计算数据范围
      const dataRange = maxRate - minRate

      // 扩大y轴范围，上下各增加30%的空间
      const padding = Math.max(dataRange * 0.3, 0.005) // 确保至少有一些padding
      const yMin = minRate - padding
      const yMax = maxRate + padding

      console.log('Y axis range:', yMin, 'to', yMax)

      return {
        type: 'line',
        data: {
          labels: labels,
          datasets: [{
            label: t('Home.lastFundingRate'),
            data: fundingRates,
            borderColor: primaryColor,
            borderWidth: 2,
            pointRadius: 3,
            pointBackgroundColor: function(context) {
              const value = context.raw;
              return value >= 0 ? successColor : errorColor;
            }
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          interaction: {
            mode: 'index',
            intersect: false,
          },
          plugins: {
            tooltip: {
              callbacks: {
                title: function(tooltipItems) {
                  // 显示完整日期
                  const dataIndex = tooltipItems[0].dataIndex;
                  return dateUtils.formatFullDate(originalDates[dataIndex]);
                },
                label: function(context) {
                  return `${context.dataset.label}: ${context.raw.toFixed(6)}%`
                }
              }
            },
            legend: {
              display: false,
            }
          },
          scales: {
            x: {
              grid: {
                color: gridColor,
                borderColor: gridColor,
                tickColor: gridColor
              },
              ticks: {
                color: textColor,
                font: {
                  family: 'var(--font-family)',
                  size: 12
                }
              }
            },
            y: {
              grid: {
                color: function(context) {
                  // 突显y=0的x轴，根据主题模式设置不同颜色
                  if (context.tick.value === 0) {
                    return isDarkMode.value ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)';
                  }
                  return gridColor;
                },
                lineWidth: function(context) {
                  // 加粗y=0的线
                  if (context.tick.value === 0) {
                    return 2;
                  }
                  return 1;
                },
                borderColor: gridColor,
                tickColor: gridColor,
              },
              ticks: {
                color: textColor,
                font: {
                  family: 'var(--font-family)',
                  size: 12
                },
                callback: function(value) {
                  return value.toFixed(4) + '%'
                }
              },
              // 设置y轴的最小值和最大值，使折线图显示在中间位置
              min: yMin,
              max: yMax,
              // 确保y=0的线始终显示
              afterFit: function(scaleInstance) {
                // 确保y=0在可见范围内
                if (scaleInstance.min > 0 || scaleInstance.max < 0) {
                  if (scaleInstance.min > 0) {
                    scaleInstance.min = 0;
                  }
                  if (scaleInstance.max < 0) {
                    scaleInstance.max = 0;
                  }
                }
              }
            }
          }
        }
      }
    }

    // 更新图表
    const updateChart = () => {
      console.log('updateChart called, chartData:', chartData.value)
      console.log('chartCanvas:', chartCanvas.value)

      if (chartData.value.length === 0 || !chartCanvas.value) {
        console.warn('Cannot update chart: chartData is empty or chartCanvas is not available')
        return
      }

      // 销毁旧图表
      if (chart) {
        chart.destroy()
      }

      let data, originalDates, labels, fundingRates;

      try {
        // 准备图表数据
        data = chartData.value
          .slice()
          .sort((a, b) => new Date(a.calc_time) - new Date(b.calc_time))

        console.log('Sorted chart data:', data)

        // 保存原始日期数据用于tooltip
        originalDates = data.map(item => item.calc_time)
        console.log('Original dates:', originalDates)

        // 对labels进行处理，连续相同日期只显示一次，后续用空字符串替代
        const rawLabels = data.map(item => dateUtils.formatShortDate(item.calc_time))
        console.log('Raw labels:', rawLabels)

        labels = rawLabels.map((label, index) => {
          if (index === 0) return label
          return label === rawLabels[index - 1] ? '' : label
        })
        console.log('Processed labels:', labels)

        // 确保last_funding_rate是有效的数字
        fundingRates = data.map(item => {
          const rate = parseFloat(item.last_funding_rate)
          return isNaN(rate) ? 0 : rate * 100
        })
        console.log('Funding rates:', fundingRates)
      } catch (error) {
        console.error('Error processing chart data:', error)
        return
      }

      // 创建图表配置
      const chartConfig = createChartConfig(data, labels, fundingRates, originalDates)

      // 创建新图表
      const ctx = chartCanvas.value.getContext('2d')
      chart = new Chart(ctx, chartConfig)
    }

    // 计算资金费率总和
    const totalFundingRateSum = computed(() => {
      if (!chartData.value || chartData.value.length === 0) return 0
      return chartData.value.reduce((sum, item) => {
        const rate = parseFloat(item.last_funding_rate)
        return sum + (isNaN(rate) ? 0 : rate * 100)
      }, 0)
    })

    // 监听主题模式变化，当主题变化时重绘图表但不重新获取数据
    watch(isDarkMode, (newVal, oldVal) => {
      if (newVal !== oldVal && chartData.value.length > 0) {
        // 只重绘图表，不重新获取数据
        updateChart()
      }
    })

    // 初始化
    onMounted(() => {
      console.log('Component mounted, chartCanvas ref:', chartCanvas.value)

      // 添加一个小延迟，确保DOM已经渲染
      setTimeout(() => {
        console.log('After timeout, chartCanvas ref:', chartCanvas.value)
        // 获取图表数据
        fetchChartData()
      }, 100)
    })

    // 组件卸载时清理图表
    onUnmounted(() => {
      if (chart) {
        chart.destroy()
        chart = null
      }
    })

    return {
      chartCanvas,
      chartData,
      totalFundingRateSum
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/home.css';
</style>
