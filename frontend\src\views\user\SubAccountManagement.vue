<template>
  <div class="user-page">
    <div class="page-header">
      <h1 class="page-title">{{ $t('SubAccountManagement.title') }}</h1>
      <div class="page-subtitle">{{ $t('SubAccountManagement.subtitle') }}</div>
    </div>
    
    <div class="sub-account-content">
      <div class="summary-section">
        <div class="summary-card">
          <div class="summary-title">{{ $t('SubAccountManagement.statistics') }}</div>
          <div class="summary-content">
            <div class="summary-item">
              <div class="summary-label">{{ $t('SubAccountManagement.subAccountCount') }}</div>
              <div class="summary-value">{{ subAccountStats.subAccountCount }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">{{ $t('SubAccountManagement.totalAssets') }}</div>
              <div class="summary-value">{{ subAccountStats.totalAssets }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">{{ $t('SubAccountManagement.totalProfit') }}</div>
              <div class="summary-value" :class="{ 'summary-value-positive': Number(subAccountStats.totalProfit) > 0, 'summary-value-negative': Number(subAccountStats.totalProfit) <= 0 }">{{ subAccountStats.totalProfit }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">{{ $t('SubAccountManagement.uncollectedFunds') }}</div>
              <div class="summary-value">{{ subAccountStats.uncollectedFunds }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <FilterComponent 
        :filters="filterConfig" 
        v-model="filters" 
        :initialValues="filters"
        @search="applyFilters" 
        @reset="resetFilters" 
      />
      
      <Table 
        :columns="tableColumns" 
        :data="subAccounts" 
        :no-data-text="$t('SubAccountManagement.noData')"
        class="desktop-only"
      >
        <!-- 父账号列 -->
        <template #parentAccount="{ item }">
          {{ getAccountName(item.accountId) }}
        </template>
        
        <!-- 子账号列 -->
        <template #email="{ item }">
          {{ item.email }}
        </template>
        
        <!-- 账号资产列 -->
        <template #wallet_balance="{ item }">
          {{ item.wallet_balance }}
        </template>
        
        <!-- 总收益列 -->
        <template #sum_income="{ item }">
          {{ item.sum_income }}
        </template>
        
        <!-- 未收集资金列 -->
        <template #collect_wallet="{ item }">
          {{ item.collect_wallet }}
        </template>
        
        <!-- 任务状态列 -->
        <template #task_status="{ item }">
          <span class="task-status" :class="getTaskStatusClass(item.task_status)">
            <span class="task-status-dot" :class="getTaskStatusDotClass(item.task_status)"></span>
            {{ getTaskStatusText(item.task_status) }}
          </span>
        </template>
        
        <!-- 状态列 -->
        <template #status="{ item }">
          <span class="status-badge" :class="getStatusClass(item.status)">
            {{ getStatusText(item.status) }}
          </span>
        </template>
        
        
        <!-- 操作列 -->
        <template #operation="{ item }">
          <div class="action-buttons">
            <button class="btn btn-sm btn-primary" @click="editApiKey(item)">
              <i class="fa fa-key"></i> {{ item.hasApiKey ? $t('SubAccountManagement.editApiKey') : $t('SubAccountManagement.addApiKey') }}
            </button>
            <button v-if="item.task_status != 0" class="btn btn-sm btn-info" @click="viewProfitDetails(item)">
              <i class="fa fa-chart-line"></i> {{ $t('SubAccountManagement.profitDetails') }}
            </button>
          </div>
        </template>
      </Table>
      
      <Cards 
        :columns="tableColumns" 
        :data="subAccounts" 
        :loading="isLoading" 
        :no-data-text="$t('SubAccountManagement.noData')"
        :loading-text="$t('SubAccountManagement.loading')"
      >
        <!-- 标题插槽 -->
        <template #title="{ item }">
          {{ item.email }}
        </template>
        
        <!-- 任务状态插槽 -->
        <template #task_status="{ item }">
          <span class="task-status" :class="getTaskStatusClass(item.task_status)">
            <span class="task-status-dot" :class="getTaskStatusDotClass(item.task_status)"></span>
            {{ getTaskStatusText(item.task_status) }}
          </span>
        </template>
        
        <!-- 状态插槽 -->
        <template #status="{ item }">
          <span class="status-badge" :class="getStatusClass(item.status)">
            {{ getStatusText(item.status) }}
          </span>
        </template>
        
        <!-- 父账号列 -->
        <template #parentAccount="{ item }">
          {{ getAccountName(item.accountId) }}
        </template>
        
        <!-- 账号资产列 -->
        <template #wallet_balance="{ item }">
          {{ item.wallet_balance }}
        </template>
        
        <!-- 总收益列 -->
        <template #sum_income="{ item }">
          {{ item.sum_income }}
        </template>
        
        <!-- 未收集资金列 -->
        <template #collect_wallet="{ item }">
          {{ item.collect_wallet }}
        </template>
        
        <!-- 操作列 -->
        <template #operation="{ item }">
          <button class="btn btn-sm btn-primary" @click="editApiKey(item)">
            <i class="fa fa-key"></i> {{ item.hasApiKey ? $t('SubAccountManagement.editApiKey') : $t('SubAccountManagement.addApiKey') }}
          </button>
          <button v-if="item.task_status != 0" class="btn btn-sm btn-info" @click="viewProfitDetails(item)">
            <i class="fa fa-chart-line"></i> {{ $t('SubAccountManagement.profitDetails') }}
          </button>
        </template>
      </Cards>
      
      <Pagination
        v-if="subAccounts.length > 0"
        :total="totalSubAccounts"
        :page="currentPage"
        :limit="limit"
        @change="handlePageChange"
      />
    </div>
    
    <!-- API KEY弹窗 -->
    <Modal 
      v-model="showApiKeyModal" 
      :title="editingSubAccount.hasApiKey ? $t('SubAccountManagement.editApiKey') : $t('SubAccountManagement.addApiKey')"
      @confirm="saveApiKey"
    >
      <form @submit.prevent="saveApiKey">
        <div class="form-group">
          <label for="apikey">{{ $t('SubAccountManagement.apikey') }}</label>
          <input type="text" id="apikey" v-model="apikeyForm.apikey" class="form-control" required />
        </div>
        <div class="form-group">
          <label for="secretkey">{{ $t('SubAccountManagement.secretkey') }}</label>
          <input type="text" id="secretkey" v-model="apikeyForm.secretkey" class="form-control" required />
        </div>
        <div class="form-group">
          <div class="form-check">
            <input type="checkbox" id="agreeTerms" v-model="apikeyForm.agreeTerms" class="form-check-input" required />
            <label for="agreeTerms" class="form-check-label">
              {{ $t('SubAccountManagement.agreeTerms') }}<a href="#" @click.prevent="showTerms">{{ $t('SubAccountManagement.apiTerms') }}</a>
            </label>
          </div>
        </div>
      </form>
    </Modal>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import Pagination from '@/components/common/Pagination.vue'
import FilterComponent from '@/components/common/FilterComponent.vue'
import Table from '@/components/common/Table.vue'
import Cards from '@/components/common/Cards.vue'
import Modal from '@/components/common/Modal.vue'
import { focusInput, showError, showSuccess } from '@/utils/utils'

export default {
  name: 'SubAccountManagementPage',
  components: {
    Pagination,
    FilterComponent,
    Table,
    Cards,
    Modal
  },
  setup() {
    const store = useStore()
    const { t } = useI18n()
    const router = useRouter()
    
    // 表格列定义
    const tableColumns = computed(() => [
      { key: 'parentAccount', title: t('SubAccountManagement.parentAccount'), align:'left' },
      { key: 'email', title: t('SubAccountManagement.subAccount'), align:'left' },
      { key: 'wallet_balance', title: t('SubAccountManagement.accountAssets'), align:'right' },
      { key: 'sum_income', title: t('SubAccountManagement.totalProfit'), align:'right' },
      { key: 'collect_wallet', title: t('SubAccountManagement.uncollectedFunds'), align:'right' },
      { key: 'task_status', title: t('SubAccountManagement.task_status') },
      { key: 'status', title: t('SubAccountManagement.status') },
      { key: 'operation', title: t('SubAccountManagement.operation') }
    ])
    
    // 加载状态
    const loading = ref(false)
    const error = ref(null)
    
    // 从 store 获取分页状态
    const currentPage = computed(() => store.getters['pagination/pagination'].current)
    const limit = computed(() => store.getters['pagination/pagination'].limit)
    const totalSubAccounts = computed(() => store.getters['pagination/pagination'].total)
    
    // 过滤参数
    const filters = reactive({
      accountId: '',
      status: ''
    })
    
    // 筛选配置
    const filterConfig = computed(() => [
      {
        type: 'select',
        id: 'accountSelect',
        label: t('SubAccountManagement.selectAccount'),
        key: 'accountId',
        placeholder: t('SubAccountManagement.all'),
        options: accounts.value.map(account => ({
          value: account.id,
          text: account.account
        }))
      }
    ])
    
    // 从 store 获取账号和子账号数据
    const accounts = computed(() => store.getters['account/accounts'])
    const subAccounts = computed(() => store.getters['account/subAccounts'])
    const isLoading = computed(() => store.getters['account/isLoading'])
    const subAccountStats = computed(() => store.state.account.subAccountStats)
    
    // 弹窗显示状态
    const showApiKeyModal = ref(false)
    
    // 表单数据
    const apikeyForm = reactive({
      apikey: '',
      secretkey: '',
      agreeTerms: true
    })
    
    // 编辑的子账号
    const editingSubAccount = reactive({
      id: null,
      hasApiKey: false
    })
    
    // 加载子账号数据
    const loadSubAccounts = (page = currentPage.value, size = limit.value) => {
      // 构建请求参数，确保分页参数正确
      const params = {
        page: page,
        limit: size
      }
      // 只添加有值的筛选条件
      if (filters.accountId) {
        params.accountId = filters.accountId
      }
      if (filters.status) {
        params.status = filters.status
      }
      store.dispatch('account/fetchSubAccounts', params)
    }
    
    // 重置筛选
    const resetFilters = () => {
      filters.accountId = ''
      filters.status = ''
      
      // 重新加载子账号数据
      loadSubAccounts()
    }
    
    // 加载账号数据
    const loadAccounts = async () => {
      try {
        // 传递一个大的 limit 参数，确保获取所有账号数据用于下拉列表
        await store.dispatch('account/fetchAccounts', { limit: 9999 })
      } catch (err) {
        console.error('加载账号数据失败:', err)
      }
    }
    
    
    // 获取账号名称
    const getAccountName = (accountId) => {
      const account = accounts.value.find(a => a.id === accountId)
      return account ? account.account : '-'
    }
    
    // 获取状态样式类
    const getStatusClass = (status) => {
      // 数据库中状态: 0：未启用 1：已启用 2：已失效 3：已停用
      switch (parseInt(status)) {
        case 0:
          return 'status-inactive'
        case 1:
          return 'status-enabled'
        case 2:
          return 'status-expired'
        case 3:
          return 'status-disabled'
        default:
          return ''
      }
    }
    
    // 获取状态文本
    const getStatusText = (status) => {
      // 数据库中状态: 0：未启用 1：已启用 2：已失效 3：已停用
      switch (parseInt(status)) {
        case 0:
          return t('SubAccountManagement.status_inactive')
        case 1:
          return t('SubAccountManagement.status_enabled')
        case 2:
          return t('SubAccountManagement.status_expired')
        case 3:
          return t('SubAccountManagement.status_disabled')
        default:
          return '-'
      }
    }
    
    // 获取任务状态样式类
    const getTaskStatusClass = (taskStatus) => {
      // 任务状态: 0：未运行 1：已完成 其它：执行中
      switch (parseInt(taskStatus)) {
        case 0:
          return 'task-status-not-running'
        case 1:
          return 'task-status-completed'
        default:
          return 'task-status-running'
      }
    }
    
    // 获取任务状态点样式类
    const getTaskStatusDotClass = (taskStatus) => {
      // 任务状态: 0：未运行 1：已完成 其它：执行中
      switch (parseInt(taskStatus)) {
        case 0:
          return 'task-status-dot-not-running'
        case 1:
          return 'task-status-dot-completed'
        default:
          return 'task-status-dot-running'
      }
    }
    
    // 获取任务状态文本
    const getTaskStatusText = (taskStatus) => {
      // 任务状态: 0：未运行 1：已完成 其它：执行中
      switch (parseInt(taskStatus)) {
        case 0:
          return t('SubAccountManagement.taskStatus_notRunning')
        case 1:
          return t('SubAccountManagement.taskStatus_completed')
        default:
          return t('SubAccountManagement.taskStatus_running')
      }
    }

    // 处理分页变化
    const handlePageChange = ({ page, limit: size }) => {
      if (page && size) {
        loadSubAccounts(page, size)
      }
    }
    
    // 编辑API KEY
    const editApiKey = async (subAccount) => {
      editingSubAccount.id = subAccount.id
      editingSubAccount.hasApiKey = subAccount.hasApiKey
      apikeyForm.apikey = subAccount.apikey
      apikeyForm.secretkey = ''
      
      apikeyForm.agreeTerms = true
      showApiKeyModal.value = true
    }
    
    // 保存API KEY
    const saveApiKey = async () => {
      if (!apikeyForm.apikey) {
        showError(t,t('SubAccountManagement.apikeyError'))
        focusInput('apikey')
        return
      }
      if (!apikeyForm.secretkey) {
        showError(t,t('SubAccountManagement.secretkeyError'))
        focusInput('secretkey')
        return
      }
      if (!apikeyForm.agreeTerms) {
        showError(t,t('SubAccountManagement.agreeTermsError'))
        return
      }
      
      try {
        loading.value = true
        
        const apiData = {
          apikey: apikeyForm.apikey,
          secretkey: apikeyForm.secretkey
        }
        
        await store.dispatch('account/updateSubAccountApi', {
          subAccountId: editingSubAccount.id,
          apiData
        })
        
        showApiKeyModal.value = false
        
        showSuccess(t,editingSubAccount.hasApiKey 
            ? t('SubAccountManagement.apikeyUpdated') 
            : t('SubAccountManagement.apikeyAdded')
        )
        
        // 重新加载子账号列表
        loadSubAccounts()
      } catch (err) {
        showError(t,err.message || t('SubAccountManagement.saveApiKeyError'))
      } finally {
        loading.value = false
      }
    }
    
    // 查看盈利明细
    const viewProfitDetails = (subAccount) => {
      router.push({
        name: 'ProfitDetails',
        query: {
          accountId: subAccount.accountId,
          subAccountId: subAccount.id
        }
      })
    }
    
    // 显示条款
    const showTerms = () => {
      store.dispatch('showNotification', {
        type: 'info',
        title: t('SubAccountManagement.apiTerms'),
        message: t('SubAccountManagement.apiTermsContent', '使用API密钥需遵守相关条款和条件，确保API密钥的安全性。')
      })
    }
    
    // 获取子账号详情
    const getSubAccount = async (subAccountId) => {
      try {
        return await store.dispatch('account/getSubAccount', subAccountId)
      } catch (err) {
        showError(t,err.message || t('SubAccountManagement.getSubAccountError'))
        return null
      }
    }
    
    onMounted(() => {
      // 重置分页状态
      store.commit('pagination/SET_PAGINATION', {
        current: 1,
        limit: limit.value
      }, { root: true })
      
      // 加载账号和子账号数据
      loadAccounts()
      loadSubAccounts(1, limit.value)
    })
    
    // 处理筛选搜索
    const applyFilters = (filterParams) => {
      // 更新本地筛选条件
      if (filterParams.accountId !== undefined) {
        filters.accountId = filterParams.accountId
      }
      
      if (filterParams.status !== undefined) {
        filters.status = filterParams.status
      }
      
      // 调用加载函数，使用当前页码
      loadSubAccounts(currentPage.value, limit.value)
    }
    
    return {
      tableColumns,
      accounts,
      subAccounts,
      subAccountStats,
      filters,
      filterConfig,
      currentPage,
      limit,
      totalSubAccounts,
      loading,
      error,
      isLoading,
      showApiKeyModal,
      apikeyForm,
      editingSubAccount,
      getAccountName,
      getStatusClass,
      getStatusText,
      getTaskStatusClass,
      getTaskStatusDotClass,
      getTaskStatusText,
      handlePageChange,
      editApiKey,
      saveApiKey,
      viewProfitDetails,
      showTerms,
      getSubAccount,
      loadSubAccounts,
      resetFilters,
      applyFilters
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/user/common.css';
@import '@/styles/views/user/subAccountManagement.css';
@import '@/styles/components/taskStatus.css';
@import '@/styles/components/summary.css';
</style>
