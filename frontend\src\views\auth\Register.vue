<template>
  <div class="register-page">
    <div class="register-container">
      <div class="register-header">
        <h2 class="register-title">{{ $t('Register.title') }}</h2>
        <p class="register-subtitle">{{ $t('Register.subtitle') }}</p>
      </div>
      
      <form class="register-form" @submit.prevent="handleRegister">
        <div class="form-group">
          <label for="username">{{ $t('Register.username') }}</label>
          <div class="input-wrapper">
            <i class="fa fa-user"></i>
            <input 
              type="text" 
              id="username" 
              v-model="form.username" 
              :placeholder="$t('Register.usernamePlaceholder')"
              required
            />
          </div>
          <div class="error-message" v-if="errors.username">{{ errors.username }}</div>
        </div>
        
        <!-- <div class="form-group">
          <label for="email">{{ $t('Register.email') }}</label>
          <div class="input-wrapper">
            <i class="fa fa-envelope"></i>
            <input 
              type="email" 
              id="email" 
              v-model="form.email" 
              :placeholder="$t('Register.emailPlaceholder')"
              required
            />
          </div>
          <div class="error-message" v-if="errors.email">{{ errors.email }}</div>
        </div> -->
        
        <div class="form-group">
          <label for="password">{{ $t('Register.password') }}</label>
          <div class="input-wrapper">
            <i class="fa fa-lock"></i>
            <input 
              :type="showPassword ? 'text' : 'password'" 
              id="password" 
              v-model="form.password" 
              :placeholder="$t('Register.passwordPlaceholder')"
              required
            />
            <button 
              type="button" 
              class="toggle-password" 
              @click="showPassword = !showPassword"
              tabindex="-1"
            >
              <i :class="showPassword ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
            </button>
          </div>
          <div class="error-message" v-if="errors.password">{{ errors.password }}</div>
        </div>
        
        <div class="form-group">
          <label for="confirmPassword">{{ $t('Register.confirmPassword') }}</label>
          <div class="input-wrapper">
            <i class="fa fa-lock"></i>
            <input 
              :type="showConfirmPassword ? 'text' : 'password'" 
              id="confirmPassword" 
              v-model="form.confirmPassword" 
              :placeholder="$t('Register.confirmPasswordPlaceholder')"
              required
            />
            <button 
              type="button" 
              class="toggle-password" 
              @click="showConfirmPassword = !showConfirmPassword"
              tabindex="-1"
            >
              <i :class="showConfirmPassword ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
            </button>
          </div>
          <div class="error-message" v-if="errors.confirmPassword">{{ errors.confirmPassword }}</div>
        </div>
        
        <div class="form-group">
          <label for="invitation">{{ $t('Register.invitation') }}</label>
          <div class="input-wrapper">
            <i class="fa fa-ticket-alt"></i>
            <input 
              type="text" 
              id="invitation" 
              v-model="form.invitation" 
              :placeholder="$t('Register.invitationPlaceholder')"
              required
              ref="invitationInput"
              :disabled="invitationDisabled"
            />
          </div>
          <div class="error-message" v-if="errors.invitation">{{ errors.invitation }}</div>
        </div>
        
        <div class="form-group captcha-group">
          <label for="captcha">{{ $t('Register.captcha') }}</label>
          <div class="captcha-wrapper">
            <div class="input-wrapper">
              <i class="fa fa-shield-alt"></i>
              <input 
                type="text" 
                id="captcha" 
                v-model="form.captcha" 
                :placeholder="$t('Register.captchaPlaceholder')"
                required
              />
            </div>
            <div class="captcha-image" @click="refreshCaptcha">
              <div v-if="!captchaUrl" style="background-color: white; width: 100%; height: 100%;"></div>
              <img v-else :src="captchaUrl" alt="Captcha" />
            </div>
          </div>
          <div class="error-message" v-if="errors.captcha">{{ errors.captcha }}</div>
        </div>
        
        <div class="form-group terms-group">
          <div class="terms-checkbox">
            <input type="checkbox" id="terms" v-model="form.terms" required />
            <label for="terms">
              {{ $t('Register.agreeTerms') }}
              <a href="#" @click.prevent="showTerms">{{ $t('Register.termsLink') }}</a>
            </label>
          </div>
          <div class="error-message" v-if="errors.terms">{{ errors.terms }}</div>
        </div>
        
        <button type="submit" class="btn-register" :disabled="loading">
          <span v-if="!loading">{{ $t('Register.registerButton') }}</span>
          <span v-else class="loading-spinner"></span>
        </button>
        
        <div class="register-footer">
          <p>{{ $t('Register.haveAccount') }} <router-link to="/auth/login">{{ $t('Register.loginNow') }}</router-link></p>
        </div>
      </form>
    </div>
    
    <!-- 服务条款弹窗 -->
    <div class="modal" v-if="termsModalVisible">
      <div class="modal-backdrop" @click="termsModalVisible = false"></div>
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{{ $t('Register.termsTitle') }}</h5>
            <button type="button" class="btn-close" @click="termsModalVisible = false">
              <i class="fa fa-times"></i>
            </button>
          </div>
          <div class="modal-body">
            <div class="terms-content">
              <h3>1. 接受条款</h3>
              <p>欢迎使用我们平台。通过访问或使用我们的服务，您同意受这些条款的约束。如果您不同意这些条款，请不要使用我们的服务。</p>
              
              <h3>2. 服务描述</h3>
              <p>我们提供一个量化交易平台，允许用户创建、测试和部署交易策略。我们不提供投资建议，也不保证任何交易结果。</p>
              
              <h3>3. 用户账户</h3>
              <p>您需要创建一个账户才能使用我们的某些服务。您负责维护您的账户安全，并对您账户下发生的所有活动负责。</p>
              
              <h3>4. 用户行为</h3>
              <p>您同意不会使用我们的服务进行任何非法或未经授权的目的。您不得干扰或破坏我们的服务或服务器。</p>
              
              <h3>5. 知识产权</h3>
              <p>我们的服务和内容受版权、商标和其他法律保护。您不得未经授权使用我们的知识产权。</p>
              
              <h3>6. 免责声明</h3>
              <p>我们的服务按"原样"提供，不提供任何明示或暗示的保证。我们不对任何交易损失负责。</p>
              
              <h3>7. 责任限制</h3>
              <p>在法律允许的最大范围内，我们对您使用或无法使用我们的服务而导致的任何损失不承担责任。</p>
              
              <h3>8. 修改条款</h3>
              <p>我们可能会不时修改这些条款。继续使用我们的服务表示您接受修改后的条款。</p>
              
              <h3>9. 终止</h3>
              <p>我们可能会因任何原因终止或暂停您对我们服务的访问，恕不另行通知。</p>
              
              <h3>10. 适用法律</h3>
              <p>这些条款受中国法律管辖，不考虑法律冲突原则。</p>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn-secondary" @click="termsModalVisible = false">
              关闭
            </button>
            <button type="button" class="btn-primary" @click="acceptTerms">
              接受
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'

export default {
  name: 'RegisterPage',
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()
    const { t } = useI18n()
    
    const form = reactive({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      captcha: '',
      invitation: '',
      terms: false
    })
    
    const errors = reactive({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      captcha: '',
      invitation: '',
      terms: ''
    })
    
    const loading = ref(false)
    const showPassword = ref(false)
    const showConfirmPassword = ref(false)
    const captchaUrl = ref('')
    const termsModalVisible = ref(false)
    const invitationDisabled = ref(false)
    
    const refreshCaptcha = async () => {
      try {
        // 使用store的getCaptcha action获取验证码
        const result = await store.dispatch('user/getCaptcha')
        
        // 处理API返回的数据格式
        if (result.captchaImage) {
          captchaUrl.value = result.captchaImage
        } else if (typeof result === 'string') {
          // 实际API返回的base64字符串
          captchaUrl.value = result
        }
      } catch (error) {
        store.dispatch('showNotification', {
          type: 'error',
          title: t('common.error'),
          message: '获取验证码失败'
        })
      }
    }
    
    const validateForm = () => {
      let isValid = true
      
      // 重置错误信息
      errors.username = ''
      // errors.email = ''
      errors.password = ''
      errors.confirmPassword = ''
      errors.captcha = ''
      errors.invitation = ''
      errors.terms = ''
      
      // 验证用户名
      if (!form.username) {
        errors.username = t('Register.usernameRequired')
        isValid = false
      } else if (form.username.length < 3) {
        errors.username = t('Register.usernameLength')
        isValid = false
      }
      
      // // 验证邮箱
      // if (!form.email) {
      //   errors.email = t('Register.emailRequired')
      //   isValid = false
      // } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
      //   errors.email = t('Register.emailInvalid')
      //   isValid = false
      // }
      
      // 验证密码
      if (!form.password) {
        errors.password = t('Register.passwordRequired')
        isValid = false
      } else if (form.password.length < 6) {
        errors.password = t('Register.passwordLength')
        isValid = false
      }
      
      // 验证确认密码
      if (!form.confirmPassword) {
        errors.confirmPassword = t('Register.confirmPasswordRequired')
        isValid = false
      } else if (form.password !== form.confirmPassword) {
        errors.confirmPassword = t('Register.passwordsNotMatch')
        isValid = false
      }
      
      // 验证验证码
      if (!form.captcha) {
        errors.captcha = t('Register.captchaRequired')
        isValid = false
      }
      
      // 验证邀请码
      if (!form.invitation) {
        errors.invitation = t('Register.invitationRequired')
        isValid = false
      }
      
      // 验证服务条款
      if (!form.terms) {
        errors.terms = t('Register.termsRequired')
        isValid = false
      }
      
      return isValid
    }
    
    // 邀请码输入框引用
    const invitationInput = ref(null)
    
    const handleRegister = async () => {
      if (!validateForm()) {
        return
      }
      
      loading.value = true
      
      try {
        // 调用store的register action
        const result = await store.dispatch('user/register', {
          username: form.username,
          // email: form.email,
          password: form.password,
          captcha: form.captcha,
          invitation: form.invitation
        })
        
        // 检查接口返回的数据
        if (!result) {
          throw new Error(t('Register.registerFailed'))
        }
        
        // 检查是否包含错误码
        if (result.code && result.code !== 200) {
          throw new Error(result.message || t('Register.registerFailed'))
        }
        
        // 注册成功，显示成功提示
        store.dispatch('showNotification', {
          type: 'success',
          title: t('common.success'),
          message: t('Register.registerSuccess')
        })
        router.push('/auth/login')
      } catch (error) {
        // 刷新验证码
        refreshCaptcha()
        // 注册失败，显示错误信息
        console.error('注册失败:', error)
        
        // 获取错误消息
        const errorMsg = error.message || '';
        
        // 根据错误类型显示不同的错误信息
        if (errorMsg.includes('验证码') || errorMsg.includes('captcha')) {
          errors.captcha = t('Register.captchaError')
          // 刷新验证码
          refreshCaptcha()
        } else if (errorMsg.includes('用户名') || errorMsg.includes('username')) {
          errors.username = errorMsg
        } else if (errorMsg.includes('邮箱') || errorMsg.includes('email')) {
          errors.email = errorMsg
        } else if (errorMsg.includes('密码') || errorMsg.includes('password')) {
          errors.password = errorMsg
        } else if (errorMsg.includes('邀请码') || errorMsg.includes('invitation')) {
          errors.invitation = t('Register.invitationInvalid')
          // 聚焦到邀请码输入框
          nextTick(() => {
            invitationInput.value && invitationInput.value.focus()
          })
        } else {
          // 通用错误信息
          errors.username = t('Register.registerFailed')
        }
        
        store.dispatch('showNotification', {
          type: 'error',
          title: t('common.error'),
          message: errorMsg || t('Register.registerFailed')
        })
      } finally {
        loading.value = false
      }
    }
    
    const showTerms = () => {
      termsModalVisible.value = true
    }
    
    const acceptTerms = () => {
      form.terms = true
      termsModalVisible.value = false
    }
    
    onMounted(() => {
      refreshCaptcha()
      
      // 检查 URL 参数中是否有 invitation，如果有则自动填充并禁止编辑
      if (route.query.invitation) {
        form.invitation = route.query.invitation
        invitationDisabled.value = true
      }
    })
    
    return {
      form,
      errors,
      loading,
      showPassword,
      showConfirmPassword,
      captchaUrl,
      termsModalVisible,
      invitationInput,
      invitationDisabled,
      refreshCaptcha,
      handleRegister,
      showTerms,
      acceptTerms
    }
  }
}
</script>

<style scoped>
@import '@/styles/views/auth/register.css';
</style>
