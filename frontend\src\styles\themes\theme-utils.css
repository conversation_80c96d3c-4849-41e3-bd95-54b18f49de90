/* 主题切换工具类 */

/* 主题切换过渡效果 */
.theme-transition {
  transition: background-color var(--transition-normal),
              color var(--transition-normal),
              border-color var(--transition-normal),
              box-shadow var(--transition-normal);
}

/* 主题切换按钮动画 */
@keyframes theme-toggle-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.theme-toggle-animation {
  animation: theme-toggle-rotate 0.5s ease;
}

/* 主题切换时的过渡效果 */
body, 
.navbar, 
.sidebar, 
.card, 
.dropdown-menu, 
.modal, 
.form-control, 
.btn, 
.table, 
.footer {
  transition: background-color 0.3s ease, 
              color 0.3s ease, 
              border-color 0.3s ease, 
              box-shadow 0.3s ease;
}

/* 暗色模式下的卡片样式 */
.dark-mode .card {
  background-color: var(--bg-card);
  border-color: var(--border-color);
}

/* 暗色模式下的表单样式 */
.dark-mode .form-control {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.dark-mode .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(240, 185, 11, 0.25);
}

.dark-mode .form-control::placeholder {
  color: var(--text-tertiary);
}

/* 暗色模式下的表格样式 */
.dark-mode .table {
  color: var(--text-primary);
}

.dark-mode .table th,
.dark-mode .table td {
  border-color: var(--border-color);
}

.dark-mode .table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}

.dark-mode .table-hover tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.075);
}

/* 暗色模式下的模态框样式 */
.dark-mode .modal-content {
  background-color: var(--bg-modal);
  border-color: var(--border-color);
}

.dark-mode .modal-header,
.dark-mode .modal-footer {
  border-color: var(--border-color);
}

/* 暗色模式下的下拉菜单样式 */
.dark-mode .dropdown-menu {
  background-color: var(--bg-card);
  border-color: var(--border-color);
}

.dark-mode .dropdown-item {
  color: var(--text-primary);
}

.dark-mode .dropdown-item:hover,
.dark-mode .dropdown-item:focus {
  background-color: var(--bg-secondary);
}

.dark-mode .dropdown-divider {
  border-color: var(--border-color);
}

/* 暗色模式下的导航栏样式 */
.dark-mode .navbar {
  background-color: var(--bg-primary);
  border-color: var(--border-color);
}

/* 暗色模式下的侧边栏样式 */
.dark-mode .sidebar {
  background-color: var(--bg-primary);
  border-color: var(--border-color);
}

/* 暗色模式下的按钮样式 */
.dark-mode .btn-light {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.dark-mode .btn-light:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color-dark);
}

/* 暗色模式下的输入组样式 */
.dark-mode .input-group-text {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

/* 暗色模式下的列表组样式 */
.dark-mode .list-group-item {
  background-color: var(--bg-card);
  border-color: var(--border-color);
  color: var(--text-primary);
}

/* 暗色模式下的面包屑样式 */
.dark-mode .breadcrumb {
  background-color: var(--bg-secondary);
}

.dark-mode .breadcrumb-item + .breadcrumb-item::before {
  color: var(--text-tertiary);
}

/* 暗色模式下的分页样式 */
.dark-mode .page-link {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.dark-mode .page-link:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--primary-color);
}

.dark-mode .page-item.active .page-link {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--text-light);
}

.dark-mode .page-item.disabled .page-link {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-tertiary);
}
